--CREATE BUFFERPOOL ETCHBP4K SIZE AUTOMATIC PAGESIZE 4096 ;

--CREATE LARGE TABLESPACE ETCHTABSP  PAGESIZE 4096 MANAGED BY AUTOMATIC STORAGE BUFFERPOOL ETCHBP4K;

DROP TABLE IF EXISTS "ETCHAP1 "."BANKDATA";
CREATE TABLE "ETCHAP1 "."BANKDATA"  (
		  "BID" CHAR(4) NOT NULL , 
		  "CNAME" CHAR(40) , 
		  "CHARGEID" VARCHAR(4) , 
		  "AREA" CHAR(4) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."BANKDATA"

ALTER TABLE "ETCHAP1 "."BANKDATA" 
	ADD CONSTRAINT "P_BANKDATA" PRIMARY KEY
		("BID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."BANKDATA"

CREATE UNIQUE INDEX "ETCHAP1 "."XBANKDATA01" ON "ETCHAP1 "."BANKDATA" 
		("BID" ASC)
		INCLUDE ("CNAME" )
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."BANKDATA"

CREATE INDEX "ETCHAP1 "."XBANKDATA02" ON "ETCHAP1 "."BANKDATA" 
		("AREA" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;


DROP TABLE IF EXISTS "ETCHAP1 "."SYSPARAM";
CREATE TABLE "ETCHAP1 "."SYSPARAM"  (
		  "PARAM" VARCHAR(30) NOT NULL , 
		  "PARAMVALUE" VARCHAR(1000) NOT NULL , 
		  "PARAMDESC" VARCHAR(300) , 
		  "MODIFYBY" VARCHAR(10) , 
		  "MODIFYTIME" TIMESTAMP )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SYSPARAM"

ALTER TABLE "ETCHAP1 "."SYSPARAM" 
	ADD CONSTRAINT "P_SYSPARAM" PRIMARY KEY
		("PARAM");
		
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."ATOM"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."ATOM";
CREATE TABLE "ETCHAP1 "."ATOM"  (
		  "ATOMID" CHAR(3) NOT NULL , 
		  "COLNAME" VARCHAR(16) NOT NULL , 
		  "DATATYPE" CHAR(1) , 
		  "DATALEN" INTEGER , 
		  "CNAME" VARCHAR(40) , 
		  "COLSEQ" INTEGER )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."ATOM"

ALTER TABLE "ETCHAP1 "."ATOM" 
	ADD CONSTRAINT "P_ATOM" PRIMARY KEY
		("ATOMID",
		 "COLNAME");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."ATOMPROFILE"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."ATOMPROFILE";
CREATE TABLE "ETCHAP1 "."ATOMPROFILE"  (
		  "ATOMID" CHAR(3) NOT NULL , 
		  "PRICE" DECIMAL(6,2) , 
		  "CNAME" VARCHAR(40) , 
		  "TBNAME" VARCHAR(40) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."ATOMPROFILE"

ALTER TABLE "ETCHAP1 "."ATOMPROFILE" 
	ADD CONSTRAINT "P_ATOMPROFILE" PRIMARY KEY
		("ATOMID");
		
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TXID"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TXID";
CREATE TABLE "ETCHAP1 "."TXID"  (
		  "TXID" CHAR(4) NOT NULL , 
		  "ATOMID" CHAR(3) NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TXID"

ALTER TABLE "ETCHAP1 "."TXID" 
	ADD CONSTRAINT "P_TXID" PRIMARY KEY
		("TXID",
		 "ATOMID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TXIDPROFILE"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TXIDPROFILE";
CREATE TABLE "ETCHAP1 "."TXIDPROFILE"  (
		  "TXID" CHAR(4) NOT NULL , 
		  "PRICE" DECIMAL(6,2) , 
		  "CNAME" VARCHAR(60) , 
		  "REFCYCLE" INTEGER , 
		  "QTYPE" CHAR(1) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TXIDPROFILE"

ALTER TABLE "ETCHAP1 "."TXIDPROFILE" 
	ADD CONSTRAINT "P_TXIDPROFILE" PRIMARY KEY
		("TXID");
		
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."LOGFILE"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."LOGFILE";
CREATE TABLE "ETCHAP1 "."LOGFILE"  (
		  "QDATE" CHAR(9) NOT NULL , 
		  "MSGID" CHAR(12) NOT NULL , 
		  "TXID" CHAR(4) NOT NULL , 
		  "PRODUCTID" CHAR(2) , 
		  "QUERYKEY1" CHAR(10) , 
		  "QUERYKEY2" CHAR(10) , 
		  "QUERYKEY3" CHAR(40) , 
		  "QUERYKEY4" CHAR(9) , 
		  "QUERYKEY5" CHAR(9) , 
		  "PRICE" DECIMAL(6,2) , 
		  "TOTCH" CHAR(1) , 
		  "PROCTIME1" BIGINT , 
		  "PROCTIME2" BIGINT , 
		  "PLATFORM" CHAR(1) , 
		  "CHARGEID" CHAR(9) , 
		  "RC" CHAR(4) , 
		  "REQUESTID" CHAR(10) , 
		  "UCNAME" CHAR(10) , 
		  "DIVISION" CHAR(4) , 
		  "DPNAME" CHAR(20) , 
		  "RFQDATE" CHAR(9) , 
		  "REASON" CHAR(3) , 
		  "FORCEFLAG" CHAR(1) WITH DEFAULT 'A' , 
		  "CHECKER" CHAR(10) , 
		  "CHECKERID" CHAR(15) , 
		  "TEAMNO" CHAR(2) , 
		  "CRDATE" TIMESTAMP WITH DEFAULT CURRENT TIMESTAMP , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "HRC" CHAR(4) WITH DEFAULT '0001' , 
		  "XRC" CHAR(4) WITH DEFAULT '0001' , 
		  "QRC" CHAR(4) WITH DEFAULT '0001' )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."LOGFILE"

ALTER TABLE "ETCHAP1 "."LOGFILE" 
	ADD CONSTRAINT "P_LOGFILE" PRIMARY KEY
		("QDATE",
		 "MSGID",
		 "TXID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE01" ON "ETCHAP1 "."LOGFILE" 
		("TOTCH" ASC,
		 "DIVISION" ASC,
		 "RC" ASC,
		 "QDATE" ASC,
		 "PRICE" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE02" ON "ETCHAP1 "."LOGFILE" 
		("TXID" ASC,
		 "TOTCH" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE03" ON "ETCHAP1 "."LOGFILE" 
		("QDATE" ASC,
		 "RC" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE04" ON "ETCHAP1 "."LOGFILE" 
		("RC" ASC,
		 "DIVISION" ASC,
		 "QDATE" ASC,
		 "REQUESTID" ASC,
		 "QUERYKEY1" ASC,
		 "MSGID" ASC,
		 "TXID" ASC,
		 "CHECKER" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE05" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY1" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE06" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY2" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE07" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY3" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE08" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY4" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE09" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY5" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;