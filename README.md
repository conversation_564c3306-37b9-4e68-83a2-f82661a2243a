# TCB-EJCIC 系統文件

本專案包含三個主要子系統的完整技術文件和分析報告。

## 專案概述

TCB-EJCIC 是一個金融交易和監控系統，由三個核心子專案組成：

- **TCBJCICW**: 聯合徵信中心交易系統
- **TCBETCHW**: 票據交換所交易系統
- **TCBEJMNW**: 系統監控平台

## 技術架構

- **JDK**: 1.6
- **Web框架**: Spring MVC
- **建置工具**: Maven 3.2.5
- **Web容器**: Jetty 8.1.8
- **資料庫**: IBM DB2
- **訊息佇列**: IBM MQ

## 文件結構

### 📋 系統分析文件
- **[spec.md](spec.md)** - 整體系統規格文件
- **[report.md](report.md)** - 系統分析報告
- **[todolist.md](todolist.md)** - 改善任務清單

### 📊 各專案詳細規格
- **[TCBJCICW-spec.md](TCBJCICW-spec.md)** - 聯徵中心交易系統規格
- **[TCBETCHW-spec.md](TCBETCHW-spec.md)** - 票據交換所交易系統規格
- **[TCBEJMNW-spec.md](TCBEJMNW-spec.md)** - 系統監控平台規格

### 📁 專案目錄
- **TCBJCICW/** - 聯徵中心交易系統原始碼
- **TCBETCHW/** - 票據交換所交易系統原始碼
- **TCBEJMNW/** - 系統監控平台原始碼
- **common-resources/** - 共用資源和配置
- **docs/** - 原有技術文件

## 環境需求

- JDK 1.6 (安裝路徑: C:\Program Files\jdk1.6.0_45)
- Maven 3.2.5 (安裝路徑: C:\apache-maven-3.2.5)
- Jetty 8.1.8 (安裝路徑: C:\jetty-distribution-8.1.8.v20121106)

## 使用 VS Code 自動配置環境

本專案已經配置了 VS Code 的設定文件，可以自動設定開發環境。只需要：

1. 使用 VS Code 開啟專案目錄
2. 安裝以下 VS Code 擴展：
   - Java Extension Pack
   - Maven for Java
   - PowerShell

### 使用 VS Code 任務啟動專案

1. 按下 `Ctrl+Shift+P` 開啟命令面板
2. 輸入 `Tasks: Run Task` 並選擇
3. 選擇 `設定開發環境` 任務來設定環境變數
4. 選擇 `啟動 Jetty 服務器` 任務來編譯並啟動專案

## 手動配置步驟

如果不使用 VS Code，也可以手動配置環境：

### 1. 設定環境變數

執行 `setup-env.ps1` PowerShell 腳本來設定所有必要的環境變數：

```powershell
powershell -ExecutionPolicy Bypass -File setup-env.ps1
```

這個腳本會：
- 設定 JAVA_HOME 環境變數指向 JDK 1.6
- 設定 MAVEN_HOME 環境變數指向 Maven 3.2.5
- 設定 JETTY_HOME 環境變數指向 Jetty 8.1.8
- 更新 PATH 環境變數
- 創建 Jetty 基礎目錄
- 複製 jetty-env.xml 到 Jetty 配置目錄
- 創建 Maven 設定文件

### 2. 編譯並啟動專案

執行 `start-jetty.ps1` PowerShell 腳本來編譯專案並啟動 Jetty 服務器：

```powershell
powershell -ExecutionPolicy Bypass -File start-jetty.ps1
```

這個腳本會：
- 設定必要的環境變數
- 檢查 Java 和 Maven 版本
- 編譯所有專案
- 複製 WAR 文件到 Jetty webapps 目錄
- 啟動 Jetty 服務器

## 系統架構圖

```mermaid
graph TB
    subgraph "前端層"
        UI[Web UI - JSP]
    end

    subgraph "應用層"
        JCICW[TCBJCICW<br/>聯徵交易系統]
        ETCHW[TCBETCHW<br/>票交系統]
        EJMNW[TCBEJMNW<br/>監控系統]
    end

    subgraph "服務層"
        JDISP[JCIC Dispatcher]
        TDISP[TCH Dispatcher]
        JGW[JCIC Gateway]
        TGW[TCH Gateway]
    end

    subgraph "資料層"
        DB[(IBM DB2<br/>資料庫)]
        MQ[IBM MQ<br/>訊息佇列]
    end

    subgraph "外部系統"
        JCIC[聯合徵信中心]
        TCH[票據交換所]
    end

    UI --> JCICW
    UI --> ETCHW
    UI --> EJMNW

    JCICW --> JDISP
    ETCHW --> TDISP
    EJMNW --> MQ

    JDISP --> JGW
    TDISP --> TGW

    JGW --> JCIC
    TGW --> TCH

    JCICW --> DB
    ETCHW --> DB
    EJMNW --> DB

    JDISP --> MQ
    TDISP --> MQ
    JGW --> MQ
    TGW --> MQ

    EJMNW -.-> JCICW
    EJMNW -.-> ETCHW
```

## 快速開始

### 1. 閱讀系統分析
- 先閱讀 [spec.md](spec.md) 了解整體架構
- 查看 [report.md](report.md) 了解現況分析和改善建議
- 參考 [todolist.md](todolist.md) 了解後續任務

### 2. 深入了解各專案
- **聯徵系統**: 閱讀 [TCBJCICW-spec.md](TCBJCICW-spec.md)
- **票交系統**: 閱讀 [TCBETCHW-spec.md](TCBETCHW-spec.md)
- **監控系統**: 閱讀 [TCBEJMNW-spec.md](TCBEJMNW-spec.md)

## 專案結構

```
tcb-ejcic/
├── spec.md                     # 整體系統規格
├── report.md                   # 系統分析報告
├── todolist.md                 # 改善任務清單
├── TCBJCICW-spec.md            # 聯徵系統規格
├── TCBETCHW-spec.md            # 票交系統規格
├── TCBEJMNW-spec.md            # 監控系統規格
├── README.md                   # 本文件
├── common-resources/           # 共用資源和配置
├── docs/                       # 原有技術文件
├── TCBJCICW/                   # 聯徵中心交易系統
├── TCBETCHW/                   # 票據交換所交易系統
└── TCBEJMNW/                   # 系統監控平台
```

## 技術特點

### 共同特點
- **分層架構**: Web層、服務層、資料存取層
- **Handler模式**: 統一的請求處理模式
- **自定義ORM**: 使用註解和反射的簡單ORM
- **MQ整合**: 完整的IBM MQ整合
- **Spring MVC**: 基於Spring MVC框架

### 各系統特色
- **TCBJCICW**: 組合查詢(CPX)、多層授權控制
- **TCBETCHW**: 區域碼處理、票據狀態管理
- **TCBEJMNW**: 多系統監控、排程任務、多資料庫

## 資料庫配置

資料庫連接配置在 `common-resources\jetty-env.xml` 文件中。

## MQ 配置

MQ 連接配置在 `common-resources\jetty-env.xml` 文件中。

## 常見問題

### 如何更改資料庫連接配置？

編輯 `common-resources\jetty-env.xml` 文件，修改相應的資料庫連接參數。

### 如何更改 MQ 連接配置？

編輯 `common-resources\jetty-env.xml` 文件，修改相應的 MQ 連接參數。

### 如何在 IDE 中開發？

1. 在 IDE 中導入專案
2. 設定 JDK 1.6 作為專案的 JDK
3. 設定 Maven 3.2.5 作為專案的 Maven
4. 使用 Jetty Maven 插件啟動專案

## 系統現代化建議

根據 [report.md](report.md) 的分析，建議優先處理：

1. **JDK升級**: 從JDK 1.6升級至JDK 8或更新版本
2. **安全性強化**: 修復已知安全漏洞
3. **框架現代化**: 遷移至Spring Boot
4. **容器化部署**: 實施Docker容器化
5. **CI/CD建立**: 建立自動化建置和部署流程

詳細的改善計畫請參考 [todolist.md](todolist.md)。

## 貢獻指南

1. 閱讀相關規格文件
2. 遵循現有的程式碼風格
3. 更新相關文件
4. 進行充分測試
5. 提交Pull Request

## 聯絡資訊

如有任何問題或建議，請聯絡專案維護團隊。
