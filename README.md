# TCB-EJCIC 開發環境配置指南

本文檔提供了如何配置 TCB-EJCIC 專案開發環境的詳細說明。

## 環境需求

- JDK 1.6 (安裝路徑: C:\Program Files\jdk1.6.0_45)
- Maven 3.2.5 (安裝路徑: C:\apache-maven-3.2.5)
- Jetty 8.1.8 (安裝路徑: C:\jetty-distribution-8.1.8.v20121106)

## 使用 VS Code 自動配置環境

本專案已經配置了 VS Code 的設定文件，可以自動設定開發環境。只需要：

1. 使用 VS Code 開啟專案目錄
2. 安裝以下 VS Code 擴展：
   - Java Extension Pack
   - Maven for Java
   - PowerShell

### 使用 VS Code 任務啟動專案

1. 按下 `Ctrl+Shift+P` 開啟命令面板
2. 輸入 `Tasks: Run Task` 並選擇
3. 選擇 `設定開發環境` 任務來設定環境變數
4. 選擇 `啟動 Jetty 服務器` 任務來編譯並啟動專案

## 手動配置步驟

如果不使用 VS Code，也可以手動配置環境：

### 1. 設定環境變數

執行 `setup-env.ps1` PowerShell 腳本來設定所有必要的環境變數：

```powershell
powershell -ExecutionPolicy Bypass -File setup-env.ps1
```

這個腳本會：
- 設定 JAVA_HOME 環境變數指向 JDK 1.6
- 設定 MAVEN_HOME 環境變數指向 Maven 3.2.5
- 設定 JETTY_HOME 環境變數指向 Jetty 8.1.8
- 更新 PATH 環境變數
- 創建 Jetty 基礎目錄
- 複製 jetty-env.xml 到 Jetty 配置目錄
- 創建 Maven 設定文件

### 2. 編譯並啟動專案

執行 `start-jetty.ps1` PowerShell 腳本來編譯專案並啟動 Jetty 服務器：

```powershell
powershell -ExecutionPolicy Bypass -File start-jetty.ps1
```

這個腳本會：
- 設定必要的環境變數
- 檢查 Java 和 Maven 版本
- 編譯所有專案
- 複製 WAR 文件到 Jetty webapps 目錄
- 啟動 Jetty 服務器

## 專案結構

- common-resources：共用資源和庫
- TCBEJMNW：獨立 Web 專案
- TCBETCHW：獨立 Web 專案
- TCBJCICW：獨立 Web 專案

## 資料庫配置

資料庫連接配置在 `common-resources\jetty-env.xml` 文件中。

## MQ 配置

MQ 連接配置在 `common-resources\jetty-env.xml` 文件中。

## 常見問題

### 如何更改資料庫連接配置？

編輯 `common-resources\jetty-env.xml` 文件，修改相應的資料庫連接參數。

### 如何更改 MQ 連接配置？

編輯 `common-resources\jetty-env.xml` 文件，修改相應的 MQ 連接參數。

### 如何在 IDE 中開發？

1. 在 IDE 中導入專案
2. 設定 JDK 1.6 作為專案的 JDK
3. 設定 Maven 3.2.5 作為專案的 Maven
4. 使用 Jetty Maven 插件啟動專案
