package mon.model;

/**
 * <pre>
 * MonModuleThread.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class MonModuleThread {
	private String sysId;
	private String moduleId;
	private String moduleName;
	private int threadSize;

	/**
	 * Returns the sysId
	 * 
	 * @return the sysId
	 */
	public String getSysId() {
		return sysId;
	}

	/**
	 * Sets the sysId
	 * 
	 * @param sysId
	 *            the sysId to set
	 */
	public void setSysId(String sysId) {
		this.sysId = sysId;
	}

	/**
	 * Returns the moduleId
	 * 
	 * @return the moduleId
	 */
	public String getModuleId() {
		return moduleId;
	}

	/**
	 * Sets the moduleId
	 * 
	 * @param moduleId
	 *            the moduleId to set
	 */
	public void setModuleId(String moduleId) {
		this.moduleId = moduleId;
	}

	/**
	 * Returns the moduleName
	 * 
	 * @return the moduleName
	 */
	public String getModuleName() {
		return moduleName;
	}

	/**
	 * Sets the moduleName
	 * 
	 * @param moduleName
	 *            the moduleName to set
	 */
	public void setModuleName(String moduleName) {
		this.moduleName = moduleName;
	}

	/**
	 * Returns the threadSize
	 * 
	 * @return the threadSize
	 */
	public int getThreadSize() {
		return threadSize;
	}

	/**
	 * Sets the threadSize
	 * 
	 * @param threadSize
	 *            the threadSize to set
	 */
	public void setThreadSize(int threadSize) {
		this.threadSize = threadSize;
	}

}
