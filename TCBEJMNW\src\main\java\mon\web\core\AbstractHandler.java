package mon.web.core;

import mon.web.constant.WebConst;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * <pre> AbstractHandler.java </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul> <li>2003/06/30,PGU,NEW </ul>
 */
public abstract class AbstractHandler implements Handler {

	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	protected String getHandlerURL(String handlerId) {
		return StringUtils.join(new String[] { WebConst.WEB_CTRLR, "?", WebConst.REQ_PARAM_HANDLER, "=", handlerId });
	}

	protected String getHandlerURL() {
		String handlerId = this.getClass().getSimpleName();
		handlerId = StringUtils.replace(handlerId, WebConst.POSTFIX_HANDLER, "");
		return this.getHandlerURL(handlerId);
	}

	@Override
	public String getErrorBackUrl() {
		return this.getHandlerURL();
	}

}
