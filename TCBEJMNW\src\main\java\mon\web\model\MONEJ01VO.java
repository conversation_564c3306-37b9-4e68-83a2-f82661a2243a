package mon.web.model;

/**
 * <pre>
 * MONEJ01VO.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class MONEJ01VO {
	boolean ok = false;
	private String qid;
	private String qname;
	private String qdesc;
	private String depth;
	private String color;

	/**
	 * Returns the qid
	 * 
	 * @return the qid
	 */
	public String getQid() {
		return qid;
	}

	/**
	 * Sets the qid
	 * 
	 * @param qid
	 *            the qid to set
	 */
	public void setQid(String qid) {
		this.qid = qid;
	}

	/**
	 * Returns the ok
	 * 
	 * @return the ok
	 */
	public boolean isOk() {
		return this.ok;
	}

	/**
	 * Sets the ok
	 * 
	 * @param ok
	 *            the ok to set
	 */
	public void setOk(boolean ok) {
		this.ok = ok;
	}

	/**
	 * Returns the qname
	 * 
	 * @return the qname
	 */
	public String getQname() {
		return this.qname;
	}

	/**
	 * Sets the qname
	 * 
	 * @param qname
	 *            the qname to set
	 */
	public void setQname(String qname) {
		this.qname = qname;
	}

	/**
	 * Returns the qdesc
	 * 
	 * @return the qdesc
	 */
	public String getQdesc() {
		return this.qdesc;
	}

	/**
	 * Sets the qdesc
	 * 
	 * @param qdesc
	 *            the qdesc to set
	 */
	public void setQdesc(String qdesc) {
		this.qdesc = qdesc;
	}

	/**
	 * Returns the depth
	 * 
	 * @return the depth
	 */
	public String getDepth() {
		return this.depth;
	}

	/**
	 * Sets the depth
	 * 
	 * @param depth
	 *            the depth to set
	 */
	public void setDepth(String depth) {
		this.depth = depth;
	}

	/**
	 * Returns the color
	 * 
	 * @return the color
	 */
	public String getColor() {
		return this.color;
	}

	/**
	 * Sets the color
	 * 
	 * @param color
	 *            the color to set
	 */
	public void setColor(String color) {
		this.color = color;
	}

}
