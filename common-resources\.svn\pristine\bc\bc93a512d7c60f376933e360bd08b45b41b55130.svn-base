--CREATE BUFFERPOOL ETCHBP4K SIZE AUTOMATIC PAGESIZE 4096 ;

--CREATE LARGE TABLESPACE ETCHTABSP  PAGESIZE 4096 MANAGED BY AUTOMATIC STORAGE BUFFERPOOL ETCHBP4K;

------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."ATOM"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."ATOM";
CREATE TABLE "ETCHAP1 "."ATOM"  (
		  "ATOMID" CHAR(3) NOT NULL , 
		  "COLNAME" VARCHAR(16) NOT NULL , 
		  "DATATYPE" CHAR(1) , 
		  "DATALEN" INTEGER , 
		  "CNAME" VARCHAR(40) , 
		  "COLSEQ" INTEGER )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."ATOM"

ALTER TABLE "ETCHAP1 "."ATOM" 
	ADD CONSTRAINT "P_ATOM" PRIMARY KEY
		("ATOMID",
		 "COLNAME");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."ATOMPROFILE"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."ATOMPROFILE";
CREATE TABLE "ETCHAP1 "."ATOMPROFILE"  (
		  "ATOMID" CHAR(3) NOT NULL , 
		  "PRICE" DECIMAL(6,2) , 
		  "CNAME" VARCHAR(40) , 
		  "TBNAME" VARCHAR(40) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."ATOMPROFILE"

ALTER TABLE "ETCHAP1 "."ATOMPROFILE" 
	ADD CONSTRAINT "P_ATOMPROFILE" PRIMARY KEY
		("ATOMID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TXID"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TXID";
CREATE TABLE "ETCHAP1 "."TXID"  (
		  "TXID" CHAR(4) NOT NULL , 
		  "ATOMID" CHAR(3) NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TXID"

ALTER TABLE "ETCHAP1 "."TXID" 
	ADD CONSTRAINT "P_TXID" PRIMARY KEY
		("TXID",
		 "ATOMID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TXIDPROFILE"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TXIDPROFILE";
CREATE TABLE "ETCHAP1 "."TXIDPROFILE"  (
		  "TXID" CHAR(4) NOT NULL , 
		  "PRICE" DECIMAL(6,2) , 
		  "CNAME" VARCHAR(60) , 
		  "REFCYCLE" INTEGER , 
		  "QTYPE" CHAR(1) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TXIDPROFILE"

ALTER TABLE "ETCHAP1 "."TXIDPROFILE" 
	ADD CONSTRAINT "P_TXIDPROFILE" PRIMARY KEY
		("TXID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."SCH_MAIN"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."SCH_MAIN";
CREATE TABLE "ETCHAP1 "."SCH_MAIN"  (
		  "SCH_SNO" BIGINT NOT NULL , 
		  "SCH_TIME" TIMESTAMP , 
		  "SCH_STATUS" CHAR(4) , 
		  "ITEM_CNT" INTEGER , 
		  "CRDATE" TIMESTAMP WITH DEFAULT CURRENT TIMESTAMP , 
		  "ISDEL" CHAR(1) , 
		  "DELTIME" TIMESTAMP , 
		  "TEAMNO" CHAR(2) , 
		  "REQUESTID" CHAR(10) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SCH_MAIN"

ALTER TABLE "ETCHAP1 "."SCH_MAIN" 
	ADD CONSTRAINT "P_SCH_MAIN" PRIMARY KEY
		("SCH_SNO");



-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_MAIN"

CREATE INDEX "ETCHAP1 "."XSCH_MAIN01" ON "ETCHAP1 "."SCH_MAIN" 
		("ISDEL" ASC,
		 "SCH_TIME" ASC,
		 "TEAMNO" ASC,
		 "CRDATE" ASC,
		 "ITEM_CNT" ASC,
		 "SCH_SNO" ASC,
		 "SCH_STATUS" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_MAIN"

CREATE INDEX "ETCHAP1 "."XSCH_MAIN02" ON "ETCHAP1 "."SCH_MAIN" 
		("REQUESTID" ASC,
		 "ISDEL" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_MAIN"

CREATE INDEX "ETCHAP1 "."XSCH_MAIN03" ON "ETCHAP1 "."SCH_MAIN" 
		("ISDEL" ASC,
		 "REQUESTID" ASC,
		 "SCH_TIME" DESC,
		 "CRDATE" ASC,
		 "ITEM_CNT" ASC,
		 "SCH_STATUS" ASC,
		 "SCH_SNO" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."SCH_ITEM"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."SCH_ITEM";
CREATE TABLE "ETCHAP1 "."SCH_ITEM"  (
		  "DETAIL_SNO" BIGINT NOT NULL , 
		  "SCH_SNO" BIGINT NOT NULL , 
		  "MSGID" CHAR(12) , 
		  "TXID" CHAR(4) , 
		  "PRODUCTID" CHAR(2) , 
		  "QUERYKEY1" VARCHAR(10) , 
		  "QUERYKEY2" VARCHAR(10) , 
		  "QUERYKEY3" VARCHAR(40) , 
		  "QUERYKEY4" VARCHAR(9) , 
		  "QUERYKEY5" VARCHAR(9) , 
		  "CHARGEID" CHAR(9) , 
		  "RC" CHAR(4) , 
		  "REQUESTID" CHAR(10) , 
		  "UCNAME" VARCHAR(10) , 
		  "DIVISION" CHAR(4) , 
		  "DPNAME" VARCHAR(20) , 
		  "FORCEFLAG" CHAR(1) , 
		  "BEGIN_DATE" TIMESTAMP , 
		  "END_DATE" TIMESTAMP , 
		  "CRDATE" TIMESTAMP WITH DEFAULT CURRENT TIMESTAMP , 
		  "ISDEL" CHAR(1) , 
		  "DELTIME" TIMESTAMP )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SCH_ITEM"

ALTER TABLE "ETCHAP1 "."SCH_ITEM" 
	ADD CONSTRAINT "P_SCH_ITEM" PRIMARY KEY
		("DETAIL_SNO");



-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM01" ON "ETCHAP1 "."SCH_ITEM" 
		("SCH_SNO" ASC,
		 "ISDEL" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM02" ON "ETCHAP1 "."SCH_ITEM" 
		("QUERYKEY1" ASC,
		 "SCH_SNO" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM03" ON "ETCHAP1 "."SCH_ITEM" 
		("QUERYKEY1" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM04" ON "ETCHAP1 "."SCH_ITEM" 
		("QUERYKEY4" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM05" ON "ETCHAP1 "."SCH_ITEM" 
		("QUERYKEY5" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."BANKDATA"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."BANKDATA";
CREATE TABLE "ETCHAP1 "."BANKDATA"  (
		  "BID" CHAR(4) NOT NULL , 
		  "CNAME" CHAR(40) , 
		  "CHARGEID" VARCHAR(4) , 
		  "AREA" CHAR(4) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."BANKDATA"

ALTER TABLE "ETCHAP1 "."BANKDATA" 
	ADD CONSTRAINT "P_BANKDATA" PRIMARY KEY
		("BID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."BANKDATA"

CREATE UNIQUE INDEX "ETCHAP1 "."XBANKDATA01" ON "ETCHAP1 "."BANKDATA" 
		("BID" ASC)
		INCLUDE ("CNAME" )
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."BANKDATA"

CREATE INDEX "ETCHAP1 "."XBANKDATA02" ON "ETCHAP1 "."BANKDATA" 
		("AREA" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."SYSPARAM"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."SYSPARAM";
CREATE TABLE "ETCHAP1 "."SYSPARAM"  (
		  "PARAM" VARCHAR(30) NOT NULL , 
		  "PARAMVALUE" VARCHAR(1000) NOT NULL , 
		  "PARAMDESC" VARCHAR(300) , 
		  "MODIFYBY" VARCHAR(10) , 
		  "MODIFYTIME" TIMESTAMP )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SYSPARAM"

ALTER TABLE "ETCHAP1 "."SYSPARAM" 
	ADD CONSTRAINT "P_SYSPARAM" PRIMARY KEY
		("PARAM");

------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."LOGFILE"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."LOGFILE";
CREATE TABLE "ETCHAP1 "."LOGFILE"  (
		  "QDATE" CHAR(9) NOT NULL , 
		  "MSGID" CHAR(12) NOT NULL , 
		  "TXID" CHAR(4) NOT NULL , 
		  "PRODUCTID" CHAR(2) , 
		  "QUERYKEY1" CHAR(10) , 
		  "QUERYKEY2" CHAR(10) , 
		  "QUERYKEY3" CHAR(40) , 
		  "QUERYKEY4" CHAR(9) , 
		  "QUERYKEY5" CHAR(9) , 
		  "PRICE" DECIMAL(6,2) , 
		  "TOTCH" CHAR(1) , 
		  "PROCTIME1" BIGINT , 
		  "PROCTIME2" BIGINT , 
		  "PLATFORM" CHAR(1) , 
		  "CHARGEID" CHAR(9) , 
		  "RC" CHAR(4) , 
		  "REQUESTID" CHAR(10) , 
		  "UCNAME" CHAR(10) , 
		  "DIVISION" CHAR(4) , 
		  "DPNAME" CHAR(20) , 
		  "RFQDATE" CHAR(9) , 
		  "REASON" CHAR(3) , 
		  "FORCEFLAG" CHAR(1) WITH DEFAULT 'A' , 
		  "CHECKER" CHAR(10) , 
		  "CHECKERID" CHAR(15) , 
		  "TEAMNO" CHAR(2) , 
		  "CRDATE" TIMESTAMP WITH DEFAULT CURRENT TIMESTAMP , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "HRC" CHAR(4) WITH DEFAULT '0001' , 
		  "XRC" CHAR(4) WITH DEFAULT '0001' , 
		  "QRC" CHAR(4) WITH DEFAULT '0001' )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."LOGFILE"

ALTER TABLE "ETCHAP1 "."LOGFILE" 
	ADD CONSTRAINT "P_LOGFILE" PRIMARY KEY
		("QDATE",
		 "MSGID",
		 "TXID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE01" ON "ETCHAP1 "."LOGFILE" 
		("TOTCH" ASC,
		 "DIVISION" ASC,
		 "RC" ASC,
		 "QDATE" ASC,
		 "PRICE" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE02" ON "ETCHAP1 "."LOGFILE" 
		("TXID" ASC,
		 "TOTCH" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE03" ON "ETCHAP1 "."LOGFILE" 
		("QDATE" ASC,
		 "RC" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE04" ON "ETCHAP1 "."LOGFILE" 
		("RC" ASC,
		 "DIVISION" ASC,
		 "QDATE" ASC,
		 "REQUESTID" ASC,
		 "QUERYKEY1" ASC,
		 "MSGID" ASC,
		 "TXID" ASC,
		 "CHECKER" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE05" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY1" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE06" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY2" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE07" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY3" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE08" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY4" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE09" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY5" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TCHDATA"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TCHDATA";
CREATE TABLE "ETCHAP1 "."TCHDATA"  (
		  "QDATE" CHAR(9) NOT NULL , 
		  "TXID" CHAR(4) NOT NULL , 
		  "MSGID" CHAR(12) NOT NULL , 
		  "DATATYPE" CHAR(1) NOT NULL , 
		  "QUERYKEY1" CHAR(10) , 
		  "QUERYKEY2" CHAR(10) , 
		  "QUERYKEY3" CHAR(40) , 
		  "QUERYKEY4" CHAR(9) , 
		  "QUERYKEY5" CHAR(9) , 
		  "DATA" CLOB(4194304) LOGGED NOT COMPACT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TCHDATA"

ALTER TABLE "ETCHAP1 "."TCHDATA" 
	ADD CONSTRAINT "P_TCHDATA" PRIMARY KEY
		("QDATE",
		 "TXID",
		 "MSGID",
		 "DATATYPE");



-- DDL Statements for Indexes on Table "ETCHAP1 "."TCHDATA"

CREATE INDEX "ETCHAP1 "."XTCHDATA01" ON "ETCHAP1 "."TCHDATA" 
		("QDATE" ASC,
		 "TXID" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TCHDATA"

CREATE INDEX "ETCHAP1 "."XTCHDATA02" ON "ETCHAP1 "."TCHDATA" 
		("QDATE" ASC,
		 "TXID" ASC,
		 "DATATYPE" ASC,
		 "QUERYKEY1" ASC,
		 "QUERYKEY2" ASC,
		 "QUERYKEY3" ASC,
		 "QUERYKEY4" ASC,
		 "QUERYKEY5" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116";
CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"  (
		  "END_DATE" VARCHAR(8) , 
		  "QUERY_ID" VARCHAR(11) , 
		  "DIRECTOR_I" VARCHAR(11) , 
		  "UED_DCC" VARCHAR(4) , 
		  "UED_BCA" VARCHAR(10) , 
		  "UMS_BCC" VARCHAR(3) , 
		  "UMS_BCA" VARCHAR(10) , 
		  "SD_BCC" VARCHAR(3) , 
		  "SD_BCA" VARCHAR(10) , 
		  "CPE_BCC" VARCHAR(3) , 
		  "CPE_BCA" VARCHAR(10) , 
		  "POR_UED_BCC" VARCHAR(4) , 
		  "POR_UED_BCA" VARCHAR(10) , 
		  "POR_UMS_BCC" VARCHAR(3) , 
		  "POR_UMS_BCA" VARCHAR(10) , 
		  "POR_SD_BCC" VARCHAR(3) , 
		  "POR_SD_BCA" VARCHAR(10) , 
		  "POR_CPE_BCC" VARCHAR(3) , 
		  "POR_CPE_BCA" VARCHAR(10) , 
		  "REJECT_DATE" VARCHAR(8) , 
		  "REJECT_END_DATE" VARCHAR(8) , 
		  "TERM_DATE" VARCHAR(8) , 
		  "TERM_EXPR_DATE" VARCHAR(8) , 
		  "FAKE_CNT" VARCHAR(3) , 
		  "ACCT_CNT" VARCHAR(3) , 
		  "ACCT_CODE" VARCHAR(9) , 
		  "ACCT_NO" VARCHAR(9) , 
		  "QRY_RLT" VARCHAR(1) , 
		  "COE_CMT" VARCHAR(1) , 
		  "IMP_INFO_CMT" VARCHAR(1) , 
		  "REL_CNT" VARCHAR(3) , 
		  "DTL_CNT" VARCHAR(4) , 
		  "QRY_NAME" VARCHAR(40) , 
		  "RESERVED" VARCHAR(3) , 
		  "IMP_INFO" VARCHAR(180) , 
		  "BACKUP" VARCHAR(97) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"

CREATE INDEX "ETCHAP1 "."IDX_ATOM_T_001_1" ON "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" 
		("QUERY_ID" ASC,
		 "END_DATE" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"

CREATE INDEX "ETCHAP1 "."IDX_ATOM_T_001_2" ON "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" 
		("QUERY_ID" ASC,
		 "CREATED_TIMESTAMP" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00101" ON "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_001_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116";
CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"  (
		  "REL_ACCT" VARCHAR(11) , 
		  "REL_NAME" VARCHAR(40) , 
		  "RESERVED" VARCHAR(12) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"

CREATE INDEX "ETCHAP1 "."IDX_ATOM_T_003_1" ON "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" 
		("REL_ACCT" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00301" ON "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_003_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116";
CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116"  (
		  "BC_DATE" VARCHAR(8) , 
		  "BC_BANK_CODE" VARCHAR(9) , 
		  "ACCT_NO" VARCHAR(9) , 
		  "CHK_NO" VARCHAR(9) , 
		  "BC_AMT" VARCHAR(10) , 
		  "BC_REASON" VARCHAR(2) , 
		  "POR_DATE" VARCHAR(8) , 
		  "REMARK" VARCHAR(1) , 
		  "COMMENT" VARCHAR(9) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00401" ON "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_004_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116";
CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116"  (
		  "END_DATE" VARCHAR(8) , 
		  "REJECT_ID" VARCHAR(8) , 
		  "INQUIRY_ID" VARCHAR(11) , 
		  "DIRECTOR_ID" VARCHAR(11) , 
		  "INQUIRY_RESULT" VARCHAR(1) , 
		  "COE_CMT" VARCHAR(1) , 
		  "LA_BCC" VARCHAR(4) , 
		  "LA_BC_UPORC" VARCHAR(4) , 
		  "LA_BC_UPORA" VARCHAR(10) , 
		  "LA_BC_PORC" VARCHAR(4) , 
		  "LA_BC_PORA" VARCHAR(10) , 
		  "INQ_CNT" VARCHAR(4) , 
		  "ACC_CNT" VARCHAR(4) , 
		  "INQ_NAME" VARCHAR(40) , 
		  "LA_DUER" VARCHAR(4) , 
		  "BACKUP" VARCHAR(316) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00501" ON "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_005_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214";
CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214"  (
		  "BC_DATE" VARCHAR(8) , 
		  "BC_BANK" VARCHAR(9) , 
		  "AMOUNT" VARCHAR(10) , 
		  "BC_REASON" VARCHAR(2) , 
		  "POR_DATE" VARCHAR(8) , 
		  "RESERVED" VARCHAR(40) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214"

CREATE INDEX "ETCHAP1 "."XATOM_T00601" ON "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214"

CREATE INDEX "SMARTCREDIT"."I_T_006_20060214" ON "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116";
CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116"  (
		  "INQUIRY_DATE" VARCHAR(8) , 
		  "INQ_ACC_NAME" VARCHAR(40) , 
		  "RESERVED" VARCHAR(17) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00701" ON "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_007_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116";
CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116"  (
		  "ACC_NAME" VARCHAR(40) , 
		  "ACC_BANK" VARCHAR(9) , 
		  "ACC_DATE" VARCHAR(8) , 
		  "CLEAR_DATE" VARCHAR(8) , 
		  "RESERVED" VARCHAR(10) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL , 
		  "ACC_NUM" VARCHAR(9) )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00801" ON "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_008_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116"
------------------------------------------------
 
DROP TABLE IF EXISTS "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116";
CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116"  (
		  "END_DATE" VARCHAR(8) , 
		  "INQ_ID" VARCHAR(11) , 
		  "DIRECTOR_ID" VARCHAR(11) , 
		  "INQ_RESULT" VARCHAR(1) , 
		  "COE_COM" VARCHAR(1) , 
		  "LA_BC_UPORD1" VARCHAR(8) , 
		  "LA_BC_UPORA1" VARCHAR(10) , 
		  "LA_BC_UPORR1" VARCHAR(2) , 
		  "LA_BC_UPORD2" VARCHAR(8) , 
		  "LA_BC_UPORA2" VARCHAR(10) , 
		  "LA_BC_UPORR2" VARCHAR(2) , 
		  "LA_BC_UPORD3" VARCHAR(8) , 
		  "LA_BC_UPORA3" VARCHAR(10) , 
		  "LA_BC_UPORR3" VARCHAR(2) , 
		  "LA_BC_PORD1" VARCHAR(8) , 
		  "LA_BC_PORA1" VARCHAR(10) , 
		  "LA_BC_POD1" VARCHAR(8) , 
		  "LA_BC_PORR1" VARCHAR(2) , 
		  "LA_BC_PORD2" VARCHAR(8) , 
		  "LA_BC_PORA2" VARCHAR(10) , 
		  "LA_BC_POD2" VARCHAR(8) , 
		  "LA_BC_PORR2" VARCHAR(2) , 
		  "LA_BC_PORD3" VARCHAR(8) , 
		  "LA_BC_PORA3" VARCHAR(10) , 
		  "LA_BC_POD3" VARCHAR(8) , 
		  "LA_BC_PORR3" VARCHAR(2) , 
		  "INQ_CNT" VARCHAR(4) , 
		  "INQ_DATE1" VARCHAR(8) , 
		  "INQ_ACC_NAME1" VARCHAR(40) , 
		  "INQ_DATE2" VARCHAR(8) , 
		  "INQ_ACC_NAME2" VARCHAR(40) , 
		  "INQ_DATE3" VARCHAR(8) , 
		  "INQ_ACC_NAME3" VARCHAR(40) , 
		  "INQ_NAME" VARCHAR(40) , 
		  "LA_DUER" VARCHAR(4) , 
		  "BACKUP" VARCHAR(66) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00901" ON "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_009_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
		
----------------------------

-- DDL Statements for Views

----------------------------
SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_001 AS SELECT * FROM TB__ATOM_CACHE_T_001_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_003 AS SELECT * FROM TB__ATOM_CACHE_T_003_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_004 AS SELECT * FROM TB__ATOM_CACHE_T_004_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_005 AS SELECT * FROM TB__ATOM_CACHE_T_005_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_006 AS SELECT * FROM TB__ATOM_CACHE_T_006_20060214;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_007 AS SELECT * FROM TB__ATOM_CACHE_T_007_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_009 AS SELECT * FROM TB__ATOM_CACHE_T_009_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_008 AS SELECT * FROM TB__ATOM_CACHE_T_008_20060116;