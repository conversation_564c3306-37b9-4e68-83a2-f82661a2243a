package etch.jdbc;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.slf4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowCallbackHandler;

public class ETCHResultSetCallbackExtractor implements ResultSetExtractor<Object> {

	private final RowCallbackHandler rcbh;
	private long current;
	private Logger logger;

	public ETCHResultSetCallbackExtractor(Logger logger, long curr, RowCallbackHandler rch) {
		this.rcbh = rch;
		this.current = curr;
		this.logger = logger;
	}

	@Override
	public Object extractData(ResultSet rs) throws SQLException, DataAccessException {
		logger.info("EloanNamedJdbcTemplate spend {} ms", (System.currentTimeMillis() - current));
		while (rs.next()) {
			this.rcbh.processRow(rs);
		}
		return null;
	}

}
