package mon.service.impl;

import java.util.Enumeration;
import java.util.Iterator;
import java.util.Properties;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import mon.service.ReturnCodeService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <pre>
 * ConfigServiceImpl
 * </pre>
 * 
 * @since 2003/6/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/6/22,PGU,new
 *          </ul>
 */
@Service("returnCodeService")
public class ReturnCodeServiceImpl implements ReturnCodeService {
	private static final Logger LOGGER = LoggerFactory.getLogger(ReturnCodeServiceImpl.class);

	@Resource(name = "errorProperties")
	private Properties errorProp;

	private static Properties prop;

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.service.ConfigService#reload()
	 */
	@Override
	@PostConstruct
	public synchronized void reload() {
		this.copyProperties(this.errorProp, prop);
		LOGGER.error(this.toString());
	}

	@SuppressWarnings("rawtypes")
	private void copyProperties(Properties src_prop, Properties dest_prop) {
		dest_prop = new Properties();
		for (Enumeration propertyNames = src_prop.propertyNames(); propertyNames.hasMoreElements();) {
			Object key = propertyNames.nextElement();
			dest_prop.put(key, src_prop.get(key));
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.service.ConfigService#getProperty(java.lang.String)
	 */
	@Override
	public String getCodeDesc(String name) {
		return this.errorProp.getProperty(name);
	}

	/**
	 * @param name
	 * @return
	 */
	public static String getProperty(String name) {
		return prop.getProperty(name);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#toString()
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public String toString() {
		SortedMap sortedSystemProperties = new TreeMap(errorProp);
		Set keySet = sortedSystemProperties.keySet();
		Iterator iterator = keySet.iterator();
		StringBuilder str = new StringBuilder();
		while (iterator.hasNext()) {
			String propertyName = (String) iterator.next();
			String propertyValue = errorProp.getProperty(propertyName);
			str.append(propertyName).append(": ").append(propertyValue).append("\n");
		}
		return str.toString();
	}

}
