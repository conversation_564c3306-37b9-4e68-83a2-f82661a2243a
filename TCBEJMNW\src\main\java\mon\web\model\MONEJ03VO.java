package mon.web.model;

/**
 * <pre> MONEJ01VO.java </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul> <li>2003/06/30,PGU,NEW </ul>
 */
public class MONEJ03VO {
	boolean ok = false;
	private String chlName;
	private String chlDesc;
	private String chlStatus;
	private String conSize;
	private String msgCount;

	/**
	 * Returns the ok
	 * 
	 * @return the ok
	 */
	public boolean isOk() {
		return this.ok;
	}

	/**
	 * Sets the ok
	 * 
	 * @param ok
	 *            the ok to set
	 */
	public void setOk(boolean ok) {
		this.ok = ok;
	}

	/**
	 * Returns the chlName
	 * 
	 * @return the chlName
	 */
	public String getChlName() {
		return this.chlName;
	}

	/**
	 * Sets the chlName
	 * 
	 * @param chlName
	 *            the chlName to set
	 */
	public void setChlName(String chlName) {
		this.chlName = chlName;
	}

	/**
	 * Returns the chlDesc
	 * 
	 * @return the chlDesc
	 */
	public String getChlDesc() {
		return this.chlDesc;
	}

	/**
	 * Sets the chlDesc
	 * 
	 * @param chlDesc
	 *            the chlDesc to set
	 */
	public void setChlDesc(String chlDesc) {
		this.chlDesc = chlDesc;
	}

	/**
	 * Returns the chlStatus
	 * 
	 * @return the chlStatus
	 */
	public String getChlStatus() {
		return this.chlStatus;
	}

	/**
	 * Sets the chlStatus
	 * 
	 * @param chlStatus
	 *            the chlStatus to set
	 */
	public void setChlStatus(String chlStatus) {
		this.chlStatus = chlStatus;
	}

	/**
	 * Returns the conSize
	 * 
	 * @return the conSize
	 */
	public String getConSize() {
		return this.conSize;
	}

	/**
	 * Sets the conSize
	 * 
	 * @param conSize
	 *            the conSize to set
	 */
	public void setConSize(String conSize) {
		this.conSize = conSize;
	}

	/**
	 * Returns the msgCount
	 * 
	 * @return the msgCount
	 */
	public String getMsgCount() {
		return this.msgCount;
	}

	/**
	 * Sets the msgCount
	 * 
	 * @param msgCount
	 *            the msgCount to set
	 */
	public void setMsgCount(String msgCount) {
		this.msgCount = msgCount;
	}

}
