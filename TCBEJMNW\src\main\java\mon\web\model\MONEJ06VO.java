package mon.web.model;

import java.util.List;

import mon.model.EJCICLogFile;

/**
 * <pre> MONEJ01VO.java </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul> <li>2003/06/30,PGU,NEW </ul>
 */
public class MONEJ06VO {
	boolean ok = false;
	// input
	EJCICLogFile input;

	// output

	List<EJCICLogFile> dataList;

	public boolean isOk() {
		return this.ok;
	}

	/**
	 * Sets the ok
	 * 
	 * @param ok
	 *            the ok to set
	 */
	public void setOk(boolean ok) {
		this.ok = ok;
	}

	/**
	 * Returns the input
	 * 
	 * @return the input
	 */
	public EJCICLogFile getInput() {
		return this.input;
	}

	/**
	 * Sets the input
	 * 
	 * @param input
	 *            the input to set
	 */
	public void setInput(EJCICLogFile input) {
		this.input = input;
	}

	/**
	 * Returns the dataList
	 * 
	 * @return the dataList
	 */
	public List<EJCICLogFile> getDataList() {
		return this.dataList;
	}

	/**
	 * Sets the dataList
	 * 
	 * @param dataList
	 *            the dataList to set
	 */
	public void setDataList(List<EJCICLogFile> dataList) {
		this.dataList = dataList;
	}

}
