package etch.dao.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import etch.dao.ReportDao;
import etch.jdbc.ETCHJdbcTemplate;

/**
 * <pre>
 * ReportDaoImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Repository("reportDao")
public class ReportDaoImpl implements ReportDao {
	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	private ETCHJdbcTemplate jdbc;

	@Override
	public List<Map<String, Object>> getReport01_02(String ym1, String ym2, String division) {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT ");
		sql.append("    DIVISION , ");
		sql.append("    COUNT(*) AS TTL_COUNT , ");
		sql.append("    SUM(price) AS TTL_PRICE ");
		sql.append("FROM ");
		sql.append("    LOGFILE ");
		sql.append("WHERE ");
		sql.append("    QDATE >= ? ");
		sql.append("    AND QDATE <= ? ");
		sql.append("    AND RC = '0000' ");
		sql.append("    AND TOTCH = 'Y' ");

		if (StringUtils.isNotEmpty(division)) {
			sql.append("    AND DIVISION = ? ");
		}

		sql.append("GROUP BY ");
		sql.append("   DIVISION ");
		sql.append("ORDER BY DIVISION");

		Object[] args = null;
		if (StringUtils.isNotEmpty(division)) {
			args = new Object[] { ym1, ym2, division };
		} else {
			args = new Object[] { ym1, ym2 };
		}

		return this.jdbc.queryForListWithMax(sql.toString(), args);
	}

	@Override
	public List<Map<String, Object>> getReport01_02History(String ym1, String ym2, String division) {

		StringBuffer sql = new StringBuffer();
		sql.append("SELECT ");
		sql.append("    INQ.RECEIVE_STATUS , ");
		sql.append("    INQT.BRANCH_CODE as DIVISION, ");
		sql.append("    COUNT(*) as TTL_COUNT, ");
		sql.append("    SUM(FEE.CHARGE_POINT) as TTL_PRICE ");
		sql.append("FROM ");
		sql.append("    TB_INQUIRY INQ ");
		sql.append("    LEFT JOIN TB_INQUIRY_FEE FEE   ON INQ.ITEM_DEFINITION = FEE.IID ");
		sql.append("    LEFT JOIN TB_INQUIRY_TASK INQT ON INQT.TID=INQ.TID ");
		sql.append("WHERE ");
		sql.append("    INQ.SENT_TIME >= ? ");
		sql.append("    AND INQ.SENT_TIME < ? ");
		sql.append("    AND INQ.RECEIVE_STATUS=1 ");
		sql.append("    AND INQ.SEND_STATUS =1 ");

		if (StringUtils.isNotEmpty(division)) {
			sql.append("    AND INQT.BRANCH_CODE = ? ");
		}

		sql.append("GROUP BY ");
		sql.append("    INQT.BRANCH_CODE , ");
		sql.append("    INQ.RECEIVE_STATUS ");
		sql.append("ORDER BY ");
		sql.append("    INQT.BRANCH_CODE , ");
		sql.append("    INQ.RECEIVE_STATUS  ");

		Object[] args = null;
		if (StringUtils.isNotEmpty(division)) {
			args = new Object[] { ym1, ym2, division };
		} else {
			args = new Object[] { ym1, ym2 };
		}

		return this.jdbc.queryForListWithMax(sql.toString(), args);
	}

	@Override
	public List<Map<String, Object>> getReport03(String ymd1, String ymd2, String brno, String order, int maxRows) {

		String sql = " select QDATE,UCNAME,REQUESTID,CHECKER,CHECKERID,TXID,TOTCH,PRICE,";
		sql += "QUERYKEY1,QUERYKEY2,QUERYKEY3,QUERYKEY4,QUERYKEY5 from LOGFILE";
		sql += " where qdate >= ? and qdate <= ? " + " and division = ? and rc='0000' ";

		if (order.toUpperCase().equals("REQUESTID")) {
			sql += " order by requestid,qdate,querykey1,msgid,txid ";
		} else if (order.toLowerCase().equals("QUERYKEY")) {
			sql += " order by querykey1,qdate,requestid,msgid,txid ";
		} else {
			sql += " order by qdate,requestid,querykey1,msgid,txid ";
		}

		return this.jdbc.queryForList(sql, new Object[] { ymd1, ymd2, brno }, 0, maxRows);
	}

	@Override
	public List<Map<String, Object>> getReport03History(String ymd1, String ymd2, String brno, String order, int maxRows) {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT ");
		sql.append("	SUBSTR(INQ.SENT_TIME,1,10) as QDATE, ");
		sql.append("    INQ.RECEIVE_STATUS as TOTCH, ");
		sql.append("    INQT.BRANCH_CODE, ");
		sql.append("    FEE.CHARGE_POINT as PRICE, ");
		sql.append("    INQT.USER_ID as REQUESTID, ");
		sql.append("    U1.REALNAME as UCNAME, ");
		sql.append("    INQT.CHECKED , ");
		sql.append("    INQT.CHECKUSERID as CHECKERID, ");
		sql.append("    U2.REALNAME as CHECKER, ");
		sql.append("    IDEF.ITEM_CODE as TXID, ");
		sql.append("    INQ.MAIN_INQUIRY_KEYS as QUERYKEY1, ");
		sql.append("    INQ.ALT_INQUIRY_KEYS as QUERYKEY2 ");
		sql.append(" FROM ");
		sql.append("    ETCHAP1.TB_INQUIRY INQ ");
		sql.append("    LEFT JOIN TB_INQUIRY_FEE FEE   ON INQ.ITEM_DEFINITION = FEE.IID ");
		sql.append("    LEFT JOIN TB_ITEM_DEFINITION IDEF  ON INQ.ITEM_DEFINITION = IDEF.IID ");
		sql.append("    LEFT JOIN TB_INQUIRY_TASK INQT ON INQT.TID=INQ.TID ");
		sql.append("    LEFT JOIN USERS U1 ON U1.LOGINNAME=INQT.USER_ID ");
		sql.append("    LEFT JOIN USERS U2 ON U2.LOGINNAME=INQT.CHECKUSERID ");
		sql.append(" WHERE ");
		sql.append("    INQ.SENT_TIME >= ? ");
		sql.append("    AND INQ.SENT_TIME < ? ");
		sql.append("    AND (INQ.RECEIVE_STATUS=1 OR INQ.RECEIVE_STATUS=2) ");
		sql.append("    AND (INQ.SEND_STATUS =1 OR INQ.SEND_STATUS =2) ");
		sql.append("    AND INQT.BRANCH_CODE = ? ");
		sql.append(" ORDER BY ");
		sql.append("	INQ.SENT_TIME, ");
		sql.append("	INQT.USER_ID, ");
		sql.append("    INQT.BRANCH_CODE , ");
		sql.append("    INQ.RECEIVE_STATUS ");

		return this.jdbc.queryForList(sql.toString(), new Object[] { ymd1, ymd2, brno }, 0, maxRows);
	}

	@Override
	public int getReport03Count(String ymd1, String ymd2, String brno) {
		String sql = " select COUNT(*) from LOGFILE where qdate >= ? and qdate <= ?  and division = ? and rc='0000' ";

		return this.jdbc.queryForInt(sql, new Object[] { ymd1, ymd2, brno });
	}

	@Override
	public int getReport03HistoryCount(String ymd1, String ymd2, String brno) {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT 	COUNT(*) ");
		sql.append("FROM ");
		sql.append("    TB_INQUIRY INQ ");
		sql.append("    LEFT JOIN TB_INQUIRY_FEE FEE   ON INQ.ITEM_DEFINITION = FEE.IID ");
		sql.append("    LEFT JOIN TB_ITEM_DEFINITION IDEF  ON INQ.ITEM_DEFINITION = IDEF.IID ");
		sql.append("    LEFT JOIN TB_INQUIRY_TASK INQT ON INQT.TID=INQ.TID ");
		sql.append("    LEFT JOIN USERS U1 ON U1.LOGINNAME=INQT.USER_ID ");
		sql.append("    LEFT JOIN USERS U2 ON U2.LOGINNAME=INQT.CHECKUSERID ");
		sql.append("WHERE ");
		sql.append("    INQ.SENT_TIME >= ? ");
		sql.append("    AND INQ.SENT_TIME < ? ");
		sql.append("    AND (INQ.RECEIVE_STATUS=1 OR INQ.RECEIVE_STATUS=2) ");
		sql.append("    AND (INQ.SEND_STATUS =1 OR INQ.SEND_STATUS =2) ");
		sql.append("    AND INQT.BRANCH_CODE = ? ");

		return this.jdbc.queryForInt(sql.toString(), new Object[] { ymd1, ymd2, brno });
	}

	// @Override
	// public List<Map<String, Object>> queryAuditLog() {
	// String sql =
	// " select coalesce(checker,'') as checker,qdate,msgid,ucname,txid,price,querykey1,querykey2,querykey3,querykey4,querykey5"
	// + " from AUDITLOG "
	// + " where coalesce(checker,'')='' and totch='Y' and RC='0000'"
	// + " order by qdate desc";
	//
	// return this.jdbc.queryForList(sql, new Object[] {});
	// }
	//
	// @Override
	// public int updateAuditLog(String tableName, String checker, String checkerId, List paramItem) {
	//
	// long t1 = System.currentTimeMillis();
	// int resultCnt = 0;
	//
	// String sql = "";
	// String[] fields = null;
	//
	// for (int i = 0; i < paramItem.size(); i++) {
	// String item = (String) paramItem.get(i);
	// StringTokenizer st = new StringTokenizer(item, "&");
	// fields = new String[3];
	// for (int j = 0; j < 3 && st.hasMoreTokens(); j++) {
	// fields[j] = st.nextToken();
	// }
	//
	// sql = "update " + tableName + " set checker=?,checkerId=? where qdate = ? and msgid =? and txid =?";
	//
	// resultCnt += this.jdbc.update(sql, new Object[] { checker, checkerId, fields[0], fields[1], fields[2] });
	//
	// }
	//
	// return resultCnt;
	// }
}
