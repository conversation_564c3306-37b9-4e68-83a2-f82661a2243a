package etch.model;

import etch.service.ModelMetaService;
import etch.service.impl.SpringContextHelper;

/**
 * <pre>
 * BaseModel.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,new
 *          </ul>
 */
public abstract class AbstractModelMeta implements ModelMeta {
	ModelMetaService modelServie;
	ModelMetaData bmo;

	public AbstractModelMeta() {
		this.modelServie = SpringContextHelper.getService(ModelMetaService.class);
		this.bmo = this.modelServie.getCacheData(this.getClass());
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.model.BaseModel#toInsertArgs()
	 */
	@Override
	public Object[] toInsertArgs() {
		return this.modelServie.getArgsByNames(this, this.bmo.getInsertArgsName());
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.model.BaseModel#toKeyArgs()
	 */
	@Override
	public Object[] toKeyArgs() {
		return this.modelServie.getArgsByNames(this, this.bmo.getKeyArgsName());
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.model.BaseModel#toUpdateByKeyArgs()
	 */
	@Override
	public Object[] toUpdateByKeyArgs() {
		return this.modelServie.getArgsByNames(this, this.bmo.getUpdateByKeyArgsName());
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.model.BaseModel#getInsertSql()
	 */
	@Override
	public String getInsertSql() {
		return this.bmo.getInsertSql();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.model.BaseModel#getUpdateByKeySql()
	 */
	@Override
	public String getUpdateByKeySql() {
		return this.bmo.getUpdateByKeySql();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.model.BaseModel#getDeleteByKeySql()
	 */
	@Override
	public String getDeleteByKeySql() {
		return this.bmo.getDeleteByKeySql();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.model.BaseModel#getFindAllSql()
	 */
	@Override
	public String getFindAllSql() {
		return this.bmo.getFindAllSql();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.model.BaseModel#getFindByKeySql()
	 */
	@Override
	public String getFindByKeySql() {
		return this.bmo.getFindByKeySql();
	}

}
