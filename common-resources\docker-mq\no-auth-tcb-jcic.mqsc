DEFINE CHANNEL('MQJ006D.SVRCONN.CH0') CHLTYPE(SVRCONN) TRPTYPE(TCP) DESCR('Server connection channel for MQJ006D CH0')
DEFINE CHANNEL('MQJ006D.SVRCONN.JCIC') CHLTYPE(SVRCONN) TRPTYPE(TCP) DESCR('Server connection channel for MQJ006D JCIC')

DEFINE QLOCAL('MQJ006D.MONITOR.QL') DESCR('Local queue for monitoring')
DEFINE QLOCAL('MQJ006D.JMET.QR') DESCR('Local queue for JMET')
DEFINE QLOCAL('MQJ006D.006IF01.QL') DESCR('Local queue for 006IF01')
DEFINE QLOCAL('MQJ006D.WAS2DIS.QL') DESCR('Local queue for WAS2DIS')
DEFINE QLOCAL('MQJ006D.DIS2WAS.QL') DESCR('Local queue for DIS2WAS')
DEFINE QLOCAL('MQJ006D.JCICONN.QL') DESCR('Local queue for JCICONN')

* 禁用通道認證 (CHLAUTH)，允許客戶端連線
ALTER QMGR CHLAUTH(DISABLED)

* 禁用連線認證 (CONNAUTH)，移除認證要求
ALTER QMGR CONNAUTH('')

* 配置通道 MQJ006D.SVRCONN.CH0，設定 MCAUSER 為 'mqm'，並將 SSL 認證設為可選
ALTER CHANNEL('MQJ006D.SVRCONN.CH0') CHLTYPE(SVRCONN) MCAUSER('mqm') SSLCAUTH(OPTIONAL)

* 配置通道 MQJ006D.SVRCONN.JCIC，設定 MCAUSER 為 'mqm'，並將 SSL 認證設為可選
ALTER CHANNEL('MQJ006D.SVRCONN.JCIC') CHLTYPE(SVRCONN) MCAUSER('mqm') SSLCAUTH(OPTIONAL)

* 為 'mqm' 使用者授予 Queue Manager 級別的所有權限
* 這一步確保 'mqm' 使用者可以執行所有 Queue Manager 操作（例如連線、查詢等），避免權限不足的問題
SET AUTHREC OBJTYPE(QMGR) PRINCIPAL('mqm') AUTHADD(ALL)

* 為 'mqm' 使用者授予所有 Queue 的所有權限
* PROFILE('**')：使用通配符匹配所有 Queue（包括現有和未來創建的 Queue）
* 這一步確保 'mqm' 使用者可以對所有 Queue 執行所有操作（例如 PUT、GET 等），避免存取 Queue 時的權限問題
SET AUTHREC PROFILE('**') OBJTYPE(QUEUE) PRINCIPAL('mqm') AUTHADD(ALL)

* 為 'admin' 使用者授予 Queue Manager 級別的所有權限
SET AUTHREC OBJTYPE(QMGR) PRINCIPAL('admin') AUTHADD(ALL)

* 為 'admin' 使用者授予所有 Queue 的存取權限（可選）
SET AUTHREC PROFILE('**') OBJTYPE(QUEUE) PRINCIPAL('admin') AUTHADD(ALL)

* 為了使SECURITY生效
REFRESH SECURITY TYPE(AUTHSERV)
REFRESH SECURITY TYPE(CONNAUTH)