package mon.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import mon.dao.ETCHLogFileDao;
import mon.jdbc.JdbcSqlUtils;
import mon.model.ETCHLogFile;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * <pre>
 * LogfileDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("ETCHLogFileDao")
public class ETCHLogFileDaoImpl implements ETCHLogFileDao {
	protected final Logger logger = LoggerFactory.getLogger(ETCHLogFileDaoImpl.class);

	@Resource
	@Qualifier("etchJcbcTemplate")
	private JdbcTemplate jdbc;

	@Override
	public List<ETCHLogFile> query(ETCHLogFile model) {
		List<String> strList = new ArrayList<String>();
		StringBuffer whereStr = new StringBuffer();

		if (StringUtils.isNotBlank(model.getQdate())) {
			whereStr.append(" AND QDATE = ? ");
			strList.add(model.getQdate());
		}

		if (StringUtils.isNotBlank(model.getTxid())) {
			whereStr.append(" AND TXID = ? ");
			strList.add(model.getTxid());
		}

		if (StringUtils.isNotBlank(model.getMsgid())) {
			whereStr.append(" AND MSGID = ? ");
			strList.add(model.getMsgid());
		}

		if (StringUtils.isNotBlank(model.getRequestid())) {
			whereStr.append(" AND REQUESTID = ? ");
			strList.add(model.getRequestid());
		}

		if (StringUtils.isNotBlank(model.getDivision())) {
			whereStr.append(" AND DIVISION = ? ");
			strList.add(model.getDivision());
		}

		if (StringUtils.isNotBlank(model.getQuerykey1())) {
			whereStr.append(" AND QUERYKEY1 = ? ");
			strList.add(model.getQuerykey1());
		}

		if (StringUtils.isNotBlank(model.getQuerykey2())) {
			whereStr.append(" AND QUERYKEY2 = ? ");
			strList.add(model.getQuerykey2());
		}

		if (StringUtils.isNotBlank(model.getQuerykey3())) {
			whereStr.append(" AND QUERYKEY3 = ? ");
			strList.add(model.getQuerykey3());
		}

		if (StringUtils.isNotBlank(model.getQuerykey4())) {
			whereStr.append(" AND QUERYKEY4 = ? ");
			strList.add(model.getQuerykey4());
		}

		if (StringUtils.isNotBlank(model.getQuerykey5())) {
			whereStr.append(" AND QUERYKEY5 = ? ");
			strList.add(model.getQuerykey5());
		}

		if (StringUtils.isNotBlank(model.getRc())) {
			if ("0000".equals(model.getRc()) || "0001".equals(model.getRc())) {
				whereStr.append(" AND RC = ? ");
				strList.add(model.getRc());
			} else {
				whereStr.append(" AND  RC <> '0000' AND  RC <> '0001' ");
			}
		}

		String sql = " select * from LOGFILE where 1=1 " + whereStr + " order by QDATE asc with ur";
		JdbcSqlUtils.printSql(sql, strList.toArray());

		long t1 = System.currentTimeMillis();
		try {
			return this.jdbc.query(sql, strList.toArray(),
					ParameterizedBeanPropertyRowMapper.newInstance(ETCHLogFile.class));
		} finally {
			logger.debug("JdbcTemplate spend {} ms", System.currentTimeMillis() - t1);
		}
	}

}