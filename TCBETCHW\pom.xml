<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>TCBETCHW</groupId>
	<artifactId>TCBETCHW</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>war</packaging>

	<properties>
		<java.version>1.6</java.version>
		<project.build.sourceEncoding>MS950</project.build.sourceEncoding>
		<project.reporting.outputEncoding>MS950</project.reporting.outputEncoding>
	</properties>

	<dependencies>
		<!-- AOP Alliance -->
		<dependency>
			<groupId>aopalliance</groupId>
			<artifactId>aopalliance</artifactId>
			<version>1.0</version>
		</dependency>

		<!-- AspectJ -->
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.6.6</version>
		</dependency>

		<!-- Axiom -->
		<dependency>
			<groupId>org.apache.ws.commons.axiom</groupId>
			<artifactId>axiom-api</artifactId>
			<version>1.2.21</version>
		</dependency>
		<dependency>
			<groupId>org.apache.ws.commons.axiom</groupId>
			<artifactId>axiom-impl</artifactId>
			<version>1.2.21</version>
		</dependency>

		<!-- Axis2 -->
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-adb</artifactId>
			<version>1.7.9</version>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-kernel</artifactId>
			<version>1.7.9</version>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-transport-http</artifactId>
			<version>1.7.9</version>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-transport-local</artifactId>
			<version>1.7.9</version>
		</dependency>

		<!-- C3P0 -->
		<dependency>
			<groupId>c3p0</groupId>
			<artifactId>c3p0</artifactId>
			<version>0.9.1.2</version>
		</dependency>

		<!-- CGLib -->
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib-nodep</artifactId>
			<version>2.1_3</version>
		</dependency>

		<!-- Apache Commons -->
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.8.3</version>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.1</version>
		</dependency>
		<dependency>
			<groupId>commons-configuration</groupId>
			<artifactId>commons-configuration</artifactId>
			<version>1.9</version>
		</dependency>
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.2.1</version>
		</dependency>
		<dependency>
			<groupId>commons-discovery</groupId>
			<artifactId>commons-discovery</artifactId>
			<version>0.2</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.2.2</version>
		</dependency>
		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>3.1</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.4</version>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.1.1</version>
		</dependency>
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>2.0</version>
		</dependency>
		<dependency>
			<groupId>commons-pool</groupId>
			<artifactId>commons-pool</artifactId>
			<version>1.5.4</version>
		</dependency>

		<!-- Dom4j -->
		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>1.6.1</version>
		</dependency>

		<!-- HttpCore -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.6</version>
		</dependency>

		<!-- ICU4J -->
		<dependency>
			<groupId>com.ibm.icu</groupId>
			<artifactId>icu4j</artifactId>
			<version>4.8.2</version>
		</dependency>

		<!-- Jasypt -->
		<dependency>
			<groupId>org.jasypt</groupId>
			<artifactId>jasypt</artifactId>
			<version>1.8</version>
		</dependency>

		<!-- JSON -->
		<dependency>
			<groupId>org.glassfish</groupId>
			<artifactId>javax.json</artifactId>
			<version>1.0.4</version>
		</dependency>
		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
			<version>1.1.1</version>
		</dependency>

		<!-- Jaxen -->
		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
			<version>1.1.4</version>
		</dependency>

		<!-- JSTL -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>1.2</version>
		</dependency>

		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>javax.servlet.jsp-api</artifactId>
			<version>2.3.3</version>
			<scope>provided</scope>
		</dependency>

		<!-- Servlet API -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.0.1</version>
			<scope>provided</scope>
		</dependency>

		<!-- Log4j -->
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.15</version>
			<exclusions>
				<exclusion>
					<groupId>com.sun.jdmk</groupId>
					<artifactId>jmxtools</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jmx</groupId>
					<artifactId>jmxri</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.jms</groupId>
					<artifactId>jms</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- MX4J -->
		<dependency>
			<groupId>mx4j</groupId>
			<artifactId>mx4j-tools</artifactId>
			<version>3.0.1</version>
		</dependency>

		<!-- Neethi -->
		<dependency>
			<groupId>org.apache.neethi</groupId>
			<artifactId>neethi</artifactId>
			<version>3.0.3</version>
		</dependency>

		<!-- Quartz -->
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>1.8.5</version>
		</dependency>

		<!-- SLF4J -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.5.10</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.5.10</version>
		</dependency>

		<!-- Spring Framework -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-instrument</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-instrument-tomcat</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jms</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-oxm</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>3.2.0.RELEASE</version>
		</dependency>

		<!-- Woden -->
		<dependency>
			<groupId>org.apache.woden</groupId>
			<artifactId>woden-core</artifactId>
			<version>1.0M10</version>
		</dependency>

		<!-- WSDL4J -->
		<dependency>
			<groupId>wsdl4j</groupId>
			<artifactId>wsdl4j</artifactId>
			<version>1.6.2</version>
		</dependency>

		<!-- Xerces -->
		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
			<version>2.11.0</version>
		</dependency>

		<!-- XMLSchema -->
		<dependency>
			<groupId>org.apache.ws.xmlschema</groupId>
			<artifactId>xmlschema-core</artifactId>
			<version>2.2.1</version>
		</dependency>

		<dependency>
			<groupId>xml-apis</groupId>
			<artifactId>xml-apis</artifactId>
			<version>1.4.01</version>
		</dependency>

		<!-- EJMN使用之mina -->
		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-core</artifactId>
			<version>2.0.5</version>
		</dependency>

		<!-- 自定義JAR -->
		<!-- MQ JARs -->
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>commonservices</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/com.ibm.mq.commonservices.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>defaultconfig</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/com.ibm.mq.defaultconfig.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>headers</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/com.ibm.mq.headers.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>mq</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/com.ibm.mq.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>jmqi</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/com.ibm.mq.jmqi.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>pcf</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/com.ibm.mq.pcf.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>mqjms</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/com.ibm.mqjms.jar</systemPath>
		</dependency>

		<!-- Notes JAR -->
		<dependency>
			<groupId>com.ibm.notes</groupId>
			<artifactId>ncso</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/NCSO.jar</systemPath>
		</dependency>

		<!-- Root directory JARs -->
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>applet</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/applet.jar</systemPath>
		</dependency>
		<!-- Tomcat5的lib -->
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>catalina</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/catalina.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>common</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/Common.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>connector</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/connector.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>dhbcore</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/dhbcore.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>dom</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/dom.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>jaxp-api</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/jaxp-api.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>jms</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/jms.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>mnm-commons</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/mnm-commons.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>pjl-comp-filter</artifactId>
			<version>1.8.1</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/pjl-comp-filter-1.8.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>sax</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/sax.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.ejcic.custom</groupId>
			<artifactId>security</artifactId>
			<version>1.0.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/../common-resources/lib/security.jar</systemPath>
		</dependency>

	</dependencies>

	<build>
		<!-- 以下版本都調整過適合jdk1.6 -->
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.0.0</version>
				<configuration>
					<warSourceDirectory>WebContent</warSourceDirectory>
					<webResources>
						<resource>
							<directory>${project.basedir}/../common-resources/lib</directory>
							<targetPath>WEB-INF/lib</targetPath>
							<includes>
								<include>**/*.jar</include>
							</includes>
							<excludes>
								<!-- WAS上面會有這個jar檔 -->
								<exclude>**/db2jcc4.jar</exclude>
								<!-- 跟上面的jar重複了，放這裡是為了jetty載入用 -->
								<exclude>**/c3p0-0.9.1.2.jar</exclude>
							</excludes>
						</resource>
					</webResources>
					<archive>
						<manifestFile>WebContent/META-INF/MANIFEST.MF</manifestFile>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-clean-plugin</artifactId>
				<version>2.6.1</version>
			</plugin>
		</plugins>
	</build>
</project>