<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
           http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd ">

	<!-- <context:annotation-config /> -->
	<bean id="configService" class="etch.service.impl.ConfigServiceImpl">
		<property name="location" value="classpath:etch.properties" />
	</bean>

	<!-- @Service -->
	<context:component-scan base-package="etch.service.impl.**">
		<context:exclude-filter type="regex"
			expression="etch.service.impl.ConfigServiceImpl" />
	</context:component-scan>

	<context:component-scan base-package="etch.jdbc.**" />
	<context:component-scan base-package="etch.dao.impl.**" />
</beans>