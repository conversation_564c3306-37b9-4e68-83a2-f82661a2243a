* 禁用通道認證 (CHLAUTH)，允許客戶端不提供認證資訊即可連線
* 這一步移除通道級別的認證檢查，確保連線不會因缺少使用者 ID 或密碼而被拒絕
ALTER QMGR CHLAUTH(DISABLED)

* 禁用連線認證 (CONNAUTH)，移除 Queue Manager 對客戶端認證的要求
* 這一步確保 Queue Manager 不會要求客戶端提供使用者 ID 和密碼，解決了 CHCKCLNT(REQDADM) 導致的認證失敗問題
ALTER QMGR CONNAUTH('')

* 修改預設服務器連線通道 (DEV.APP.SVRCONN)，設定 MCAUSER 為 'mqm'，並將 SSL 認證設為可選
* MCAUSER('mqm')：將所有連線映射到 'mqm' 使用者，確保連線以 'mqm' 身份執行
* SSLCAUTH(OPTIONAL)：允許客戶端不提供 SSL/TLS 證書即可連線，解決了 SSLCAUTH(REQUIRED) 導致的認證失敗問題
ALTER CHANNEL(DEV.APP.SVRCONN) CHLTYPE(SVRCONN) MCAUSER('mqm') SSLCAUTH(OPTIONAL)

* 為 'mqm' 使用者授予 Queue Manager 級別的所有權限
* 這一步確保 'mqm' 使用者可以執行所有 Queue Manager 操作（例如連線、查詢等），避免權限不足的問題
SET AUTHREC OBJTYPE(QMGR) PRINCIPAL('mqm') AUTHADD(ALL)

* 為 'mqm' 使用者授予所有 Queue 的所有權限
* PROFILE('**')：使用通配符匹配所有 Queue（包括現有和未來創建的 Queue）
* 這一步確保 'mqm' 使用者可以對所有 Queue 執行所有操作（例如 PUT、GET 等），避免存取 Queue 時的權限問題
SET AUTHREC PROFILE('**') OBJTYPE(QUEUE) PRINCIPAL('mqm') AUTHADD(ALL)

* 為 'admin' 使用者授予 Queue Manager 級別的所有權限
SET AUTHREC OBJTYPE(QMGR) PRINCIPAL('admin') AUTHADD(ALL)

* 為 'admin' 使用者授予所有 Queue 的存取權限（可選）
SET AUTHREC PROFILE('**') OBJTYPE(QUEUE) PRINCIPAL('admin') AUTHADD(ALL)

* 為了使SECURITY生效
REFRESH SECURITY TYPE(AUTHSERV)
REFRESH SECURITY TYPE(CONNAUTH)