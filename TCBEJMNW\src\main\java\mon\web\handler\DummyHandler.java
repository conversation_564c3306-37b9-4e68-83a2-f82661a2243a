package mon.web.handler;

import java.util.Map;

import mon.exception.AppException;
import mon.web.core.AbstractHandler;
import mon.web.core.WebContext;
import mon.web.core.WebView;


/**
 * <pre> DummyHandler.java </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul> <li>2003/06/30,PGU,NEW </ul>
 */
public class DummyHandler extends AbstractHandler {

	/*
	 * (non-Javadoc)
	 * @see etch.web.core.Handler#action(etch.web.core.WebContext, java.util.Map)
	 */
	@Override
	public WebView action(WebContext context, Map<String, String> paramMap) throws AppException {
		return null;
	}
}
