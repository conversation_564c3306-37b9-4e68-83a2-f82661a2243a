
tcb-j006

cmd:
docker build -f Dockerfile-tcb-j006 -t ibm-mqserver .

cmd:
docker run --name mq-j006 --env LICENSE=accept --env MQ_QMGR_NAME=MQJ006D --env MQ_ADMIN_PASSWORD=passw0rd --publish 1414:1414 --publish 9443:9443 --volume mq-j006-data:/mnt/mqm --detach ibm-mqserver

如果執行過程中有發現少了什麼queue可以直接登入然後建立

docker exec -it mq-j006 /bin/bash
進入執行
runmqsc MQJ006D
輸入缺少的QUEUE PS. 記得執行了幫我加到no-auth-tcb-j006.mqsc中，這樣下次別人起就不用特別加
DEFINE QLOCAL('MQJ006D.JCICONN.QL') DESCR('Local queue for JCICONN')


tcb-etch

待完成......
cmd:
docker build -f Dockerfile-tcb-etch -t ibm-mqserver .

cmd:
docker run --name mq-etch --env LICENSE=accept --env MQ_QMGR_NAME=MQT006D --env MQ_ADMIN_PASSWORD=passw0rd --publish 1414:1414 --publish 9443:9443 --volume mq-etch-data:/mnt/mqm --detach ibm-mqserver
