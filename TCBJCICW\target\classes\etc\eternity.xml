<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd">
	
	<bean id="Core" class="com.alfalumia.eternity.core.Core">
		<constructor-arg value="core"/>
		<!-- <constructor-arg value="9960:+4:4"/> -->
		<constructor-arg value="9960:-4:4"/>
		<property name="domain" value="eternity"/>
		<property name="modulesDirs" value="modules|modules.tcb"/>
		<property name="modulesDataDir" value="/home/<USER>/modulesdata"/>
	</bean>

</beans>