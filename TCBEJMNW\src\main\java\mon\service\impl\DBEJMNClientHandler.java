package mon.service.impl;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * AgentClientHandler.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class DBEJMNClientHandler extends IoHandlerAdapter {
	private static final Logger logger = LoggerFactory.getLogger(DBEJMNClientHandler.class);

	private final String values;

	private boolean finished;

	private StringBuffer result = new StringBuffer();

	public DBEJMNClientHandler(String values) {
		this.values = values;
	}

	/**
	 * Returns the finished
	 * 
	 * @return the finished
	 */
	public boolean isFinished() {
		return finished;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.apache.mina.core.service.IoHandlerAdapter#exceptionCaught(org.apache.mina.core.session.IoSession,
	 * java.lang.Throwable)
	 */
	@Override
	public void exceptionCaught(IoSession session, Throwable cause) throws Exception {
		logger.error("[exceptionCaught]{}" + ToStringBuilder.reflectionToString(session), cause);
		session.close(true);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.apache.mina.core.service.IoHandlerAdapter#messageReceived(org.apache.mina.core.session.IoSession,
	 * java.lang.Object)
	 */
	@Override
	public void messageReceived(IoSession session, Object message) throws Exception {
		this.result.append((String) message).append("\n");
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.apache.mina.core.service.IoHandlerAdapter#sessionOpened(org.apache.mina.core.session.IoSession)
	 */
	@Override
	public void sessionOpened(IoSession session) throws Exception {
		session.write(values);

	}

	/**
	 * Returns the result
	 * 
	 * @return the result
	 */
	public String getResult() {
		return result.toString();
	}

}
