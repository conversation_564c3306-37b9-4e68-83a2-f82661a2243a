package ejcic.msg;

import ejcic.disp.QueueIdHelper;
import ejcic.service.ConfigService;
import ejcic.service.Env;
import ejcic.service.impl.ConfigServiceImpl;
import ejcic.utils.DateTimeUtils;

/**
 * <pre>
 * GWMessage.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class GWMessage extends HeaderMessage {
	public GWMessage() {
	}

	public GWMessage(String txid) {
		ConfigService configService = ConfigServiceImpl.getInstance();
		this.setItem(Message.F_HBANK, configService.getProperty(Env.SYS_BANK_ID));
		this.setItem(Message.F_HBRANCH, configService.getProperty(Env.SYS_BRANCH_ID));
		this.setItem(Message.F_HSYSDATE, DateTimeUtils.getDate("yyyyMMdd"));
		this.setItem(Message.F_HSYSTIME, DateTimeUtils.getDate("hhmmss"));
		this.setItem(Message.F_HTXID, txid);
		this.setItem(Message.F_HQID, new QueueIdHelper().getQueueId());
		this.setItem(Message.F_HMSGID1, "0");
		this.setItem(Message.F_HMSGID2, "0");
		this.setItem(Message.F_HSEQID, "000000");
		this.setItem(Message.F_HERRCODE, "000000");
		this.setItem(Message.F_HCHARGE, configService.getProperty(Env.SYS_BRANCH_ID));
		this.setItem(Message.F_HREQID, "0");
		this.setItem(Message.F_HRESV, "0");
	}

}
