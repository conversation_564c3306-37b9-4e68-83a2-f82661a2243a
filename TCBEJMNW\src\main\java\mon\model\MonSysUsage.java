package mon.model;

import java.sql.Timestamp;

/**
 * <pre>
 * MonSysUsageTable.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@TModel(tbName = "MON_SYSUSAGE")
public class MonSysUsage extends AbstractModelMeta {
	public static final int INIT = -9;

	@TKeyField
	private String sysId;

	@TKeyField
	private String schSno;

	@TKeyField
	private String hostId = "";

	@TField
	private int cpu_ap = INIT;

	@TField
	private int cpu_db = INIT;

	@TField
	private int mem_web = INIT;

	@TField
	private int mem_web_max = INIT;

	@TField
	private int mem_disp1 = INIT;

	@TField
	private int mem_disp1_max = INIT;

	@TField
	private int mem_disp2 = INIT;

	@TField
	private int mem_disp2_max = INIT;

	@TField
	private int mem_gw = INIT;

	@TField
	private int mem_gw_max = INIT;

	@TField
	private Timestamp runts = new Timestamp(System.currentTimeMillis());

	@TField
	private String vmstat_ap = "";

	@TField
	private String vmstat_db = "";

	/**
	 * Returns the sysId
	 * 
	 * @return the sysId
	 */
	public String getSysId() {
		return this.sysId;
	}

	/**
	 * Sets the sysId
	 * 
	 * @param sysId
	 *            the sysId to set
	 */
	public void setSysId(String sysId) {
		this.sysId = sysId;
	}

	/**
	 * Returns the schSno
	 * 
	 * @return the schSno
	 */
	public String getSchSno() {
		return this.schSno;
	}

	/**
	 * Sets the schSno
	 * 
	 * @param schSno
	 *            the schSno to set
	 */
	public void setSchSno(String schSno) {
		this.schSno = schSno;
	}

	/**
	 * Returns the hostId
	 * 
	 * @return the hostId
	 */
	public String getHostId() {
		return this.hostId;
	}

	/**
	 * Sets the hostId
	 * 
	 * @param hostId
	 *            the hostId to set
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	/**
	 * Returns the cpu_ap
	 * 
	 * @return the cpu_ap
	 */
	public int getCpu_ap() {
		return this.cpu_ap;
	}

	/**
	 * Sets the cpu_ap
	 * 
	 * @param cpu_ap
	 *            the cpu_ap to set
	 */
	public void setCpu_ap(int cpu_ap) {
		this.cpu_ap = cpu_ap;
	}

	/**
	 * Returns the cpu_db
	 * 
	 * @return the cpu_db
	 */
	public int getCpu_db() {
		return this.cpu_db;
	}

	/**
	 * Sets the cpu_db
	 * 
	 * @param cpu_db
	 *            the cpu_db to set
	 */
	public void setCpu_db(int cpu_db) {
		this.cpu_db = cpu_db;
	}

	/**
	 * Returns the mem_web
	 * 
	 * @return the mem_web
	 */
	public int getMem_web() {
		return this.mem_web;
	}

	/**
	 * Sets the mem_web
	 * 
	 * @param mem_web
	 *            the mem_web to set
	 */
	public void setMem_web(int mem_web) {
		this.mem_web = mem_web;
	}

	/**
	 * Returns the mem_web_max
	 * 
	 * @return the mem_web_max
	 */
	public int getMem_web_max() {
		return this.mem_web_max;
	}

	/**
	 * Sets the mem_web_max
	 * 
	 * @param mem_web_max
	 *            the mem_web_max to set
	 */
	public void setMem_web_max(int mem_web_max) {
		this.mem_web_max = mem_web_max;
	}

	/**
	 * Returns the mem_disp1
	 * 
	 * @return the mem_disp1
	 */
	public int getMem_disp1() {
		return this.mem_disp1;
	}

	/**
	 * Sets the mem_disp1
	 * 
	 * @param mem_disp1
	 *            the mem_disp1 to set
	 */
	public void setMem_disp1(int mem_disp1) {
		this.mem_disp1 = mem_disp1;
	}

	/**
	 * Returns the mem_disp1_max
	 * 
	 * @return the mem_disp1_max
	 */
	public int getMem_disp1_max() {
		return this.mem_disp1_max;
	}

	/**
	 * Sets the mem_disp1_max
	 * 
	 * @param mem_disp1_max
	 *            the mem_disp1_max to set
	 */
	public void setMem_disp1_max(int mem_disp1_max) {
		this.mem_disp1_max = mem_disp1_max;
	}

	/**
	 * Returns the mem_disp2
	 * 
	 * @return the mem_disp2
	 */
	public int getMem_disp2() {
		return this.mem_disp2;
	}

	/**
	 * Sets the mem_disp2
	 * 
	 * @param mem_disp2
	 *            the mem_disp2 to set
	 */
	public void setMem_disp2(int mem_disp2) {
		this.mem_disp2 = mem_disp2;
	}

	/**
	 * Returns the mem_disp2_max
	 * 
	 * @return the mem_disp2_max
	 */
	public int getMem_disp2_max() {
		return this.mem_disp2_max;
	}

	/**
	 * Sets the mem_disp2_max
	 * 
	 * @param mem_disp2_max
	 *            the mem_disp2_max to set
	 */
	public void setMem_disp2_max(int mem_disp2_max) {
		this.mem_disp2_max = mem_disp2_max;
	}

	/**
	 * Returns the mem_gw
	 * 
	 * @return the mem_gw
	 */
	public int getMem_gw() {
		return this.mem_gw;
	}

	/**
	 * Sets the mem_gw
	 * 
	 * @param mem_gw
	 *            the mem_gw to set
	 */
	public void setMem_gw(int mem_gw) {
		this.mem_gw = mem_gw;
	}

	/**
	 * Returns the mem_gw_max
	 * 
	 * @return the mem_gw_max
	 */
	public int getMem_gw_max() {
		return this.mem_gw_max;
	}

	/**
	 * Sets the mem_gw_max
	 * 
	 * @param mem_gw_max
	 *            the mem_gw_max to set
	 */
	public void setMem_gw_max(int mem_gw_max) {
		this.mem_gw_max = mem_gw_max;
	}

	/**
	 * Returns the runts
	 * 
	 * @return the runts
	 */
	public Timestamp getRunts() {
		return this.runts;
	}

	/**
	 * Sets the runts
	 * 
	 * @param runts
	 *            the runts to set
	 */
	public void setRunts(Timestamp runts) {
		this.runts = runts;
	}

	/**
	 * Returns the vmstat_ap
	 * 
	 * @return the vmstat_ap
	 */
	public String getVmstat_ap() {
		return this.vmstat_ap;
	}

	/**
	 * Sets the vmstat_ap
	 * 
	 * @param vmstat_ap
	 *            the vmstat_ap to set
	 */
	public void setVmstat_ap(String vmstat_ap) {
		this.vmstat_ap = vmstat_ap;
	}

	/**
	 * Returns the vmstat_db
	 * 
	 * @return the vmstat_db
	 */
	public String getVmstat_db() {
		return this.vmstat_db;
	}

	/**
	 * Sets the vmstat_db
	 * 
	 * @param vmstat_db
	 *            the vmstat_db to set
	 */
	public void setVmstat_db(String vmstat_db) {
		this.vmstat_db = vmstat_db;
	}

}
