package ejcic.exception;

public class JcicException extends AppException {

	private static final long serialVersionUID = 1L;
	private String resonCode = "";
	private String errorMsg = "";
	private String progName = "";
	private String methodName = "";
	private int threadNo = 0;

	public JcicException() {
		super();
	}

	public JcicException(String sMessage) {
		super(sMessage);
		this.errorMsg = sMessage;
	}

	public JcicException(String sResonCode, String sMessage) {
		super(sMessage);
		this.errorMsg = sMessage;
		this.resonCode = sResonCode;
	}

	public void setErrorMsg(String sMsg) {
		this.errorMsg = sMsg;
	}

	public void setResonCode(String sCode) {
		this.resonCode = sCode;
	}

	public String getErrorMsg() {
		return this.errorMsg;
	}

	public String getResonCode() {
		return this.resonCode;
	}

	public String getMethodName() {
		return methodName;
	}

	public String getProgName() {
		return progName;
	}

	public void setMethodName(String methodName) {
		this.methodName = methodName;
	}

	public void setProgName(String progName) {
		this.progName = progName;
	}

	public void setThreadNo(int threadNo) {
		this.threadNo = threadNo;
	}

	public int getThreadNo() {
		return threadNo;
	}

}