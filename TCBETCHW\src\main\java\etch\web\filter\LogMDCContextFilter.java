package etch.web.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.log4j.MDC;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import etch.web.constant.WebConst;
import etch.web.model.WebUserProfile;

/**
 * <pre> LogMDCContextFilter.java </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul> <li>2003/06/30,PGU,new </ul>
 */
@WebFilter(filterName = "LogMDCContextFilter", urlPatterns = { "/*" })
public class LogMDCContextFilter implements Filter {
	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	public final static String M_USER_ID = "USER_ID";
	public final static String M_BRANCH_ID = "BRANCH_ID";
	public final static String M_CHARGE_ID = "CHARGE_ID";
	public final static String M_CLIENT_IP = "CLIENT_IP";
	public final static String M_SESSION_ID = "SESSION_ID";

	private final static String DEFAULT_USER_ID = "------";
	private final static String DEFAULT_BRANCH_ID = "---";

	/*
	 * (non-Javadoc)
	 * @see javax.servlet.Filter#destroy()
	 */
	@Override
	public void destroy() {

	}

	/*
	 * (non-Javadoc)
	 * @see javax.servlet.Filter#doFilter(javax.servlet.ServletRequest, javax.servlet.ServletResponse,
	 * javax.servlet.FilterChain)
	 */
	@Override
	public void doFilter(final ServletRequest request, final ServletResponse response, final FilterChain chain)
			throws IOException, ServletException {
		try {
			this.setMDCValue(request);
			chain.doFilter(request, response);
		} finally {
			this.removeMDCValue();
		}

	}

	/*
	 * (non-Javadoc)
	 * @see javax.servlet.Filter#init(javax.servlet.FilterConfig)
	 */
	@Override
	public void init(final FilterConfig arg0) throws ServletException {

	}

	private void setMDCValue(final ServletRequest request) {
		HttpSession session = ((HttpServletRequest) request).getSession(false);
		WebUserProfile user = (session == null) ? null : (WebUserProfile) session.getAttribute(WebConst.USER);
		MDC.put(LogMDCContextFilter.M_CLIENT_IP, request.getRemoteAddr());
		if (user != null) {
			MDC.put(LogMDCContextFilter.M_USER_ID, user.getUserId());
			MDC.put(LogMDCContextFilter.M_BRANCH_ID, user.getBranchId());
			MDC.put(LogMDCContextFilter.M_CHARGE_ID, user.getChargeId());
			MDC.put(LogMDCContextFilter.M_SESSION_ID, session.getId());

		} else {
			MDC.put(LogMDCContextFilter.M_USER_ID, LogMDCContextFilter.DEFAULT_USER_ID);
			MDC.put(LogMDCContextFilter.M_BRANCH_ID, LogMDCContextFilter.DEFAULT_BRANCH_ID);
			MDC.put(LogMDCContextFilter.M_CHARGE_ID, LogMDCContextFilter.DEFAULT_BRANCH_ID);
			MDC.put(LogMDCContextFilter.M_SESSION_ID, LogMDCContextFilter.DEFAULT_BRANCH_ID);
		}
	}

	private void removeMDCValue() {
		MDC.remove(LogMDCContextFilter.M_CLIENT_IP);
		MDC.remove(LogMDCContextFilter.M_USER_ID);
		MDC.remove(LogMDCContextFilter.M_BRANCH_ID);
		MDC.remove(LogMDCContextFilter.M_CHARGE_ID);
		MDC.remove(LogMDCContextFilter.M_SESSION_ID);
	}

}
