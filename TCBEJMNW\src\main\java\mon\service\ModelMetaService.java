package mon.service;

import mon.model.ModelMetaData;

import org.springframework.stereotype.Service;


/**
 * <pre> ModelServiceImpl.java </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul> <li>2003/06/30,PGU,NEW </ul>
 */
@Service("modelServiceImpl")
public interface ModelMetaService {
	public ModelMetaData getCacheData(Class<?> clazz);

	public void load();

	public Object[] getArgsByNames(Object obj, String[] fieldNames);

	public Object[] getInsertSql(Class<?> clazz);

	public Object[] getUpdateByKeySql(Class<?> clazz);

	public Object[] getDeleteByKeySql(Class<?> clazz);

	public String getFindAllSql(Class<?> clazz);

	public Object[] getFindAllByKeySql(Class<?> clazz);

}
