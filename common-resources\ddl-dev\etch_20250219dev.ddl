-- This CLP file was created using DB2LOOK Version "10.1" 
-- Timestamp: Wed Feb 19 17:40:28 2025
-- Database Name: ETCH           
-- Database Manager Version: DB2/AIX64 Version 10.1.6      
-- Database Codepage: 950
-- Database Collating Sequence is: UNIQUE
-- Alternate collating sequence(alt_collate): null
-- varchar2 compatibility(varchar2_compat): OFF
-- COMMIT is omitted. Explicit commit is required after executing the script.


---------------------------------------
-- DDL Statements for STORAGE GROUPS --
---------------------------------------
 
CREATE STOGROUP "IBMSTOGROUP" ON '/database/etchdb'
	 OVERHEAD 6.725000
	 DEVICE READ RATE 100.000000
	 DATA TAG NONE 
	 SET AS DEFAULT;

------------------------------------
-- DDL Statements for TABLESPACES --
------------------------------------


CREATE REGULAR TABLESPACE "ETCHTABSP" IN DATABASE PARTITION GROUP IBMDEFAULTGROUP 
	 PAGESIZE 4096 MANAGED BY AUTOMATIC STORAGE 
	 USING STOGROUP "IBMSTOGROUP" 
	 AUTORESIZE YES 
	 INITIALSIZE 768 K 
	 MAXSIZE NONE 
	 EXTENTSIZE 32
	 PREFETCHSIZE 32
	 BUFFERPOOL "IBMDEFAULTBP"
	 DATA TAG NONE
	 OVERHEAD 12.670000
	 TRANSFERRATE 0.180000 
	 FILE SYSTEM CACHING  
	 DROPPED TABLE RECOVERY ON;

CREATE REGULAR TABLESPACE "SYSTOOLSPACE" IN DATABASE PARTITION GROUP IBMCATGROUP PAGESIZE 4096 MANAGED BY SYSTEM 
	 USING ('/database/etchdb/jctinst1/NODE0000/ETCH/T000004/SYSTOOLSPACE')
	 EXTENTSIZE 32
	 PREFETCHSIZE AUTOMATIC
	 BUFFERPOOL "IBMDEFAULTBP"
	 DATA TAG NONE
	 OVERHEAD 12.670000
	 TRANSFERRATE 0.180000 
	 FILE SYSTEM CACHING  
	 DROPPED TABLE RECOVERY ON;

CREATE USER TEMPORARY TABLESPACE "SYSTOOLSTMPSPACE" IN DATABASE PARTITION GROUP IBMCATGROUP PAGESIZE 4096 MANAGED BY SYSTEM 
	 USING ('/database/etchdb/jctinst1/NODE0000/ETCH/T000005/SYSTOOLSTMPSPACE')
	 EXTENTSIZE 32
	 PREFETCHSIZE AUTOMATIC
	 BUFFERPOOL "IBMDEFAULTBP"
	 OVERHEAD 12.670000
	 TRANSFERRATE 0.180000 
	 FILE SYSTEM CACHING  
	 DROPPED TABLE RECOVERY OFF;


----------------------
-- Mimic tablespace --
----------------------

ALTER TABLESPACE "SYSCATSPACE"
      PREFETCHSIZE AUTOMATIC
      OVERHEAD 12.670000
      FILE SYSTEM CACHING 
      TRANSFERRATE 0.180000;


ALTER TABLESPACE "TEMPSPACE1"
      PREFETCHSIZE AUTOMATIC
      OVERHEAD 12.670000
      FILE SYSTEM CACHING 
      TRANSFERRATE 0.180000;


ALTER TABLESPACE "USERSPACE1"
      PREFETCHSIZE AUTOMATIC
      OVERHEAD 12.670000
      FILE SYSTEM CACHING 
      TRANSFERRATE 0.180000
      DATA TAG NONE;


------------------------------------------------
-- DDL Statements for Schemas
------------------------------------------------

-- Running the DDL below will explicitly create a schema in the
-- new database that corresponds to an implicitly created schema
-- in the original database.

CREATE SCHEMA "etchap1 ";

CREATE SCHEMA "ETCHAP1 ";

CREATE SCHEMA "SMARTCREDIT";

CREATE SCHEMA "DB2INST1";


---------------------------------
-- DDL Statements for Sequences
---------------------------------


CREATE SEQUENCE "ETCHAP1 "."JS2_ACCESSURL" AS INTEGER
	MINVALUE 1 MAXVALUE 2147483647
	START WITH 1 INCREMENT BY 1
	CACHE 20 NO CYCLE NO ORDER;


CREATE SEQUENCE "ETCHAP1 "."JS2_AGENT" AS INTEGER
	MINVALUE 1 MAXVALUE 2147483647
	START WITH 1 INCREMENT BY 1
	CACHE 20 NO CYCLE NO ORDER;


CREATE SEQUENCE "ETCHAP1 "."SQ_CACHE" AS INTEGER
	MINVALUE 1 MAXVALUE 2147483647
	START WITH 1 INCREMENT BY 1
	CACHE 20 NO CYCLE NO ORDER;

ALTER SEQUENCE "ETCHAP1 "."SQ_CACHE" RESTART WITH 7481;


CREATE SEQUENCE "ETCHAP1 "."SQ_INQUIRY" AS INTEGER
	MINVALUE 1 MAXVALUE 2147483647
	START WITH 1 INCREMENT BY 1
	CACHE 20 NO CYCLE NO ORDER;

ALTER SEQUENCE "ETCHAP1 "."SQ_INQUIRY" RESTART WITH 4101;


CREATE SEQUENCE "ETCHAP1 "."SQ_INQUIRY_TASK" AS INTEGER
	MINVALUE 1 MAXVALUE 2147483647
	START WITH 1 INCREMENT BY 1
	CACHE 20 NO CYCLE NO ORDER;

ALTER SEQUENCE "ETCHAP1 "."SQ_INQUIRY_TASK" RESTART WITH 3561;


CREATE SEQUENCE "ETCHAP1 "."SQ_ITEM_CACHE_INFO" AS INTEGER
	MINVALUE 1 MAXVALUE 2147483647
	START WITH 1 INCREMENT BY 1
	CACHE 20 NO CYCLE NO ORDER;

ALTER SEQUENCE "ETCHAP1 "."SQ_ITEM_CACHE_INFO" RESTART WITH 2981;


CREATE SEQUENCE "ETCHAP1 "."SQ_MESSAGE" AS INTEGER
	MINVALUE 1 MAXVALUE 2147483647
	START WITH 1 INCREMENT BY 1
	CACHE 20 NO CYCLE NO ORDER;

ALTER SEQUENCE "ETCHAP1 "."SQ_MESSAGE" RESTART WITH 2901;



------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."EXPLAIN_INSTANCE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."EXPLAIN_INSTANCE"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_OPTION" CHAR(1) NOT NULL , 
		  "SNAPSHOT_TAKEN" CHAR(1) NOT NULL , 
		  "DB2_VERSION" CHAR(7) NOT NULL , 
		  "SQL_TYPE" CHAR(1) NOT NULL , 
		  "QUERYOPT" INTEGER NOT NULL , 
		  "BLOCK" CHAR(1) NOT NULL , 
		  "ISOLATION" CHAR(2) NOT NULL , 
		  "BUFFPAGE" INTEGER NOT NULL , 
		  "AVG_APPLS" INTEGER NOT NULL , 
		  "SORTHEAP" INTEGER NOT NULL , 
		  "LOCKLIST" INTEGER NOT NULL , 
		  "MAXLOCKS" SMALLINT NOT NULL , 
		  "LOCKS_AVAIL" INTEGER NOT NULL , 
		  "CPU_SPEED" DOUBLE NOT NULL , 
		  "REMARKS" VARCHAR(254) , 
		  "DBHEAP" INTEGER NOT NULL , 
		  "COMM_SPEED" DOUBLE NOT NULL , 
		  "PARALLELISM" CHAR(2) NOT NULL , 
		  "DATAJOINER" CHAR(1) NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."EXPLAIN_INSTANCE"

ALTER TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" 
	ADD PRIMARY KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."EXPLAIN_STATEMENT"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."EXPLAIN_STATEMENT"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "QUERYNO" INTEGER NOT NULL , 
		  "QUERYTAG" CHAR(20) NOT NULL , 
		  "STATEMENT_TYPE" CHAR(2) NOT NULL , 
		  "UPDATABLE" CHAR(1) NOT NULL , 
		  "DELETABLE" CHAR(1) NOT NULL , 
		  "TOTAL_COST" DOUBLE NOT NULL , 
		  "STATEMENT_TEXT" CLOB(2097152) NOT LOGGED NOT COMPACT NOT NULL , 
		  "SNAPSHOT" BLOB(10485760) NOT LOGGED NOT COMPACT , 
		  "QUERY_DEGREE" INTEGER NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."EXPLAIN_STATEMENT"

ALTER TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" 
	ADD PRIMARY KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO");



-- DDL Statements for Indexes on Table "ETCHAP1 "."EXPLAIN_STATEMENT"

CREATE INDEX "ETCHAP1 "."STMT_I1" ON "ETCHAP1 "."EXPLAIN_STATEMENT" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."EXPLAIN_ARGUMENT"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "OPERATOR_ID" INTEGER NOT NULL , 
		  "ARGUMENT_TYPE" CHAR(8) NOT NULL , 
		  "ARGUMENT_VALUE" VARCHAR(1024) , 
		  "LONG_ARGUMENT_VALUE" CLOB(2097152) NOT LOGGED NOT COMPACT )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."EXPLAIN_ARGUMENT"

CREATE INDEX "ETCHAP1 "."ARG_I1" ON "ETCHAP1 "."EXPLAIN_ARGUMENT" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC,
		 "OPERATOR_ID" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."EXPLAIN_OBJECT"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."EXPLAIN_OBJECT"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "OBJECT_SCHEMA" VARCHAR(128) NOT NULL , 
		  "OBJECT_NAME" VARCHAR(128) NOT NULL , 
		  "OBJECT_TYPE" CHAR(2) NOT NULL , 
		  "CREATE_TIME" TIMESTAMP , 
		  "STATISTICS_TIME" TIMESTAMP , 
		  "COLUMN_COUNT" SMALLINT NOT NULL , 
		  "ROW_COUNT" BIGINT NOT NULL , 
		  "WIDTH" INTEGER NOT NULL , 
		  "PAGES" INTEGER NOT NULL , 
		  "DISTINCT" CHAR(1) NOT NULL , 
		  "TABLESPACE_NAME" VARCHAR(128) , 
		  "OVERHEAD" DOUBLE NOT NULL , 
		  "TRANSFER_RATE" DOUBLE NOT NULL , 
		  "PREFETCHSIZE" INTEGER NOT NULL , 
		  "EXTENTSIZE" INTEGER NOT NULL , 
		  "CLUSTER" DOUBLE NOT NULL , 
		  "NLEAF" INTEGER NOT NULL , 
		  "NLEVELS" INTEGER NOT NULL , 
		  "FULLKEYCARD" BIGINT NOT NULL , 
		  "OVERFLOW" INTEGER NOT NULL , 
		  "FIRSTKEYCARD" BIGINT NOT NULL , 
		  "FIRST2KEYCARD" BIGINT NOT NULL , 
		  "FIRST3KEYCARD" BIGINT NOT NULL , 
		  "FIRST4KEYCARD" BIGINT NOT NULL , 
		  "SEQUENTIAL_PAGES" INTEGER NOT NULL , 
		  "DENSITY" INTEGER NOT NULL , 
		  "STATS_SRC" CHAR(1) NOT NULL , 
		  "AVERAGE_SEQUENCE_GAP" DOUBLE NOT NULL , 
		  "AVERAGE_SEQUENCE_FETCH_GAP" DOUBLE NOT NULL , 
		  "AVERAGE_SEQUENCE_PAGES" DOUBLE NOT NULL , 
		  "AVERAGE_SEQUENCE_FETCH_PAGES" DOUBLE NOT NULL , 
		  "AVERAGE_RANDOM_PAGES" DOUBLE NOT NULL , 
		  "AVERAGE_RANDOM_FETCH_PAGES" DOUBLE NOT NULL , 
		  "NUMRIDS" BIGINT NOT NULL , 
		  "NUMRIDS_DELETED" BIGINT NOT NULL , 
		  "NUM_EMPTY_LEAFS" BIGINT NOT NULL , 
		  "ACTIVE_BLOCKS" BIGINT NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."EXPLAIN_OBJECT"

CREATE INDEX "ETCHAP1 "."OBJ_I1" ON "ETCHAP1 "."EXPLAIN_OBJECT" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."EXPLAIN_OPERATOR"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."EXPLAIN_OPERATOR"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "OPERATOR_ID" INTEGER NOT NULL , 
		  "OPERATOR_TYPE" CHAR(6) NOT NULL , 
		  "TOTAL_COST" DOUBLE NOT NULL , 
		  "IO_COST" DOUBLE NOT NULL , 
		  "CPU_COST" DOUBLE NOT NULL , 
		  "FIRST_ROW_COST" DOUBLE NOT NULL , 
		  "RE_TOTAL_COST" DOUBLE NOT NULL , 
		  "RE_IO_COST" DOUBLE NOT NULL , 
		  "RE_CPU_COST" DOUBLE NOT NULL , 
		  "COMM_COST" DOUBLE NOT NULL , 
		  "FIRST_COMM_COST" DOUBLE NOT NULL , 
		  "BUFFERS" DOUBLE NOT NULL , 
		  "REMOTE_TOTAL_COST" DOUBLE NOT NULL , 
		  "REMOTE_COMM_COST" DOUBLE NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."EXPLAIN_OPERATOR"

CREATE INDEX "ETCHAP1 "."OPR_I1" ON "ETCHAP1 "."EXPLAIN_OPERATOR" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC,
		 "OPERATOR_ID" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."EXPLAIN_PREDICATE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."EXPLAIN_PREDICATE"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "OPERATOR_ID" INTEGER NOT NULL , 
		  "PREDICATE_ID" INTEGER NOT NULL , 
		  "HOW_APPLIED" CHAR(5) NOT NULL , 
		  "WHEN_EVALUATED" CHAR(3) NOT NULL , 
		  "RELOP_TYPE" CHAR(2) NOT NULL , 
		  "SUBQUERY" CHAR(1) NOT NULL , 
		  "FILTER_FACTOR" DOUBLE NOT NULL , 
		  "PREDICATE_TEXT" CLOB(2097152) NOT LOGGED NOT COMPACT )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."EXPLAIN_PREDICATE"

CREATE INDEX "ETCHAP1 "."PRD_I1" ON "ETCHAP1 "."EXPLAIN_PREDICATE" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC,
		 "OPERATOR_ID" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."EXPLAIN_STREAM"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."EXPLAIN_STREAM"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "STREAM_ID" INTEGER NOT NULL , 
		  "SOURCE_TYPE" CHAR(1) NOT NULL , 
		  "SOURCE_ID" INTEGER NOT NULL , 
		  "TARGET_TYPE" CHAR(1) NOT NULL , 
		  "TARGET_ID" INTEGER NOT NULL , 
		  "OBJECT_SCHEMA" VARCHAR(128) , 
		  "OBJECT_NAME" VARCHAR(128) , 
		  "STREAM_COUNT" DOUBLE NOT NULL , 
		  "COLUMN_COUNT" SMALLINT NOT NULL , 
		  "PREDICATE_ID" INTEGER NOT NULL , 
		  "COLUMN_NAMES" CLOB(2097152) NOT LOGGED NOT COMPACT , 
		  "PMID" SMALLINT NOT NULL , 
		  "SINGLE_NODE" CHAR(5) , 
		  "PARTITION_COLUMNS" CLOB(2097152) NOT LOGGED NOT COMPACT )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."EXPLAIN_STREAM"

CREATE INDEX "ETCHAP1 "."STM_I1" ON "ETCHAP1 "."EXPLAIN_STREAM" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."USERS"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."USERS"  (
		  "USERID" BIGINT NOT NULL , 
		  "LOGINNAME" VARCHAR(32) , 
		  "PASSWD" VARCHAR(32) , 
		  "REALNAME" VARCHAR(32) , 
		  "EMAIL" VARCHAR(32) , 
		  "DESCRIPTION" VARCHAR(64) , 
		  "PASSWDLASTCHANGEDATE" DATE , 
		  "CREATEDATETIME" DATE , 
		  "DEFAULTINFO" BIGINT , 
		  "TEMPINFO" BIGINT , 
		  "TXNAME" VARCHAR(32) , 
		  "TXDATETIME" DATE , 
		  "USERIP" VARCHAR(255) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."USERS"

ALTER TABLE "ETCHAP1 "."USERS" 
	ADD PRIMARY KEY
		("USERID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."USERS"

CREATE INDEX "ETCHAP1 "."IDX_USER_INFO" ON "ETCHAP1 "."USERS" 
		("DEFAULTINFO" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."USERS"

CREATE INDEX "ETCHAP1 "."IDX_USER_LOGINNAME" ON "ETCHAP1 "."USERS" 
		("LOGINNAME" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."USERS"

CREATE INDEX "ETCHAP1 "."XUSERS51" ON "ETCHAP1 "."USERS" 
		("LOGINNAME" ASC,
		 "REALNAME" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."OPERATORS"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."OPERATORS"  (
		  "USERID" BIGINT NOT NULL , 
		  "OID" BIGINT NOT NULL )   
		 IN "ETCHTABSP" ; 





------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."DOCUMENTFORROLE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."DOCUMENTFORROLE"  (
		  "RID" BIGINT NOT NULL , 
		  "TIDD" VARCHAR(32) , 
		  "CREATING" SMALLINT , 
		  "READING" SMALLINT , 
		  "UPDATING1" SMALLINT , 
		  "DELETING1" SMALLINT , 
		  "EXECUTING" SMALLINT , 
		  "ACCESSSTRING" VARCHAR(255) , 
		  "TID" VARCHAR(32) NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."DOCUMENTFORROLE"

ALTER TABLE "ETCHAP1 "."DOCUMENTFORROLE" 
	ADD PRIMARY KEY
		("RID",
		 "TID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."ORGANIZATIONUNIT"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."ORGANIZATIONUNIT"  (
		  "OID" BIGINT NOT NULL , 
		  "DISCRIMINATOR" CHAR(1) NOT NULL , 
		  "NAME" VARCHAR(40) , 
		  "DESCRIPTION" VARCHAR(64) , 
		  "STARTDATETIME" DATE , 
		  "ENDDATETIME" DATE , 
		  "DID" BIGINT , 
		  "TXNAME" VARCHAR(32) , 
		  "TXDATETIME" DATE , 
		  "USERIP" VARCHAR(255) , 
		  "OPERATOR1" SMALLINT , 
		  "DEPARTMENTCODE" VARCHAR(255) , 
		  "PROJECT" SMALLINT , 
		  "VIRTUAL" SMALLINT , 
		  "MGRUID" BIGINT , 
		  "TCHCODE" VARCHAR(10) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."ORGANIZATIONUNIT"

ALTER TABLE "ETCHAP1 "."ORGANIZATIONUNIT" 
	ADD PRIMARY KEY
		("OID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."ORGANIZATIONUNIT"

CREATE INDEX "ETCHAP1 "."IDX_ORG_DEPCODE" ON "ETCHAP1 "."ORGANIZATIONUNIT" 
		("DEPARTMENTCODE" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."ORGANIZATIONUNIT"

CREATE INDEX "ETCHAP1 "."IDX_ORG_DISC" ON "ETCHAP1 "."ORGANIZATIONUNIT" 
		("DISCRIMINATOR" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."SECURITYROLE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."SECURITYROLE"  (
		  "RID" BIGINT NOT NULL , 
		  "NAME" VARCHAR(32) , 
		  "DESCRIPTION" VARCHAR(64) , 
		  "AID" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SECURITYROLE"

ALTER TABLE "ETCHAP1 "."SECURITYROLE" 
	ADD PRIMARY KEY
		("RID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."AGENT"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."AGENT"  (
		  "AID" BIGINT NOT NULL , 
		  "ENDDATETIME" DATE , 
		  "STARTDATETIME" DATE , 
		  "ACTIVE" SMALLINT , 
		  "DEFAULTAGENT" SMALLINT , 
		  "USERID" BIGINT , 
		  "OID" BIGINT , 
		  "ID4DEFAULT" BIGINT , 
		  "INDEX4DEFAULT" INTEGER , 
		  "ID" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."AGENT"

ALTER TABLE "ETCHAP1 "."AGENT" 
	ADD PRIMARY KEY
		("AID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."DOCUMENTFORGROUP"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."DOCUMENTFORGROUP"  (
		  "OID" BIGINT NOT NULL , 
		  "TIDD" VARCHAR(32) , 
		  "CREATING" SMALLINT , 
		  "READING" SMALLINT , 
		  "UPDATING1" SMALLINT , 
		  "DELETING1" SMALLINT , 
		  "EXECUTING" SMALLINT , 
		  "ACCESSSTRING" VARCHAR(255) , 
		  "TID" VARCHAR(32) NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."DOCUMENTFORGROUP"

ALTER TABLE "ETCHAP1 "."DOCUMENTFORGROUP" 
	ADD PRIMARY KEY
		("OID",
		 "TID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."USERINFO"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."USERINFO"  (
		  "IID" BIGINT NOT NULL , 
		  "ACTIVEDATE" DATE , 
		  "EXPIREDATE" DATE , 
		  "ACTIVE" SMALLINT , 
		  "ID" BIGINT , 
		  "USERID" BIGINT , 
		  "TXNAME" VARCHAR(32) , 
		  "TXDATETIME" DATE , 
		  "USERIP" VARCHAR(255) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."USERINFO"

ALTER TABLE "ETCHAP1 "."USERINFO" 
	ADD PRIMARY KEY
		("IID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."USERINFO"

CREATE INDEX "ETCHAP1 "."IDX_USERINFO_ID" ON "ETCHAP1 "."USERINFO" 
		("USERID" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."APPLICATION"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."APPLICATION"  (
		  "AID" BIGINT NOT NULL , 
		  "NAME" VARCHAR(64) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."APPLICATION"

ALTER TABLE "ETCHAP1 "."APPLICATION" 
	ADD PRIMARY KEY
		("AID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."ROLEGROUP"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."ROLEGROUP"  (
		  "GID1" BIGINT NOT NULL , 
		  "RID" BIGINT NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."ROLEGROUP"

ALTER TABLE "ETCHAP1 "."ROLEGROUP" 
	ADD PRIMARY KEY
		("RID",
		 "GID1");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."USERDEPARTMENTROLE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."USERDEPARTMENTROLE"  (
		  "ID" BIGINT NOT NULL , 
		  "STARTDATETIME" DATE , 
		  "IID" BIGINT , 
		  "GID1" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."USERDEPARTMENTROLE"

ALTER TABLE "ETCHAP1 "."USERDEPARTMENTROLE" 
	ADD PRIMARY KEY
		("ID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."USERDEPARTMENTROLE"

CREATE INDEX "ETCHAP1 "."IDX_USERDEP_GID" ON "ETCHAP1 "."USERDEPARTMENTROLE" 
		("GID1" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."USERDEPARTMENTROLE"

CREATE INDEX "ETCHAP1 "."IDX_USERDEP_IID" ON "ETCHAP1 "."USERDEPARTMENTROLE" 
		("IID" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."STRINGVALUEFORUSER"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."STRINGVALUEFORUSER"  (
		  "USERID" BIGINT NOT NULL , 
		  "VALUE" VARCHAR(255) NOT NULL , 
		  "NAME" VARCHAR(255) NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."STRINGVALUEFORUSER"

ALTER TABLE "ETCHAP1 "."STRINGVALUEFORUSER" 
	ADD PRIMARY KEY
		("USERID",
		 "NAME");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."PASSWDMEMENTO"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."PASSWDMEMENTO"  (
		  "ID" BIGINT NOT NULL , 
		  "PASSWD" VARCHAR(255) , 
		  "TXNAME" VARCHAR(32) , 
		  "TXDATETIME" DATE , 
		  "USERIP" VARCHAR(255) , 
		  "USERID" BIGINT , 
		  "INDEX1" INTEGER )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."PASSWDMEMENTO"

ALTER TABLE "ETCHAP1 "."PASSWDMEMENTO" 
	ADD PRIMARY KEY
		("ID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."PASSWDMEMENTO"

CREATE INDEX "ETCHAP1 "."IDX_PASSWD" ON "ETCHAP1 "."PASSWDMEMENTO" 
		("USERID" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."SECURITYKEYVALUEINFO"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO"  (
		  "ID" BIGINT NOT NULL , 
		  "MODELNAME" VARCHAR(255) NOT NULL , 
		  "KEYNAME" VARCHAR(255) NOT NULL , 
		  "KEYDISPLAYNAME" VARCHAR(255) , 
		  "REQUIRED" CHAR(1) , 
		  "ENABLED" CHAR(1) , 
		  "UITYPE" VARCHAR(255) , 
		  "DEFAULTVALUE" VARCHAR(255) , 
		  "ATTRIBUTES" VARCHAR(255) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SECURITYKEYVALUEINFO"

ALTER TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" 
	ADD PRIMARY KEY
		("ID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."ACCESSURL"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."ACCESSURL"  (
		  "ID" BIGINT NOT NULL , 
		  "PERMISSION" CHAR(1) , 
		  "PATTERN" VARCHAR(255) , 
		  "RID" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."ACCESSURL"

ALTER TABLE "ETCHAP1 "."ACCESSURL" 
	ADD PRIMARY KEY
		("ID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."STRINGVALUEFOROU"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."STRINGVALUEFOROU"  (
		  "OID" BIGINT NOT NULL , 
		  "VALUE" VARCHAR(255) , 
		  "NAME" VARCHAR(255) NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."STRINGVALUEFOROU"

ALTER TABLE "ETCHAP1 "."STRINGVALUEFOROU" 
	ADD PRIMARY KEY
		("OID",
		 "NAME");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."SECURITYPARAMETER"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."SECURITYPARAMETER"  (
		  "ID" BIGINT NOT NULL , 
		  "NAME" VARCHAR(100) , 
		  "VALUE" VARCHAR(255) , 
		  "TYPE" VARCHAR(10) , 
		  "EDITABLE" CHAR(1) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SECURITYPARAMETER"

ALTER TABLE "ETCHAP1 "."SECURITYPARAMETER" 
	ADD PRIMARY KEY
		("ID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."AUDITLOGRECORD"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."AUDITLOGRECORD"  (
		  "ID" BIGINT NOT NULL , 
		  "CRUDE" CHAR(1) , 
		  "AUDITNAME" VARCHAR(255) , 
		  "ENTITYCLASS" VARCHAR(255) , 
		  "TXNAME" VARCHAR(32) , 
		  "TXDATETIME" DATE , 
		  "USERIP" VARCHAR(255) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."AUDITLOGRECORD"

ALTER TABLE "ETCHAP1 "."AUDITLOGRECORD" 
	ADD PRIMARY KEY
		("ID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."AUDITINGLOG"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."AUDITINGLOG"  (
		  "ID" BIGINT NOT NULL , 
		  "CRUDE" CHAR(1) , 
		  "IDENTITYCODE" VARCHAR(32) , 
		  "ENTITY" VARCHAR(32) , 
		  "CODE" INTEGER , 
		  "NOTICE" VARCHAR(255) , 
		  "TXNAME" VARCHAR(32) , 
		  "TXDATETIME" TIMESTAMP , 
		  "USERIP" VARCHAR(255) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."AUDITINGLOG"

ALTER TABLE "ETCHAP1 "."AUDITINGLOG" 
	ADD PRIMARY KEY
		("ID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."USERCARETAKER"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."USERCARETAKER"  (
		  "USERID" BIGINT NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."USERCARETAKER"

ALTER TABLE "ETCHAP1 "."USERCARETAKER" 
	ADD PRIMARY KEY
		("USERID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION"  (
		  "NAME" VARCHAR(20) NOT NULL , 
		  "DESCRIPTION" VARCHAR(50) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION"

ALTER TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" 
	ADD PRIMARY KEY
		("NAME");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP"  (
		  "IID" BIGINT NOT NULL , 
		  "INQUIRY_TYPE" INTEGER NOT NULL , 
		  "TX_CODE" VARCHAR(4) NOT NULL , 
		  "DATE_TIME" TIMESTAMP NOT NULL , 
		  "MAIN_INQUIRY_CONDITION" VARCHAR(100) , 
		  "ALT_INQUIRY_CONDITION" VARCHAR(100) , 
		  "INQUIRED_RECORDS" SMALLINT , 
		  "INQUIRY_STATUS" INTEGER , 
		  "HTML_CACHE" BIGINT , 
		  "ROW_DATA_CACHE" BIGINT , 
		  "XML_CACHE" BIGINT , 
		  "XML_CACHE2" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP"

ALTER TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" 
	ADD PRIMARY KEY
		("IID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER"  (
		  "PID" VARCHAR(4) NOT NULL , 
		  "DESCRIPTION" VARCHAR(50) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER"

ALTER TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" 
	ADD PRIMARY KEY
		("PID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_CACHE_BACKUP"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_CACHE_BACKUP"  (
		  "CID" BIGINT NOT NULL , 
		  "CONTENT" CLOB(524288) LOGGED NOT COMPACT , 
		  "CACHE_TYPE" INTEGER )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_CACHE_BACKUP"

ALTER TABLE "ETCHAP1 "."TB_CACHE_BACKUP" 
	ADD PRIMARY KEY
		("CID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_CODE_DEFINITION"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_CODE_DEFINITION"  (
		  "CID" BIGINT NOT NULL , 
		  "NAME" VARCHAR(20) NOT NULL , 
		  "CODE" VARCHAR(10) NOT NULL , 
		  "VALUE" VARCHAR(70) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_CODE_DEFINITION"

ALTER TABLE "ETCHAP1 "."TB_CODE_DEFINITION" 
	ADD PRIMARY KEY
		("CID");


-- DDL Statements for Unique Constraints on Table "ETCHAP1 "."TB_CODE_DEFINITION"


ALTER TABLE "ETCHAP1 "."TB_CODE_DEFINITION" 
	ADD UNIQUE
		("NAME",
		 "CODE");

------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_SUB_ITEM_DEFINITION"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION"  (
		  "SID" BIGINT NOT NULL , 
		  "TARGET_ITEM" BIGINT , 
		  "SEQ_ID" INTEGER , 
		  "CID" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_SUB_ITEM_DEFINITION"

ALTER TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" 
	ADD PRIMARY KEY
		("SID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_ATOM_DEFINITION_INFO"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO"  (
		  "IID" BIGINT NOT NULL , 
		  "EFF_DATE" TIMESTAMP , 
		  "EXP_DATE" TIMESTAMP , 
		  "CONTENTS" CLOB(524288) LOGGED NOT COMPACT , 
		  "VIEW_IS_CREATED" SMALLINT , 
		  "ATOM_CODE" VARCHAR(8) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_ATOM_DEFINITION_INFO"

ALTER TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" 
	ADD PRIMARY KEY
		("IID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_ITEM_TYPE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_ITEM_TYPE"  (
		  "TID" BIGINT NOT NULL , 
		  "DESCRIPTION" VARCHAR(50) , 
		  "RAW_DATA_SUPPLIED" SMALLINT , 
		  "HTML_DATAS_UPPLIED" SMALLINT , 
		  "TYPE_CODE" INTEGER )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_ITEM_TYPE"

ALTER TABLE "ETCHAP1 "."TB_ITEM_TYPE" 
	ADD PRIMARY KEY
		("TID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_CACHE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_CACHE"  (
		  "CID" BIGINT NOT NULL , 
		  "CONTENT" CLOB(524288) LOGGED NOT COMPACT , 
		  "CACHE_TYPE" INTEGER )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_CACHE"

ALTER TABLE "ETCHAP1 "."TB_CACHE" 
	ADD PRIMARY KEY
		("CID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_INQUIRY_FEE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_INQUIRY_FEE"  (
		  "FID" BIGINT NOT NULL , 
		  "CHARGE_POINT" DOUBLE , 
		  "EFF_DATE" TIMESTAMP , 
		  "EXP_DATE" TIMESTAMP , 
		  "IID" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_INQUIRY_FEE"

ALTER TABLE "ETCHAP1 "."TB_INQUIRY_FEE" 
	ADD PRIMARY KEY
		("FID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY_FEE"

CREATE INDEX "ETCHAP1 "."XTB_INQUIRY_FEE01" ON "ETCHAP1 "."TB_INQUIRY_FEE" 
		("IID" ASC,
		 "CHARGE_POINT" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_ITEM_DEFINITION"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_ITEM_DEFINITION"  (
		  "IID" BIGINT NOT NULL , 
		  "DISCRIMINATOR" CHAR(1) NOT NULL , 
		  "ITEM_CODE" VARCHAR(5) NOT NULL , 
		  "CACHE_REFRESH_CYCLE" INTEGER NOT NULL , 
		  "CACHE_REFRESH_UNIT" VARCHAR(1) NOT NULL , 
		  "CACHE_REFRESH_DATE" DATE , 
		  "DESCRIPTION" VARCHAR(100) , 
		  "MAIN_INQUIRY_CONDITIONS" VARCHAR(100) , 
		  "ALT_INQUIRY_CONDITIONS" VARCHAR(100) , 
		  "DISABLED" SMALLINT , 
		  "ITEM_TYPE" BIGINT , 
		  "PROVIDER" VARCHAR(4) NOT NULL , 
		  "TARGET" INTEGER , 
		  "XSL_FILE_NAME" VARCHAR(20) , 
		  "GROOVY_FILE_NAME" VARCHAR(20) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_ITEM_DEFINITION"

ALTER TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" 
	ADD PRIMARY KEY
		("IID");


-- DDL Statements for Unique Constraints on Table "ETCHAP1 "."TB_ITEM_DEFINITION"


ALTER TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" 
	ADD UNIQUE
		("ITEM_CODE",
		 "PROVIDER");

------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_ITEM_CACHE_INFO"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO"  (
		  "IID" BIGINT NOT NULL , 
		  "INQUIRY_TYPE" INTEGER NOT NULL , 
		  "TX_CODE" VARCHAR(4) NOT NULL , 
		  "DATE_TIME" TIMESTAMP NOT NULL , 
		  "MAIN_INQUIRY_CONDITION" VARCHAR(100) , 
		  "ALT_INQUIRY_CONDITION" VARCHAR(100) , 
		  "INQUIRED_RECORDS" SMALLINT , 
		  "INQUIRY_STATUS" INTEGER , 
		  "HTML_CACHE" BIGINT , 
		  "ROW_DATA_CACHE" BIGINT , 
		  "XML_CACHE" BIGINT , 
		  "XML_CACHE2" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_ITEM_CACHE_INFO"

ALTER TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" 
	ADD PRIMARY KEY
		("IID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_ITEM_CACHE_INFO"

CREATE INDEX "ETCHAP1 "."IDX_CACHE_ISCACHE" ON "ETCHAP1 "."TB_ITEM_CACHE_INFO" 
		("TX_CODE" ASC,
		 "INQUIRY_TYPE" ASC,
		 "MAIN_INQUIRY_CONDITION" ASC,
		 "INQUIRY_STATUS" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_INQUIRY"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_INQUIRY"  (
		  "IID" BIGINT NOT NULL , 
		  "DISCRIMINATOR" CHAR(1) NOT NULL , 
		  "MAIN_INQUIRY_KEYS" VARCHAR(100) , 
		  "ALT_INQUIRY_KEYS" VARCHAR(100) , 
		  "ITEM_CACHE_INFO" BIGINT , 
		  "ITEM_DEFINITION" BIGINT , 
		  "TID" BIGINT , 
		  "SENT_TIME" TIMESTAMP , 
		  "SEND_STATUS" INTEGER , 
		  "RECEIVE_STATUS" INTEGER , 
		  "COMBO_INQUIRY" BIGINT , 
		  "REQUEST_MESSAGE" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_INQUIRY"

ALTER TABLE "ETCHAP1 "."TB_INQUIRY" 
	ADD PRIMARY KEY
		("IID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY"

CREATE INDEX "ETCHAP1 "."IDX_INQUIRY_3" ON "ETCHAP1 "."TB_INQUIRY" 
		("ITEM_CACHE_INFO" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY"

CREATE INDEX "ETCHAP1 "."XTB_INQUIRY01" ON "ETCHAP1 "."TB_INQUIRY" 
		("SENT_TIME" ASC,
		 "RECEIVE_STATUS" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY"

CREATE INDEX "ETCHAP1 "."XTB_INQUIRY50" ON "ETCHAP1 "."TB_INQUIRY" 
		("TID" ASC,
		 "SENT_TIME" ASC,
		 "RECEIVE_STATUS" ASC,
		 "ITEM_DEFINITION" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY"

CREATE INDEX "ETCHAP1 "."XTB_INQUIRY51" ON "ETCHAP1 "."TB_INQUIRY" 
		("TID" ASC,
		 "SENT_TIME" ASC,
		 "ALT_INQUIRY_KEYS" ASC,
		 "MAIN_INQUIRY_KEYS" ASC,
		 "RECEIVE_STATUS" ASC,
		 "ITEM_DEFINITION" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION"  (
		  "DID" BIGINT NOT NULL , 
		  "UI_KEY" VARCHAR(25) NOT NULL , 
		  "ITEM_CODE" VARCHAR(5) NOT NULL , 
		  "QUERY_CONDITION" VARCHAR(25) NOT NULL , 
		  "LENGTH" INTEGER , 
		  "DESCRIPTION" VARCHAR(50) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION"

ALTER TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" 
	ADD PRIMARY KEY
		("DID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_MESSAGE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_MESSAGE"  (
		  "MID" BIGINT NOT NULL , 
		  "DISCRIMINATOR" CHAR(1) NOT NULL , 
		  "BANK_CODE" VARCHAR(7) , 
		  "SYS_DATE" VARCHAR(8) , 
		  "SYS_TIME" VARCHAR(6) , 
		  "TX_CODE" VARCHAR(4) , 
		  "QUEUE_ID" VARCHAR(8) , 
		  "RESERVED_FOR_MEMBER" VARCHAR(12) , 
		  "TX_SERIES" VARCHAR(6) , 
		  "ERROR_CODE" VARCHAR(5) , 
		  "BRANCH_CODE_FOR_PAYMENT" VARCHAR(4) , 
		  "USER_ID" VARCHAR(10) , 
		  "INQUIRY_REASON" VARCHAR(3) , 
		  "RESPONSE_SERIES" VARCHAR(6) , 
		  "TOTAL_RESPONSES" CHAR(1) , 
		  "ACCOUNT_NUMBER" VARCHAR(60) , 
		  "OTHER_KEYS" VARCHAR(50) , 
		  "INQUIRED_RECORDS" VARCHAR(50) , 
		  "DATA_SERIES" VARCHAR(3) , 
		  "RESERVED_COLUMN" VARCHAR(11) , 
		  "CONTIUNED_FLAG" CHAR(1) , 
		  "CHARGMAN_NUMBER" VARCHAR(10) , 
		  "ACCOUNT_NAME" VARCHAR(40) , 
		  "OPEN_BANK_NUMBER" VARCHAR(9) , 
		  "OPEN_ACCOUNT_NUMBER" VARCHAR(9) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_MESSAGE"

ALTER TABLE "ETCHAP1 "."TB_MESSAGE" 
	ADD PRIMARY KEY
		("MID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_ATOM_DEFINITION"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_ATOM_DEFINITION"  (
		  "ATOM_CODE" VARCHAR(8) NOT NULL , 
		  "CATALOG" VARCHAR(40) , 
		  "PURPOSE" VARCHAR(100) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_ATOM_DEFINITION"

ALTER TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" 
	ADD PRIMARY KEY
		("ATOM_CODE");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_COMBO_CODITION"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_COMBO_CODITION"  (
		  "CBIID" BIGINT NOT NULL , 
		  "INQUIRY_NO" INTEGER , 
		  "NEXT_INQUIRY" INTEGER , 
		  "BOOLEAN_EXPRESSION" VARCHAR(255) , 
		  "SID" BIGINT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_COMBO_CODITION"

ALTER TABLE "ETCHAP1 "."TB_COMBO_CODITION" 
	ADD PRIMARY KEY
		("CBIID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_ITEM_ATOM_MAPPING"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING"  (
		  "IID" BIGINT NOT NULL , 
		  "ATOM_CODE" VARCHAR(8) NOT NULL )   
		 IN "ETCHTABSP" ; 





------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_INQUIRY_TASK"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_INQUIRY_TASK"  (
		  "TID" BIGINT NOT NULL , 
		  "SCHEDULED_TIME" TIMESTAMP , 
		  "CACHE_OPTION" INTEGER , 
		  "STATUS" INTEGER , 
		  "IS_SCHEDULED_JOB" SMALLINT , 
		  "INQUIRIED_RECORD_ATTATCHED" SMALLINT , 
		  "DATA_TYPE" INTEGER , 
		  "RETURN_TYPE" INTEGER , 
		  "NOTE" VARCHAR(50) , 
		  "QUERY_BY" VARCHAR(1) , 
		  "USER_ID" VARCHAR(10) , 
		  "BRANCH_CODE" VARCHAR(20) , 
		  "AGENT_BRANCH_CODE" VARCHAR(20) , 
		  "FINANCIAL_CODE" VARCHAR(20) , 
		  "LOG_DATE" TIMESTAMP , 
		  "USER_IP" VARCHAR(15) , 
		  "REASON_1" INTEGER , 
		  "REASON_21" INTEGER , 
		  "REASON_22" INTEGER , 
		  "REASON_23" INTEGER , 
		  "REASON_24" INTEGER , 
		  "REASON_25" INTEGER , 
		  "REASON_26" INTEGER , 
		  "REASON_3" INTEGER , 
		  "CHECKED" SMALLINT , 
		  "CHECKUSERID" VARCHAR(10) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_INQUIRY_TASK"

ALTER TABLE "ETCHAP1 "."TB_INQUIRY_TASK" 
	ADD PRIMARY KEY
		("TID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY_TASK"

CREATE INDEX "ETCHAP1 "."IDX_FIND_UNCHECKED" ON "ETCHAP1 "."TB_INQUIRY_TASK" 
		("BRANCH_CODE" ASC,
		 "CHECKED" ASC,
		 "SCHEDULED_TIME" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY_TASK"

CREATE INDEX "ETCHAP1 "."IDX_TASK_QUERY_BY" ON "ETCHAP1 "."TB_INQUIRY_TASK" 
		("QUERY_BY" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY_TASK"

CREATE INDEX "ETCHAP1 "."IDX_TASK_SCHE" ON "ETCHAP1 "."TB_INQUIRY_TASK" 
		("SCHEDULED_TIME" ASC,
		 "IS_SCHEDULED_JOB" ASC,
		 "STATUS" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY_TASK"

CREATE INDEX "ETCHAP1 "."XTB_INQUIRY_TASK50" ON "ETCHAP1 "."TB_INQUIRY_TASK" 
		("BRANCH_CODE" ASC,
		 "USER_ID" ASC,
		 "CHECKED" ASC,
		 "TID" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB_INQUIRY_TASK"

CREATE INDEX "ETCHAP1 "."XTB_INQUIRY_TASK51" ON "ETCHAP1 "."TB_INQUIRY_TASK" 
		("BRANCH_CODE" ASC,
		 "USER_ID" ASC,
		 "CHECKUSERID" ASC,
		 "CHECKED" ASC,
		 "TID" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_BOARD"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_BOARD"  (
		  "ID" BIGINT NOT NULL , 
		  "STARTDATE" TIMESTAMP , 
		  "ENDDATE" TIMESTAMP , 
		  "NOTE" VARCHAR(255) , 
		  "CREATEDTIME" TIMESTAMP )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_BOARD"

ALTER TABLE "ETCHAP1 "."TB_BOARD" 
	ADD PRIMARY KEY
		("ID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"  (
		  "END_DATE" VARCHAR(8) , 
		  "QUERY_ID" VARCHAR(11) , 
		  "DIRECTOR_I" VARCHAR(11) , 
		  "UED_DCC" VARCHAR(4) , 
		  "UED_BCA" VARCHAR(10) , 
		  "UMS_BCC" VARCHAR(3) , 
		  "UMS_BCA" VARCHAR(10) , 
		  "SD_BCC" VARCHAR(3) , 
		  "SD_BCA" VARCHAR(10) , 
		  "CPE_BCC" VARCHAR(3) , 
		  "CPE_BCA" VARCHAR(10) , 
		  "POR_UED_BCC" VARCHAR(4) , 
		  "POR_UED_BCA" VARCHAR(10) , 
		  "POR_UMS_BCC" VARCHAR(3) , 
		  "POR_UMS_BCA" VARCHAR(10) , 
		  "POR_SD_BCC" VARCHAR(3) , 
		  "POR_SD_BCA" VARCHAR(10) , 
		  "POR_CPE_BCC" VARCHAR(3) , 
		  "POR_CPE_BCA" VARCHAR(10) , 
		  "REJECT_DATE" VARCHAR(8) , 
		  "REJECT_END_DATE" VARCHAR(8) , 
		  "TERM_DATE" VARCHAR(8) , 
		  "TERM_EXPR_DATE" VARCHAR(8) , 
		  "FAKE_CNT" VARCHAR(3) , 
		  "ACCT_CNT" VARCHAR(3) , 
		  "ACCT_CODE" VARCHAR(9) , 
		  "ACCT_NO" VARCHAR(9) , 
		  "QRY_RLT" VARCHAR(1) , 
		  "COE_CMT" VARCHAR(1) , 
		  "IMP_INFO_CMT" VARCHAR(1) , 
		  "REL_CNT" VARCHAR(3) , 
		  "DTL_CNT" VARCHAR(4) , 
		  "QRY_NAME" VARCHAR(40) , 
		  "RESERVED" VARCHAR(3) , 
		  "IMP_INFO" VARCHAR(180) , 
		  "BACKUP" VARCHAR(97) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"

CREATE INDEX "ETCHAP1 "."IDX_ATOM_T_001_1" ON "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" 
		("QUERY_ID" ASC,
		 "END_DATE" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"

CREATE INDEX "ETCHAP1 "."IDX_ATOM_T_001_2" ON "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" 
		("QUERY_ID" ASC,
		 "CREATED_TIMESTAMP" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00101" ON "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_001_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"  (
		  "REL_ACCT" VARCHAR(11) , 
		  "REL_NAME" VARCHAR(40) , 
		  "RESERVED" VARCHAR(12) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"

CREATE INDEX "ETCHAP1 "."IDX_ATOM_T_003_1" ON "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" 
		("REL_ACCT" ASC)
		PCTFREE 10 MINPCTUSED 10

		COMPRESS NO DISALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00301" ON "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_003_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116"  (
		  "BC_DATE" VARCHAR(8) , 
		  "BC_BANK_CODE" VARCHAR(9) , 
		  "ACCT_NO" VARCHAR(9) , 
		  "CHK_NO" VARCHAR(9) , 
		  "BC_AMT" VARCHAR(10) , 
		  "BC_REASON" VARCHAR(2) , 
		  "POR_DATE" VARCHAR(8) , 
		  "REMARK" VARCHAR(1) , 
		  "COMMENT" VARCHAR(9) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00401" ON "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_004_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116"  (
		  "END_DATE" VARCHAR(8) , 
		  "REJECT_ID" VARCHAR(8) , 
		  "INQUIRY_ID" VARCHAR(11) , 
		  "DIRECTOR_ID" VARCHAR(11) , 
		  "INQUIRY_RESULT" VARCHAR(1) , 
		  "COE_CMT" VARCHAR(1) , 
		  "LA_BCC" VARCHAR(4) , 
		  "LA_BC_UPORC" VARCHAR(4) , 
		  "LA_BC_UPORA" VARCHAR(10) , 
		  "LA_BC_PORC" VARCHAR(4) , 
		  "LA_BC_PORA" VARCHAR(10) , 
		  "INQ_CNT" VARCHAR(4) , 
		  "ACC_CNT" VARCHAR(4) , 
		  "INQ_NAME" VARCHAR(40) , 
		  "LA_DUER" VARCHAR(4) , 
		  "BACKUP" VARCHAR(316) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00501" ON "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_005_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214"  (
		  "BC_DATE" VARCHAR(8) , 
		  "BC_BANK" VARCHAR(9) , 
		  "AMOUNT" VARCHAR(10) , 
		  "BC_REASON" VARCHAR(2) , 
		  "POR_DATE" VARCHAR(8) , 
		  "RESERVED" VARCHAR(40) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214"

CREATE INDEX "ETCHAP1 "."XATOM_T00601" ON "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214"

CREATE INDEX "SMARTCREDIT"."I_T_006_20060214" ON "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116"  (
		  "INQUIRY_DATE" VARCHAR(8) , 
		  "INQ_ACC_NAME" VARCHAR(40) , 
		  "RESERVED" VARCHAR(17) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00701" ON "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_007_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116"  (
		  "ACC_NAME" VARCHAR(40) , 
		  "ACC_BANK" VARCHAR(9) , 
		  "ACC_DATE" VARCHAR(8) , 
		  "CLEAR_DATE" VARCHAR(8) , 
		  "RESERVED" VARCHAR(10) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL , 
		  "ACC_NUM" VARCHAR(9) )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00801" ON "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_008_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116"  (
		  "END_DATE" VARCHAR(8) , 
		  "INQ_ID" VARCHAR(11) , 
		  "DIRECTOR_ID" VARCHAR(11) , 
		  "INQ_RESULT" VARCHAR(1) , 
		  "COE_COM" VARCHAR(1) , 
		  "LA_BC_UPORD1" VARCHAR(8) , 
		  "LA_BC_UPORA1" VARCHAR(10) , 
		  "LA_BC_UPORR1" VARCHAR(2) , 
		  "LA_BC_UPORD2" VARCHAR(8) , 
		  "LA_BC_UPORA2" VARCHAR(10) , 
		  "LA_BC_UPORR2" VARCHAR(2) , 
		  "LA_BC_UPORD3" VARCHAR(8) , 
		  "LA_BC_UPORA3" VARCHAR(10) , 
		  "LA_BC_UPORR3" VARCHAR(2) , 
		  "LA_BC_PORD1" VARCHAR(8) , 
		  "LA_BC_PORA1" VARCHAR(10) , 
		  "LA_BC_POD1" VARCHAR(8) , 
		  "LA_BC_PORR1" VARCHAR(2) , 
		  "LA_BC_PORD2" VARCHAR(8) , 
		  "LA_BC_PORA2" VARCHAR(10) , 
		  "LA_BC_POD2" VARCHAR(8) , 
		  "LA_BC_PORR2" VARCHAR(2) , 
		  "LA_BC_PORD3" VARCHAR(8) , 
		  "LA_BC_PORA3" VARCHAR(10) , 
		  "LA_BC_POD3" VARCHAR(8) , 
		  "LA_BC_PORR3" VARCHAR(2) , 
		  "INQ_CNT" VARCHAR(4) , 
		  "INQ_DATE1" VARCHAR(8) , 
		  "INQ_ACC_NAME1" VARCHAR(40) , 
		  "INQ_DATE2" VARCHAR(8) , 
		  "INQ_ACC_NAME2" VARCHAR(40) , 
		  "INQ_DATE3" VARCHAR(8) , 
		  "INQ_ACC_NAME3" VARCHAR(40) , 
		  "INQ_NAME" VARCHAR(40) , 
		  "LA_DUER" VARCHAR(4) , 
		  "BACKUP" VARCHAR(66) , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "SEQ_NO" INTEGER NOT NULL , 
		  "CREATED_TIMESTAMP" TIMESTAMP NOT NULL , 
		  "EXPIRY_DATE" TIMESTAMP NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116"

CREATE INDEX "ETCHAP1 "."XATOM_T00901" ON "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" 
		("CREATED_TIMESTAMP" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116"

CREATE INDEX "SMARTCREDIT"."I_T_009_20060116" ON "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" 
		("QUERY_CONDITION" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1"  (
		  "DID" BIGINT NOT NULL , 
		  "UI_KEY" VARCHAR(25) NOT NULL , 
		  "ITEM_CODE" VARCHAR(5) NOT NULL , 
		  "QUERY_CONDITION" VARCHAR(25) NOT NULL , 
		  "LENGTH" INTEGER , 
		  "DESCRIPTION" VARCHAR(50) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1"

ALTER TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" 
	ADD PRIMARY KEY
		("DID");


------------------------------------------------
-- DDL Statements for Table "DB2INST1"."EXPLAIN_INSTANCE"
------------------------------------------------
 

CREATE TABLE "DB2INST1"."EXPLAIN_INSTANCE"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_OPTION" CHAR(1) NOT NULL , 
		  "SNAPSHOT_TAKEN" CHAR(1) NOT NULL , 
		  "DB2_VERSION" CHAR(7) NOT NULL , 
		  "SQL_TYPE" CHAR(1) NOT NULL , 
		  "QUERYOPT" INTEGER NOT NULL , 
		  "BLOCK" CHAR(1) NOT NULL , 
		  "ISOLATION" CHAR(2) NOT NULL , 
		  "BUFFPAGE" INTEGER NOT NULL , 
		  "AVG_APPLS" INTEGER NOT NULL , 
		  "SORTHEAP" INTEGER NOT NULL , 
		  "LOCKLIST" INTEGER NOT NULL , 
		  "MAXLOCKS" SMALLINT NOT NULL , 
		  "LOCKS_AVAIL" INTEGER NOT NULL , 
		  "CPU_SPEED" DOUBLE NOT NULL , 
		  "REMARKS" VARCHAR(254) , 
		  "DBHEAP" INTEGER NOT NULL , 
		  "COMM_SPEED" DOUBLE NOT NULL , 
		  "PARALLELISM" CHAR(2) NOT NULL , 
		  "DATAJOINER" CHAR(1) NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "DB2INST1"."EXPLAIN_INSTANCE"

ALTER TABLE "DB2INST1"."EXPLAIN_INSTANCE" 
	ADD PRIMARY KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION");


------------------------------------------------
-- DDL Statements for Table "DB2INST1"."EXPLAIN_STATEMENT"
------------------------------------------------
 

CREATE TABLE "DB2INST1"."EXPLAIN_STATEMENT"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "QUERYNO" INTEGER NOT NULL , 
		  "QUERYTAG" CHAR(20) NOT NULL , 
		  "STATEMENT_TYPE" CHAR(2) NOT NULL , 
		  "UPDATABLE" CHAR(1) NOT NULL , 
		  "DELETABLE" CHAR(1) NOT NULL , 
		  "TOTAL_COST" DOUBLE NOT NULL , 
		  "STATEMENT_TEXT" CLOB(2097152) NOT LOGGED NOT COMPACT NOT NULL , 
		  "SNAPSHOT" BLOB(10485760) NOT LOGGED NOT COMPACT , 
		  "QUERY_DEGREE" INTEGER NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "DB2INST1"."EXPLAIN_STATEMENT"

ALTER TABLE "DB2INST1"."EXPLAIN_STATEMENT" 
	ADD PRIMARY KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO");



-- DDL Statements for Indexes on Table "DB2INST1"."EXPLAIN_STATEMENT"

CREATE INDEX "DB2INST1"."STMT_I1" ON "DB2INST1"."EXPLAIN_STATEMENT" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "DB2INST1"."EXPLAIN_ARGUMENT"
------------------------------------------------
 

CREATE TABLE "DB2INST1"."EXPLAIN_ARGUMENT"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "OPERATOR_ID" INTEGER NOT NULL , 
		  "ARGUMENT_TYPE" CHAR(8) NOT NULL , 
		  "ARGUMENT_VALUE" VARCHAR(1024) , 
		  "LONG_ARGUMENT_VALUE" CLOB(2097152) NOT LOGGED NOT COMPACT )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "DB2INST1"."EXPLAIN_ARGUMENT"

CREATE INDEX "DB2INST1"."ARG_I1" ON "DB2INST1"."EXPLAIN_ARGUMENT" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC,
		 "OPERATOR_ID" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "DB2INST1"."EXPLAIN_OBJECT"
------------------------------------------------
 

CREATE TABLE "DB2INST1"."EXPLAIN_OBJECT"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "OBJECT_SCHEMA" VARCHAR(128) NOT NULL , 
		  "OBJECT_NAME" VARCHAR(128) NOT NULL , 
		  "OBJECT_TYPE" CHAR(2) NOT NULL , 
		  "CREATE_TIME" TIMESTAMP , 
		  "STATISTICS_TIME" TIMESTAMP , 
		  "COLUMN_COUNT" SMALLINT NOT NULL , 
		  "ROW_COUNT" BIGINT NOT NULL , 
		  "WIDTH" INTEGER NOT NULL , 
		  "PAGES" INTEGER NOT NULL , 
		  "DISTINCT" CHAR(1) NOT NULL , 
		  "TABLESPACE_NAME" VARCHAR(128) , 
		  "OVERHEAD" DOUBLE NOT NULL , 
		  "TRANSFER_RATE" DOUBLE NOT NULL , 
		  "PREFETCHSIZE" INTEGER NOT NULL , 
		  "EXTENTSIZE" INTEGER NOT NULL , 
		  "CLUSTER" DOUBLE NOT NULL , 
		  "NLEAF" INTEGER NOT NULL , 
		  "NLEVELS" INTEGER NOT NULL , 
		  "FULLKEYCARD" BIGINT NOT NULL , 
		  "OVERFLOW" INTEGER NOT NULL , 
		  "FIRSTKEYCARD" BIGINT NOT NULL , 
		  "FIRST2KEYCARD" BIGINT NOT NULL , 
		  "FIRST3KEYCARD" BIGINT NOT NULL , 
		  "FIRST4KEYCARD" BIGINT NOT NULL , 
		  "SEQUENTIAL_PAGES" INTEGER NOT NULL , 
		  "DENSITY" INTEGER NOT NULL , 
		  "STATS_SRC" CHAR(1) NOT NULL , 
		  "AVERAGE_SEQUENCE_GAP" DOUBLE NOT NULL , 
		  "AVERAGE_SEQUENCE_FETCH_GAP" DOUBLE NOT NULL , 
		  "AVERAGE_SEQUENCE_PAGES" DOUBLE NOT NULL , 
		  "AVERAGE_SEQUENCE_FETCH_PAGES" DOUBLE NOT NULL , 
		  "AVERAGE_RANDOM_PAGES" DOUBLE NOT NULL , 
		  "AVERAGE_RANDOM_FETCH_PAGES" DOUBLE NOT NULL , 
		  "NUMRIDS" BIGINT NOT NULL , 
		  "NUMRIDS_DELETED" BIGINT NOT NULL , 
		  "NUM_EMPTY_LEAFS" BIGINT NOT NULL , 
		  "ACTIVE_BLOCKS" BIGINT NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "DB2INST1"."EXPLAIN_OBJECT"

CREATE INDEX "DB2INST1"."OBJ_I1" ON "DB2INST1"."EXPLAIN_OBJECT" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "DB2INST1"."EXPLAIN_OPERATOR"
------------------------------------------------
 

CREATE TABLE "DB2INST1"."EXPLAIN_OPERATOR"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "OPERATOR_ID" INTEGER NOT NULL , 
		  "OPERATOR_TYPE" CHAR(6) NOT NULL , 
		  "TOTAL_COST" DOUBLE NOT NULL , 
		  "IO_COST" DOUBLE NOT NULL , 
		  "CPU_COST" DOUBLE NOT NULL , 
		  "FIRST_ROW_COST" DOUBLE NOT NULL , 
		  "RE_TOTAL_COST" DOUBLE NOT NULL , 
		  "RE_IO_COST" DOUBLE NOT NULL , 
		  "RE_CPU_COST" DOUBLE NOT NULL , 
		  "COMM_COST" DOUBLE NOT NULL , 
		  "FIRST_COMM_COST" DOUBLE NOT NULL , 
		  "BUFFERS" DOUBLE NOT NULL , 
		  "REMOTE_TOTAL_COST" DOUBLE NOT NULL , 
		  "REMOTE_COMM_COST" DOUBLE NOT NULL )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "DB2INST1"."EXPLAIN_OPERATOR"

CREATE INDEX "DB2INST1"."OPR_I1" ON "DB2INST1"."EXPLAIN_OPERATOR" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC,
		 "OPERATOR_ID" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "DB2INST1"."EXPLAIN_PREDICATE"
------------------------------------------------
 

CREATE TABLE "DB2INST1"."EXPLAIN_PREDICATE"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "OPERATOR_ID" INTEGER NOT NULL , 
		  "PREDICATE_ID" INTEGER NOT NULL , 
		  "HOW_APPLIED" CHAR(5) NOT NULL , 
		  "WHEN_EVALUATED" CHAR(3) NOT NULL , 
		  "RELOP_TYPE" CHAR(2) NOT NULL , 
		  "SUBQUERY" CHAR(1) NOT NULL , 
		  "FILTER_FACTOR" DOUBLE NOT NULL , 
		  "PREDICATE_TEXT" CLOB(2097152) NOT LOGGED NOT COMPACT )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "DB2INST1"."EXPLAIN_PREDICATE"

CREATE INDEX "DB2INST1"."PRD_I1" ON "DB2INST1"."EXPLAIN_PREDICATE" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC,
		 "OPERATOR_ID" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "DB2INST1"."EXPLAIN_STREAM"
------------------------------------------------
 

CREATE TABLE "DB2INST1"."EXPLAIN_STREAM"  (
		  "EXPLAIN_REQUESTER" VARCHAR(128) NOT NULL , 
		  "EXPLAIN_TIME" TIMESTAMP NOT NULL , 
		  "SOURCE_NAME" VARCHAR(128) NOT NULL , 
		  "SOURCE_SCHEMA" VARCHAR(128) NOT NULL , 
		  "SOURCE_VERSION" VARCHAR(64) NOT NULL , 
		  "EXPLAIN_LEVEL" CHAR(1) NOT NULL , 
		  "STMTNO" INTEGER NOT NULL , 
		  "SECTNO" INTEGER NOT NULL , 
		  "STREAM_ID" INTEGER NOT NULL , 
		  "SOURCE_TYPE" CHAR(1) NOT NULL , 
		  "SOURCE_ID" INTEGER NOT NULL , 
		  "TARGET_TYPE" CHAR(1) NOT NULL , 
		  "TARGET_ID" INTEGER NOT NULL , 
		  "OBJECT_SCHEMA" VARCHAR(128) , 
		  "OBJECT_NAME" VARCHAR(128) , 
		  "STREAM_COUNT" DOUBLE NOT NULL , 
		  "COLUMN_COUNT" SMALLINT NOT NULL , 
		  "PREDICATE_ID" INTEGER NOT NULL , 
		  "COLUMN_NAMES" CLOB(2097152) NOT LOGGED NOT COMPACT , 
		  "PMID" SMALLINT NOT NULL , 
		  "SINGLE_NODE" CHAR(5) , 
		  "PARTITION_COLUMNS" CLOB(2097152) NOT LOGGED NOT COMPACT )   
		 IN "ETCHTABSP" ; 






-- DDL Statements for Indexes on Table "DB2INST1"."EXPLAIN_STREAM"

CREATE INDEX "DB2INST1"."STM_I1" ON "DB2INST1"."EXPLAIN_STREAM" 
		("EXPLAIN_TIME" ASC,
		 "EXPLAIN_LEVEL" ASC,
		 "STMTNO" ASC,
		 "SECTNO" ASC)
		
		COMPRESS NO DISALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."LOGFILE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."LOGFILE"  (
		  "QDATE" CHAR(9) NOT NULL , 
		  "MSGID" CHAR(12) NOT NULL , 
		  "TXID" CHAR(4) NOT NULL , 
		  "PRODUCTID" CHAR(2) , 
		  "QUERYKEY1" CHAR(10) , 
		  "QUERYKEY2" CHAR(10) , 
		  "QUERYKEY3" CHAR(40) , 
		  "QUERYKEY4" CHAR(9) , 
		  "QUERYKEY5" CHAR(9) , 
		  "PRICE" DECIMAL(6,2) , 
		  "TOTCH" CHAR(1) , 
		  "PROCTIME1" BIGINT , 
		  "PROCTIME2" BIGINT , 
		  "PLATFORM" CHAR(1) , 
		  "CHARGEID" CHAR(9) , 
		  "RC" CHAR(4) , 
		  "REQUESTID" CHAR(10) , 
		  "UCNAME" CHAR(10) , 
		  "DIVISION" CHAR(4) , 
		  "DPNAME" CHAR(20) , 
		  "RFQDATE" CHAR(9) , 
		  "REASON" CHAR(3) , 
		  "FORCEFLAG" CHAR(1) WITH DEFAULT 'A' , 
		  "CHECKER" CHAR(10) , 
		  "CHECKERID" CHAR(15) , 
		  "TEAMNO" CHAR(2) , 
		  "CRDATE" TIMESTAMP WITH DEFAULT CURRENT TIMESTAMP , 
		  "QUERY_CONDITION" VARCHAR(200) , 
		  "HRC" CHAR(4) WITH DEFAULT '0001' , 
		  "XRC" CHAR(4) WITH DEFAULT '0001' , 
		  "QRC" CHAR(4) WITH DEFAULT '0001' )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."LOGFILE"

ALTER TABLE "ETCHAP1 "."LOGFILE" 
	ADD CONSTRAINT "P_LOGFILE" PRIMARY KEY
		("QDATE",
		 "MSGID",
		 "TXID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE01" ON "ETCHAP1 "."LOGFILE" 
		("TOTCH" ASC,
		 "DIVISION" ASC,
		 "RC" ASC,
		 "QDATE" ASC,
		 "PRICE" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE02" ON "ETCHAP1 "."LOGFILE" 
		("TXID" ASC,
		 "TOTCH" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE03" ON "ETCHAP1 "."LOGFILE" 
		("QDATE" ASC,
		 "RC" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE04" ON "ETCHAP1 "."LOGFILE" 
		("RC" ASC,
		 "DIVISION" ASC,
		 "QDATE" ASC,
		 "REQUESTID" ASC,
		 "QUERYKEY1" ASC,
		 "MSGID" ASC,
		 "TXID" ASC,
		 "CHECKER" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE05" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY1" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE06" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY2" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE07" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY3" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE08" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY4" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."LOGFILE"

CREATE INDEX "ETCHAP1 "."XLOGFILE09" ON "ETCHAP1 "."LOGFILE" 
		("QUERYKEY5" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TCHDATA"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TCHDATA"  (
		  "QDATE" CHAR(9) NOT NULL , 
		  "TXID" CHAR(4) NOT NULL , 
		  "MSGID" CHAR(12) NOT NULL , 
		  "DATATYPE" CHAR(1) NOT NULL , 
		  "QUERYKEY1" CHAR(10) , 
		  "QUERYKEY2" CHAR(10) , 
		  "QUERYKEY3" CHAR(40) , 
		  "QUERYKEY4" CHAR(9) , 
		  "QUERYKEY5" CHAR(9) , 
		  "DATA" CLOB(4194304) LOGGED NOT COMPACT )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TCHDATA"

ALTER TABLE "ETCHAP1 "."TCHDATA" 
	ADD CONSTRAINT "P_TCHDATA" PRIMARY KEY
		("QDATE",
		 "TXID",
		 "MSGID",
		 "DATATYPE");



-- DDL Statements for Indexes on Table "ETCHAP1 "."TCHDATA"

CREATE INDEX "ETCHAP1 "."XTCHDATA01" ON "ETCHAP1 "."TCHDATA" 
		("QDATE" ASC,
		 "TXID" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."TCHDATA"

CREATE INDEX "ETCHAP1 "."XTCHDATA02" ON "ETCHAP1 "."TCHDATA" 
		("QDATE" ASC,
		 "TXID" ASC,
		 "DATATYPE" ASC,
		 "QUERYKEY1" ASC,
		 "QUERYKEY2" ASC,
		 "QUERYKEY3" ASC,
		 "QUERYKEY4" ASC,
		 "QUERYKEY5" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."ATOM"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."ATOM"  (
		  "ATOMID" CHAR(3) NOT NULL , 
		  "COLNAME" VARCHAR(16) NOT NULL , 
		  "DATATYPE" CHAR(1) , 
		  "DATALEN" INTEGER , 
		  "CNAME" VARCHAR(40) , 
		  "COLSEQ" INTEGER )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."ATOM"

ALTER TABLE "ETCHAP1 "."ATOM" 
	ADD CONSTRAINT "P_ATOM" PRIMARY KEY
		("ATOMID",
		 "COLNAME");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."ATOMPROFILE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."ATOMPROFILE"  (
		  "ATOMID" CHAR(3) NOT NULL , 
		  "PRICE" DECIMAL(6,2) , 
		  "CNAME" VARCHAR(40) , 
		  "TBNAME" VARCHAR(40) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."ATOMPROFILE"

ALTER TABLE "ETCHAP1 "."ATOMPROFILE" 
	ADD CONSTRAINT "P_ATOMPROFILE" PRIMARY KEY
		("ATOMID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TXID"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TXID"  (
		  "TXID" CHAR(4) NOT NULL , 
		  "ATOMID" CHAR(3) NOT NULL )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TXID"

ALTER TABLE "ETCHAP1 "."TXID" 
	ADD CONSTRAINT "P_TXID" PRIMARY KEY
		("TXID",
		 "ATOMID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."TXIDPROFILE"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."TXIDPROFILE"  (
		  "TXID" CHAR(4) NOT NULL , 
		  "PRICE" DECIMAL(6,2) , 
		  "CNAME" VARCHAR(60) , 
		  "REFCYCLE" INTEGER , 
		  "QTYPE" CHAR(1) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."TXIDPROFILE"

ALTER TABLE "ETCHAP1 "."TXIDPROFILE" 
	ADD CONSTRAINT "P_TXIDPROFILE" PRIMARY KEY
		("TXID");


------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."SCH_MAIN"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."SCH_MAIN"  (
		  "SCH_SNO" BIGINT NOT NULL , 
		  "SCH_TIME" TIMESTAMP , 
		  "SCH_STATUS" CHAR(4) , 
		  "ITEM_CNT" INTEGER , 
		  "CRDATE" TIMESTAMP WITH DEFAULT CURRENT TIMESTAMP , 
		  "ISDEL" CHAR(1) , 
		  "DELTIME" TIMESTAMP , 
		  "TEAMNO" CHAR(2) , 
		  "REQUESTID" CHAR(10) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SCH_MAIN"

ALTER TABLE "ETCHAP1 "."SCH_MAIN" 
	ADD CONSTRAINT "P_SCH_MAIN" PRIMARY KEY
		("SCH_SNO");



-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_MAIN"

CREATE INDEX "ETCHAP1 "."XSCH_MAIN01" ON "ETCHAP1 "."SCH_MAIN" 
		("ISDEL" ASC,
		 "SCH_TIME" ASC,
		 "TEAMNO" ASC,
		 "CRDATE" ASC,
		 "ITEM_CNT" ASC,
		 "SCH_SNO" ASC,
		 "SCH_STATUS" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_MAIN"

CREATE INDEX "ETCHAP1 "."XSCH_MAIN02" ON "ETCHAP1 "."SCH_MAIN" 
		("REQUESTID" ASC,
		 "ISDEL" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_MAIN"

CREATE INDEX "ETCHAP1 "."XSCH_MAIN03" ON "ETCHAP1 "."SCH_MAIN" 
		("ISDEL" ASC,
		 "REQUESTID" ASC,
		 "SCH_TIME" DESC,
		 "CRDATE" ASC,
		 "ITEM_CNT" ASC,
		 "SCH_STATUS" ASC,
		 "SCH_SNO" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."SCH_ITEM"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."SCH_ITEM"  (
		  "DETAIL_SNO" BIGINT NOT NULL , 
		  "SCH_SNO" BIGINT NOT NULL , 
		  "MSGID" CHAR(12) , 
		  "TXID" CHAR(4) , 
		  "PRODUCTID" CHAR(2) , 
		  "QUERYKEY1" VARCHAR(10) , 
		  "QUERYKEY2" VARCHAR(10) , 
		  "QUERYKEY3" VARCHAR(40) , 
		  "QUERYKEY4" VARCHAR(9) , 
		  "QUERYKEY5" VARCHAR(9) , 
		  "CHARGEID" CHAR(9) , 
		  "RC" CHAR(4) , 
		  "REQUESTID" CHAR(10) , 
		  "UCNAME" VARCHAR(10) , 
		  "DIVISION" CHAR(4) , 
		  "DPNAME" VARCHAR(20) , 
		  "FORCEFLAG" CHAR(1) , 
		  "BEGIN_DATE" TIMESTAMP , 
		  "END_DATE" TIMESTAMP , 
		  "CRDATE" TIMESTAMP WITH DEFAULT CURRENT TIMESTAMP , 
		  "ISDEL" CHAR(1) , 
		  "DELTIME" TIMESTAMP )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SCH_ITEM"

ALTER TABLE "ETCHAP1 "."SCH_ITEM" 
	ADD CONSTRAINT "P_SCH_ITEM" PRIMARY KEY
		("DETAIL_SNO");



-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM01" ON "ETCHAP1 "."SCH_ITEM" 
		("SCH_SNO" ASC,
		 "ISDEL" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM02" ON "ETCHAP1 "."SCH_ITEM" 
		("QUERYKEY1" ASC,
		 "SCH_SNO" DESC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM03" ON "ETCHAP1 "."SCH_ITEM" 
		("QUERYKEY1" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM04" ON "ETCHAP1 "."SCH_ITEM" 
		("QUERYKEY4" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."SCH_ITEM"

CREATE INDEX "ETCHAP1 "."XSCH_ITEM05" ON "ETCHAP1 "."SCH_ITEM" 
		("QUERYKEY5" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."BANKDATA"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."BANKDATA"  (
		  "BID" CHAR(4) NOT NULL , 
		  "CNAME" CHAR(40) , 
		  "CHARGEID" VARCHAR(4) , 
		  "AREA" CHAR(4) )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."BANKDATA"

ALTER TABLE "ETCHAP1 "."BANKDATA" 
	ADD CONSTRAINT "P_BANKDATA" PRIMARY KEY
		("BID");



-- DDL Statements for Indexes on Table "ETCHAP1 "."BANKDATA"

CREATE UNIQUE INDEX "ETCHAP1 "."XBANKDATA01" ON "ETCHAP1 "."BANKDATA" 
		("BID" ASC)
		INCLUDE ("CNAME" )
		
		COMPRESS NO ALLOW REVERSE SCANS;

-- DDL Statements for Indexes on Table "ETCHAP1 "."BANKDATA"

CREATE INDEX "ETCHAP1 "."XBANKDATA02" ON "ETCHAP1 "."BANKDATA" 
		("AREA" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;
------------------------------------------------
-- DDL Statements for Table "ETCHAP1 "."SYSPARAM"
------------------------------------------------
 

CREATE TABLE "ETCHAP1 "."SYSPARAM"  (
		  "PARAM" VARCHAR(30) NOT NULL , 
		  "PARAMVALUE" VARCHAR(1000) NOT NULL , 
		  "PARAMDESC" VARCHAR(300) , 
		  "MODIFYBY" VARCHAR(10) , 
		  "MODIFYTIME" TIMESTAMP )   
		 IN "ETCHTABSP" ; 


-- DDL Statements for Primary Key on Table "ETCHAP1 "."SYSPARAM"

ALTER TABLE "ETCHAP1 "."SYSPARAM" 
	ADD CONSTRAINT "P_SYSPARAM" PRIMARY KEY
		("PARAM");


-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."EXPLAIN_STATEMENT"

ALTER TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" 
	ADD CONSTRAINT "SQL060425154751560" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION")
	REFERENCES "ETCHAP1 "."EXPLAIN_INSTANCE"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."EXPLAIN_ARGUMENT"

ALTER TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" 
	ADD CONSTRAINT "SQL060425154752400" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "ETCHAP1 "."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."EXPLAIN_OBJECT"

ALTER TABLE "ETCHAP1 "."EXPLAIN_OBJECT" 
	ADD CONSTRAINT "SQL060425154753070" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "ETCHAP1 "."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."EXPLAIN_OPERATOR"

ALTER TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" 
	ADD CONSTRAINT "SQL060425154753730" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "ETCHAP1 "."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."EXPLAIN_PREDICATE"

ALTER TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" 
	ADD CONSTRAINT "SQL060425154754810" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "ETCHAP1 "."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."EXPLAIN_STREAM"

ALTER TABLE "ETCHAP1 "."EXPLAIN_STREAM" 
	ADD CONSTRAINT "SQL060425154755940" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "ETCHAP1 "."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."USERS"

ALTER TABLE "ETCHAP1 "."USERS" 
	ADD CONSTRAINT "FK41368A10B12B12A2" FOREIGN KEY
		("TEMPINFO")
	REFERENCES "ETCHAP1 "."USERINFO"
		("IID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."USERS" 
	ADD CONSTRAINT "FK41368A10E5EFA68F" FOREIGN KEY
		("DEFAULTINFO")
	REFERENCES "ETCHAP1 "."USERINFO"
		("IID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."OPERATORS"

ALTER TABLE "ETCHAP1 "."OPERATORS" 
	ADD CONSTRAINT "FK9DBDA4571ADCA" FOREIGN KEY
		("OID")
	REFERENCES "ETCHAP1 "."ORGANIZATIONUNIT"
		("OID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."OPERATORS" 
	ADD CONSTRAINT "FK9DBDA457CE2B3226" FOREIGN KEY
		("USERID")
	REFERENCES "ETCHAP1 "."USERS"
		("USERID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."DOCUMENTFORROLE"

ALTER TABLE "ETCHAP1 "."DOCUMENTFORROLE" 
	ADD CONSTRAINT "FK56E40F241B90D" FOREIGN KEY
		("RID")
	REFERENCES "ETCHAP1 "."SECURITYROLE"
		("RID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."ORGANIZATIONUNIT"

ALTER TABLE "ETCHAP1 "."ORGANIZATIONUNIT" 
	ADD CONSTRAINT "FKA5CF842F1847F" FOREIGN KEY
		("DID")
	REFERENCES "ETCHAP1 "."ORGANIZATIONUNIT"
		("OID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."SECURITYROLE"

ALTER TABLE "ETCHAP1 "."SECURITYROLE" 
	ADD CONSTRAINT "FKB7BD0AEE1793C" FOREIGN KEY
		("AID")
	REFERENCES "ETCHAP1 "."APPLICATION"
		("AID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."AGENT"

ALTER TABLE "ETCHAP1 "."AGENT" 
	ADD CONSTRAINT "FK3C452E51ADCA" FOREIGN KEY
		("OID")
	REFERENCES "ETCHAP1 "."USERDEPARTMENTROLE"
		("ID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."AGENT" 
	ADD CONSTRAINT "FK3C452E592ABB5A8" FOREIGN KEY
		("ID4DEFAULT")
	REFERENCES "ETCHAP1 "."USERDEPARTMENTROLE"
		("ID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."AGENT" 
	ADD CONSTRAINT "FK3C452E5CE2B3226" FOREIGN KEY
		("USERID")
	REFERENCES "ETCHAP1 "."USERS"
		("USERID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."AGENT" 
	ADD CONSTRAINT "FK3C452E5D1B" FOREIGN KEY
		("ID")
	REFERENCES "ETCHAP1 "."USERDEPARTMENTROLE"
		("ID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."DOCUMENTFORGROUP"

ALTER TABLE "ETCHAP1 "."DOCUMENTFORGROUP" 
	ADD CONSTRAINT "FK85043D911ADCA" FOREIGN KEY
		("OID")
	REFERENCES "ETCHAP1 "."ORGANIZATIONUNIT"
		("OID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."USERINFO"

ALTER TABLE "ETCHAP1 "."USERINFO" 
	ADD CONSTRAINT "FKE9C999F1CE2B3226" FOREIGN KEY
		("USERID")
	REFERENCES "ETCHAP1 "."USERS"
		("USERID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."USERINFO" 
	ADD CONSTRAINT "FKE9C999F1D1B" FOREIGN KEY
		("ID")
	REFERENCES "ETCHAP1 "."USERDEPARTMENTROLE"
		("ID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."ROLEGROUP"

ALTER TABLE "ETCHAP1 "."ROLEGROUP" 
	ADD CONSTRAINT "FKC4C937911B90D" FOREIGN KEY
		("RID")
	REFERENCES "ETCHAP1 "."SECURITYROLE"
		("RID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."ROLEGROUP" 
	ADD CONSTRAINT "FKC4C937913068AF" FOREIGN KEY
		("GID1")
	REFERENCES "ETCHAP1 "."ORGANIZATIONUNIT"
		("OID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."USERDEPARTMENTROLE"

ALTER TABLE "ETCHAP1 "."USERDEPARTMENTROLE" 
	ADD CONSTRAINT "FK3EDE84AB19744" FOREIGN KEY
		("IID")
	REFERENCES "ETCHAP1 "."USERINFO"
		("IID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."USERDEPARTMENTROLE" 
	ADD CONSTRAINT "FK3EDE84AB3068AF" FOREIGN KEY
		("GID1")
	REFERENCES "ETCHAP1 "."ORGANIZATIONUNIT"
		("OID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."STRINGVALUEFORUSER"

ALTER TABLE "ETCHAP1 "."STRINGVALUEFORUSER" 
	ADD CONSTRAINT "FKAAAFC54CCE2B3226" FOREIGN KEY
		("USERID")
	REFERENCES "ETCHAP1 "."USERS"
		("USERID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."PASSWDMEMENTO"

ALTER TABLE "ETCHAP1 "."PASSWDMEMENTO" 
	ADD CONSTRAINT "FK698143A3CE2B3226" FOREIGN KEY
		("USERID")
	REFERENCES "ETCHAP1 "."USERCARETAKER"
		("USERID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."ACCESSURL"

ALTER TABLE "ETCHAP1 "."ACCESSURL" 
	ADD CONSTRAINT "FKF961094B1B90D" FOREIGN KEY
		("RID")
	REFERENCES "ETCHAP1 "."SECURITYROLE"
		("RID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."STRINGVALUEFOROU"

ALTER TABLE "ETCHAP1 "."STRINGVALUEFOROU" 
	ADD CONSTRAINT "FK6A3353871ADCA" FOREIGN KEY
		("OID")
	REFERENCES "ETCHAP1 "."ORGANIZATIONUNIT"
		("OID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP"

ALTER TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" 
	ADD CONSTRAINT "FKC012F3DB8025CA58" FOREIGN KEY
		("XML_CACHE2")
	REFERENCES "ETCHAP1 "."TB_CACHE_BACKUP"
		("CID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" 
	ADD CONSTRAINT "FKC012F3DB84DC3A32" FOREIGN KEY
		("ROW_DATA_CACHE")
	REFERENCES "ETCHAP1 "."TB_CACHE_BACKUP"
		("CID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" 
	ADD CONSTRAINT "FKC012F3DB9085591A" FOREIGN KEY
		("XML_CACHE")
	REFERENCES "ETCHAP1 "."TB_CACHE_BACKUP"
		("CID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" 
	ADD CONSTRAINT "FKC012F3DBF41B6DCE" FOREIGN KEY
		("HTML_CACHE")
	REFERENCES "ETCHAP1 "."TB_CACHE_BACKUP"
		("CID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."TB_SUB_ITEM_DEFINITION"

ALTER TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" 
	ADD CONSTRAINT "FK4361D68F1049E" FOREIGN KEY
		("CID")
	REFERENCES "ETCHAP1 "."TB_ITEM_DEFINITION"
		("IID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" 
	ADD CONSTRAINT "FK4361D68F5D19901" FOREIGN KEY
		("TARGET_ITEM")
	REFERENCES "ETCHAP1 "."TB_ITEM_DEFINITION"
		("IID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."TB_ATOM_DEFINITION_INFO"

ALTER TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" 
	ADD CONSTRAINT "FKF319B65D970EB81B" FOREIGN KEY
		("ATOM_CODE")
	REFERENCES "ETCHAP1 "."TB_ATOM_DEFINITION"
		("ATOM_CODE")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."TB_INQUIRY_FEE"

ALTER TABLE "ETCHAP1 "."TB_INQUIRY_FEE" 
	ADD CONSTRAINT "FK67235C7D11B24" FOREIGN KEY
		("IID")
	REFERENCES "ETCHAP1 "."TB_ITEM_DEFINITION"
		("IID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."TB_ITEM_DEFINITION"

ALTER TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" 
	ADD CONSTRAINT "FK78332E2E283BC0E6" FOREIGN KEY
		("ITEM_TYPE")
	REFERENCES "ETCHAP1 "."TB_ITEM_TYPE"
		("TID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" 
	ADD CONSTRAINT "FK78332E2EF3C9F5F1" FOREIGN KEY
		("PROVIDER")
	REFERENCES "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER"
		("PID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."TB_INQUIRY"

ALTER TABLE "ETCHAP1 "."TB_INQUIRY" 
	ADD CONSTRAINT "FK8DAB78D61446F" FOREIGN KEY
		("TID")
	REFERENCES "ETCHAP1 "."TB_INQUIRY_TASK"
		("TID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."TB_INQUIRY" 
	ADD CONSTRAINT "FK8DAB78D6776869FF" FOREIGN KEY
		("ITEM_DEFINITION")
	REFERENCES "ETCHAP1 "."TB_ITEM_DEFINITION"
		("IID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."TB_INQUIRY" 
	ADD CONSTRAINT "FK8DAB78D67CE1A7D6" FOREIGN KEY
		("COMBO_INQUIRY")
	REFERENCES "ETCHAP1 "."TB_INQUIRY"
		("IID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."TB_INQUIRY" 
	ADD CONSTRAINT "FK8DAB78D69AAED217" FOREIGN KEY
		("REQUEST_MESSAGE")
	REFERENCES "ETCHAP1 "."TB_MESSAGE"
		("MID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."TB_COMBO_CODITION"

ALTER TABLE "ETCHAP1 "."TB_COMBO_CODITION" 
	ADD CONSTRAINT "FKC5E3BE27140AE" FOREIGN KEY
		("SID")
	REFERENCES "ETCHAP1 "."TB_SUB_ITEM_DEFINITION"
		("SID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "ETCHAP1 "."TB_ITEM_ATOM_MAPPING"

ALTER TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" 
	ADD CONSTRAINT "FK5B9C7DDB11B24" FOREIGN KEY
		("IID")
	REFERENCES "ETCHAP1 "."TB_ITEM_DEFINITION"
		("IID")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

ALTER TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" 
	ADD CONSTRAINT "FK5B9C7DDB970EB81B" FOREIGN KEY
		("ATOM_CODE")
	REFERENCES "ETCHAP1 "."TB_ATOM_DEFINITION"
		("ATOM_CODE")
	ON DELETE NO ACTION
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "DB2INST1"."EXPLAIN_STATEMENT"

ALTER TABLE "DB2INST1"."EXPLAIN_STATEMENT" 
	ADD CONSTRAINT "SQL060523144718040" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION")
	REFERENCES "DB2INST1"."EXPLAIN_INSTANCE"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "DB2INST1"."EXPLAIN_ARGUMENT"

ALTER TABLE "DB2INST1"."EXPLAIN_ARGUMENT" 
	ADD CONSTRAINT "SQL060523144719250" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "DB2INST1"."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "DB2INST1"."EXPLAIN_OBJECT"

ALTER TABLE "DB2INST1"."EXPLAIN_OBJECT" 
	ADD CONSTRAINT "SQL060523144720030" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "DB2INST1"."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "DB2INST1"."EXPLAIN_OPERATOR"

ALTER TABLE "DB2INST1"."EXPLAIN_OPERATOR" 
	ADD CONSTRAINT "SQL060523144720760" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "DB2INST1"."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "DB2INST1"."EXPLAIN_PREDICATE"

ALTER TABLE "DB2INST1"."EXPLAIN_PREDICATE" 
	ADD CONSTRAINT "SQL060523144722210" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "DB2INST1"."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;

-- DDL Statements for Foreign Keys on Table "DB2INST1"."EXPLAIN_STREAM"

ALTER TABLE "DB2INST1"."EXPLAIN_STREAM" 
	ADD CONSTRAINT "SQL060523144725610" FOREIGN KEY
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	REFERENCES "DB2INST1"."EXPLAIN_STATEMENT"
		("EXPLAIN_REQUESTER",
		 "EXPLAIN_TIME",
		 "SOURCE_NAME",
		 "SOURCE_SCHEMA",
		 "SOURCE_VERSION",
		 "EXPLAIN_LEVEL",
		 "STMTNO",
		 "SECTNO")
	ON DELETE CASCADE
	ON UPDATE NO ACTION
	ENFORCED
	ENABLE QUERY OPTIMIZATION;






----------------------------

-- DDL Statements for Views

----------------------------
SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_001 AS SELECT * FROM TB__ATOM_CACHE_T_001_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_003 AS SELECT * FROM TB__ATOM_CACHE_T_003_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_004 AS SELECT * FROM TB__ATOM_CACHE_T_004_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_005 AS SELECT * FROM TB__ATOM_CACHE_T_005_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_006 AS SELECT * FROM TB__ATOM_CACHE_T_006_20060214;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_007 AS SELECT * FROM TB__ATOM_CACHE_T_007_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_009 AS SELECT * FROM TB__ATOM_CACHE_T_009_20060116;

SET CURRENT SCHEMA = "ETCHAP1 ";
SET CURRENT PATH = "SYSIBM","SYSFUN","SYSPROC","ETCHAP1";
CREATE VIEW T_008 AS SELECT * FROM TB__ATOM_CACHE_T_008_20060116;





-------------------------------------------------
-- DDL Statements for Audits 
-------------------------------------------------

CREATE AUDIT POLICY "DB2AUDIT_CFG_MIGR"
CATEGORIES AUDIT STATUS FAILURE ,
CHECKING STATUS FAILURE ,
OBJMAINT STATUS FAILURE ,
SECMAINT STATUS FAILURE ,
SYSADMIN STATUS FAILURE ,
VALIDATE STATUS FAILURE ERROR TYPE NORMAL ;


--------------------------------------------
-- Authorization Statements on Tables/Views 
--------------------------------------------

 
GRANT SELECT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ACCESSURL" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AGENT" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AGENT" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AGENT" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AGENT" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AGENT" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AGENT" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AGENT" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AGENT" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AGENT" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AGENT" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AGENT" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AGENT" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AGENT" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AGENT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AGENT" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AGENT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."APPLICATION" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."APPLICATION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOM" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOM" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOM" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOM" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOM" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOM" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOM" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOM" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOM" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOM" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOM" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOM" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOM" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOM" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOM" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOM" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ATOMPROFILE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITINGLOG" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."AUDITLOGRECORD" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."BANKDATA" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."BANKDATA" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "SYSIBMADM"."DBCFG" TO USER "JCDINST1" ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORGROUP" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."DOCUMENTFORROLE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_ARGUMENT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_INSTANCE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OBJECT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_OPERATOR" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_PREDICATE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STATEMENT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."EXPLAIN_STREAM" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."LOGFILE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."LOGFILE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."OPERATORS" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."OPERATORS" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ORGANIZATIONUNIT" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."PASSWDMEMENTO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."ROLEGROUP" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_ITEM" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SCH_MAIN" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYKEYVALUEINFO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYPARAMETER" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SECURITYROLE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFOROU" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."STRINGVALUEFORUSER" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."SYSPARAM" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_001" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_001" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_001" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_001" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_001" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_001" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_001" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_001" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_001" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_001" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_001" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_001" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_001" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_001" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_001" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_001" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_003" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_003" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_003" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_003" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_003" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_003" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_003" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_003" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_003" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_003" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_003" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_003" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_003" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_003" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_003" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_003" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_004" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_004" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_004" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_004" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_004" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_004" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_004" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_004" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_004" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_004" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_004" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_004" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_004" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_004" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_004" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_004" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_005" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_005" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_005" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_005" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_005" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_005" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_005" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_005" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_005" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_005" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_005" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_005" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_005" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_005" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_005" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_005" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_006" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_006" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_006" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_006" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_006" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_006" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_006" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_006" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_006" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_006" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_006" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_006" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_006" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_006" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_006" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_006" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_007" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_007" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_007" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_007" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_007" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_007" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_007" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_007" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_007" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_007" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_007" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_007" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_007" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_007" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_007" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_007" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_008" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_008" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_008" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_008" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_008" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_008" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_008" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_008" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_008" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_008" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_008" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_008" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_008" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_008" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_008" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_008" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_009" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_009" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_009" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_009" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_009" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_009" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_009" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_009" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_009" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_009" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_009" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_009" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."T_009" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."T_009" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."T_009" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."T_009" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "EMDAP33 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "JCTINST1" ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_001_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "EMDAP33 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "JCTINST1" ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_003_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "EMDAP33 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "JCTINST1" ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_004_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "EMDAP33 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "JCTINST1" ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_005_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "EMDAP33 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "JCTINST1" ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_006_20060214" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "EMDAP33 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "JCTINST1" ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_007_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "EMDAP33 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "JCTINST1" ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_008_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "EMDAP33 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "JCTINST1" ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB__ATOM_CACHE_T_009_20060116" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ATOM_DEFINITION_INFO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_BOARD" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CACHE_BACKUP" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_CATEGORY_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CODE_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_COMBO_CODITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_CREDIT_REPORT_PROVIDER" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_FEE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_KEY_DEFINITION1" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_INQUIRY_TASK" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_ATOM_MAPPING" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_CACHE_INFO_BACKUP" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_ITEM_TYPE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_MESSAGE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TB_SUB_ITEM_DEFINITION" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TCHDATA" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TCHDATA" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXID" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXID" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXID" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXID" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXID" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXID" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXID" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXID" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXID" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXID" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXID" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXID" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXID" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXID" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXID" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXID" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."TXIDPROFILE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERCARETAKER" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERDEPARTMENTROLE" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERINFO" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERINFO" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERINFO" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERINFO" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERINFO" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERINFO" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERINFO" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERINFO" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERINFO" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERINFO" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERINFO" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERINFO" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERINFO" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERINFO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERINFO" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERINFO" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERS" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERS" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERS" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERS" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERS" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERS" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERS" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERS" TO USER "EJMNAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERS" TO USER "EJMNAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERS" TO USER "EJMNAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERS" TO USER "EJMNAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP  " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP1 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP1 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP1 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP1 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP2 " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP2 " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP2 " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERS" TO USER "ETCHAP2 " ;

GRANT DELETE ON TABLE "ETCHAP1 "."USERS" TO USER "OPTPSS  " ;

GRANT INSERT ON TABLE "ETCHAP1 "."USERS" TO USER "OPTPSS  " ;

GRANT SELECT ON TABLE "ETCHAP1 "."USERS" TO USER "OPTPSS  " ;

GRANT UPDATE ON TABLE "ETCHAP1 "."USERS" TO USER "OPTPSS  " ;

----------------------------------------
-- Authorization Statements on Packages 
----------------------------------------

 
GRANT BIND ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ02 " TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."AOTMJ03 " TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ03 " TO  PUBLIC   ;

GRANT BIND ON PACKAGE "NULLID  "."ATSJ04  " TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ATSJ04  " TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."ATSJ04  " TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ATSJ04  " TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."ATSJ04  " TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ATSJ04  " TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."ATSJ04  " TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ATSJ04  " TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."ATSJ04  " TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ATSJ04  " TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."ATSJ04  " TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ATSJ04  " TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."ATSJ04  " TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ATSJ04  " TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."ATSJ04  " TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ATSJ04  " TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."ATSJ04  " TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ATSJ04  " TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."DB2MOVE " TO USER "DB2INST1" ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DB2MOVE " TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."DB2MVJ01" TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DB2MVJ01" TO  PUBLIC   ;

GRANT BIND ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."DSPRCJ02" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."NNSTAT  " TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."NNSTAT  " TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."NNSTAT  " TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."NNSTAT  " TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."NNSTAT  " TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."NNSTAT  " TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."NNSTAT  " TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."NNSTAT  " TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."NNSTAT  " TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."NNSTAT  " TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."NNSTAT  " TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."NNSTAT  " TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."NNSTAT  " TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."NNSTAT  " TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."NNSTAT  " TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."NNSTAT  " TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."NNSTAT  " TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."NNSTAT  " TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1E00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1E00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1F00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1F00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1J00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1J00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1J00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1J00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1J00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1J00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1J00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1J00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1J00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1J00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1J00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1J00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1J00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1J00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1J00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1J00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA1J00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA1J00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2E00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2E00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2F00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2F00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2J00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2J00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2J00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2J00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2J00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2J00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2J00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2J00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2J00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2J00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2J00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2J00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2J00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2J00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2J00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2J00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA2J00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA2J00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3E00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3E00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3F00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3F00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3J00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3J00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3J00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3J00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3J00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3J00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3J00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3J00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3J00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3J00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3J00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3J00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3J00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3J00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3J00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3J00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA3J00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA3J00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4E00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4E00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4F00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4F00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4J00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4J00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4J00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4J00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4J00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4J00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4J00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4J00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4J00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4J00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4J00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4J00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4J00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4J00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4J00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4J00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA4J00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA4J00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5E00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5E00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5F00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5F00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5J00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5J00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5J00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5J00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5J00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5J00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5J00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5J00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5J00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5J00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5J00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5J00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5J00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5J00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5J00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5J00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLA5J00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLA5J00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABE01" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABE01" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABF01" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABF01" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABJ02" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABJ02" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABJ02" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABJ02" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABJ02" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABJ02" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABJ02" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABJ02" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABJ02" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABJ02" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABJ02" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABJ02" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABJ02" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABJ02" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABJ02" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABJ02" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLABJ02" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLABJ02" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2E03" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2E03" TO USER "ETCHAP1 " ;

GRANT CONTROL ON PACKAGE "NULLID  "."SQLC2E06" TO USER "JCDINST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2E06" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2E06" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2E07" TO USER "DB2INST1" ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2E07" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2E07" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2E07" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2F0A" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2F0A" TO USER "ELAPUSR " ;

GRANT CONTROL ON PACKAGE "NULLID  "."SQLC2J25" TO USER "JCDINST1" ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2J25" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2J25" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2J25" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2J25" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2J25" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2J25" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2J25" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2J25" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC2J25" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC2J25" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3E03" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3E03" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3E05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3E05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3E06" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3E06" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3E06" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3F09" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3F09" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3J24" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3J24" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3J24" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3J24" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3J24" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3J24" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3J24" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3J24" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3J24" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3J24" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3J24" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3J24" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3J24" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3J24" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3J24" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3J24" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC3J24" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC3J24" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4E03" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4E03" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4E05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4E05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4E06" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4E06" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4E06" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4F09" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4F09" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4J24" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4J24" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4J24" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4J24" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4J24" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4J24" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4J24" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4J24" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4J24" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4J24" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4J24" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4J24" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4J24" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4J24" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4J24" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4J24" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC4J24" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC4J24" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5E03" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5E03" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5E05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5E05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5E06" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5E06" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5E06" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5F09" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5F09" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5J24" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5J24" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5J24" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5J24" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5J24" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5J24" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5J24" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5J24" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5J24" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5J24" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5J24" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5J24" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5J24" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5J24" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5J24" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5J24" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC5J24" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC5J24" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6E03" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6E03" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6E05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6E05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6E06" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6E06" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6E06" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6F09" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6F09" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6J24" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6J24" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6J24" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6J24" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6J24" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6J24" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6J24" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6J24" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6J24" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6J24" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6J24" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6J24" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6J24" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6J24" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6J24" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6J24" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLC6J24" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLC6J24" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE2E0G" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3E00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3E00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3F01" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3F01" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3J01" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3J01" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3J01" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3J01" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3J01" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3J01" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3J01" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3J01" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3J01" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3J01" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3J01" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3J01" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3J01" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3J01" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3J01" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3J01" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLE3J01" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLE3J01" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9E0L" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO  PUBLIC   ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLNAE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLNAE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLNBE09" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLNBE09" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLNCE03" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLNCE03" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1E00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1E00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1E01" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1E01" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1F01" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1F01" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1J02" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1J02" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1J02" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1J02" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1J02" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1J02" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1J02" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1J02" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1J02" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1J02" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1J02" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1J02" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1J02" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1J02" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1J02" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1J02" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLP1J02" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLP1J02" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLQG_DS" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLQG_DS" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAE11" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAE11" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAE12" TO USER "DB2INST1" ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAE15" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAE15" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAE15" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAE17" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAF14" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAF14" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUAJ20" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUATLD" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUATLD" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBE01" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBE01" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBE02" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBE02" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBF02" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBF02" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUBJ05" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCE01" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCE01" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCE02" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCE02" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCF03" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCF03" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUCJ05" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDF01" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDF01" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUDJ02" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEF00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEF00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUEJ01" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFE06" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFE06" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFE07" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFE07" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFF0E" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFF0E" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ14" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ15" TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ15" TO  PUBLIC   ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUGE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUGE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUGF00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUGF00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHF01" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHF01" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ04" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ05" TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ05" TO  PUBLIC   ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIF00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIF00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUIJ00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJE02" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJE02" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJE03" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJE03" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJE03" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJF05" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJF05" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUJJ0D" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKE04" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKE04" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKE06" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKE06" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKF07" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKF07" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUKJ0B" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULF00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULF00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULJ00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULJ00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULJ00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULJ00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULJ00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULJ00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULJ00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULJ00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULJ00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULJ00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULJ00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULJ00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULJ00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULJ00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULJ00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULJ00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLULJ00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLULJ00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUME00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUME00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMF00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMF00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUMJ00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNE01" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNE01" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNF01" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNF01" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUNJ01" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOF00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOF00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUOJ01" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPE00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPE00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPF00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPF00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUPJ03" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZF00" TO USER "ELAPUSR " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZF00" TO USER "ELAPUSR " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUZJ00" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."ST_ADMIN" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."ST_ADMIN" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."STADME00" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."STADME00" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."STADME01" TO USER "DB2INST1" ;

GRANT EXECUTE ON PACKAGE "NULLID  "."STADME01" TO USER "DB2INST1" ;

GRANT EXECUTE ON PACKAGE "NULLID  "."STADME02" TO USER "DB2INST1" ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH100" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH100" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH100" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH100" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH100" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH100" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH100" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH100" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH100" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH100" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH100" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH100" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH100" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH100" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH100" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH100" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH100" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH100" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH101" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH101" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH101" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH101" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH101" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH101" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH101" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH101" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH101" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH101" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH101" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH101" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH101" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH101" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH101" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH101" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH101" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH101" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH102" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH102" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH102" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH102" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH102" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH102" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH102" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH102" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH102" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH102" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH102" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH102" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH102" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH102" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH102" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH102" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH102" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH102" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH200" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH200" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH200" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH200" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH200" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH200" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH200" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH200" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH200" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH200" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH200" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH200" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH200" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH200" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH200" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH200" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH200" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH200" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH201" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH201" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH201" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH201" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH201" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH201" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH201" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH201" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH201" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH201" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH201" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH201" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH201" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH201" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH201" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH201" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH201" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH201" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH202" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH202" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH202" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH202" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH202" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH202" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH202" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH202" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH202" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH202" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH202" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH202" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH202" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH202" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH202" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH202" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH202" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH202" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH300" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH300" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH300" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH300" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH300" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH300" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH300" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH300" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH300" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH300" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH300" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH300" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH300" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH300" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH300" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH300" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH300" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH300" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH301" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH301" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH301" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH301" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH301" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH301" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH301" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH301" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH301" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH301" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH301" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH301" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH301" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH301" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH301" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH301" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH301" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH301" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH302" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH302" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH302" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH302" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH302" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH302" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH302" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH302" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH302" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH302" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH302" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH302" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH302" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH302" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH302" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH302" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH302" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH302" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH400" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH400" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH400" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH400" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH400" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH400" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH400" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH400" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH400" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH400" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH400" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH400" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH400" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH400" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH400" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH400" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH400" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH400" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH401" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH401" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH401" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH401" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH401" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH401" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH401" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH401" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH401" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH401" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH401" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH401" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH401" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH401" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH401" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH401" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH401" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH401" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH402" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH402" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH402" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH402" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH402" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH402" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH402" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH402" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH402" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH402" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH402" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH402" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH402" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH402" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH402" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH402" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLH402" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLH402" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN100" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN100" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN100" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN100" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN100" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN100" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN100" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN100" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN100" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN100" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN100" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN100" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN100" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN100" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN100" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN100" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN100" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN100" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN101" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN101" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN101" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN101" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN101" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN101" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN101" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN101" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN101" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN101" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN101" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN101" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN101" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN101" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN101" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN101" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN101" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN101" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN102" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN102" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN102" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN102" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN102" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN102" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN102" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN102" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN102" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN102" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN102" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN102" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN102" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN102" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN102" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN102" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN102" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN102" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN200" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN200" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN200" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN200" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN200" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN200" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN200" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN200" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN200" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN200" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN200" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN200" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN200" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN200" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN200" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN200" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN200" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN200" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN201" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN201" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN201" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN201" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN201" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN201" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN201" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN201" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN201" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN201" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN201" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN201" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN201" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN201" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN201" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN201" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN201" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN201" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN202" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN202" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN202" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN202" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN202" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN202" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN202" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN202" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN202" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN202" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN202" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN202" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN202" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN202" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN202" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN202" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN202" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN202" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN300" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN300" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN300" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN300" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN300" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN300" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN300" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN300" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN300" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN300" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN300" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN300" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN300" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN300" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN300" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN300" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN300" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN300" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN301" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN301" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN301" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN301" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN301" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN301" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN301" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN301" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN301" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN301" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN301" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN301" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN301" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN301" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN301" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN301" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN301" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN301" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN302" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN302" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN302" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN302" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN302" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN302" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN302" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN302" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN302" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN302" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN302" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN302" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN302" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN302" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN302" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN302" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN302" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN302" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN400" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN400" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN400" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN400" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN400" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN400" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN400" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN400" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN400" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN400" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN400" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN400" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN400" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN400" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN400" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN400" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN400" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN400" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN401" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN401" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN401" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN401" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN401" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN401" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN401" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN401" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN401" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN401" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN401" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN401" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN401" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN401" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN401" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN401" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN401" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN401" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN402" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN402" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN402" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN402" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN402" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN402" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN402" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN402" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN402" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN402" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN402" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN402" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN402" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN402" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN402" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN402" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSLN402" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSLN402" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH100" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH100" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH100" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH100" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH100" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH100" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH100" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH100" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH100" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH100" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH100" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH100" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH100" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH100" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH100" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH100" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH100" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH100" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH101" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH101" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH101" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH101" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH101" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH101" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH101" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH101" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH101" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH101" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH101" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH101" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH101" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH101" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH101" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH101" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH101" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH101" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH102" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH102" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH102" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH102" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH102" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH102" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH102" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH102" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH102" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH102" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH102" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH102" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH102" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH102" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH102" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH102" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH102" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH102" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH200" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH200" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH200" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH200" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH200" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH200" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH200" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH200" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH200" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH201" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH201" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH201" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH201" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH201" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH201" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH201" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH201" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH201" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH201" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH201" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH201" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH201" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH201" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH201" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH201" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH201" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH201" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH202" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH202" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH202" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH202" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH202" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH202" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH202" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH202" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH202" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH202" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH202" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH202" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH202" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH202" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH202" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH202" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH202" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH202" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH300" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH300" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH300" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH300" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH300" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH300" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH300" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH300" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH300" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH301" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH301" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH301" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH301" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH301" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH301" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH301" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH301" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH301" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH301" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH301" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH301" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH301" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH301" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH301" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH301" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH301" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH301" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH302" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH302" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH302" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH302" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH302" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH302" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH302" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH302" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH302" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH302" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH302" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH302" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH302" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH302" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH302" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH302" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH302" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH302" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH400" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH400" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH400" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH400" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH400" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH400" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH400" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH400" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH400" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH400" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH400" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH400" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH400" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH400" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH400" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH400" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH400" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH400" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH401" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH401" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH401" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH401" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH401" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH401" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH401" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH401" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH401" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH401" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH401" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH401" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH401" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH401" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH401" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH401" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH401" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH401" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH402" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH402" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH402" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH402" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH402" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH402" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH402" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH402" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH402" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH402" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH402" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH402" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH402" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH402" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH402" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH402" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSH402" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH402" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN100" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN100" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN100" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN100" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN100" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN100" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN100" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN100" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN100" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN100" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN100" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN100" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN100" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN100" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN100" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN100" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN100" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN100" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN101" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN101" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN101" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN101" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN101" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN101" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN101" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN101" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN101" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN101" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN101" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN101" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN101" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN101" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN101" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN101" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN101" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN101" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN102" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN102" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN102" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN102" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN102" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN102" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN102" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN102" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN102" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN102" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN102" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN102" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN102" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN102" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN102" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN102" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN102" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN102" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN200" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN200" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN200" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN200" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN200" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN200" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN200" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN200" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN200" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN200" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN200" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN200" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN200" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN200" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN200" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN200" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN200" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN200" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN201" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN201" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN201" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN201" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN201" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN201" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN201" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN201" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN201" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN201" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN201" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN201" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN201" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN201" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN201" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN201" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN201" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN201" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN202" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN202" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN202" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN202" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN202" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN202" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN202" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN202" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN202" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN202" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN202" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN202" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN202" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN202" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN202" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN202" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN202" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN202" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN300" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN300" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN300" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN300" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN300" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN300" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN300" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN300" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN300" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN300" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN300" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN300" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN300" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN300" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN300" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN300" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN300" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN300" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN301" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN301" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN301" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN301" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN301" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN301" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN301" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN301" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN301" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN301" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN301" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN301" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN301" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN301" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN301" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN301" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN301" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN301" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN302" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN302" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN302" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN302" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN302" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN302" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN302" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN302" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN302" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN302" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN302" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN302" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN302" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN302" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN302" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN302" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN302" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN302" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN400" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN400" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN400" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN400" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN400" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN400" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN400" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN400" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN400" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN400" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN400" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN400" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN400" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN400" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN400" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN400" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN400" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN400" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN401" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN401" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN401" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN401" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN401" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN401" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN401" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN401" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN401" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN401" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN401" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN401" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN401" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN401" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN401" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN401" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN401" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN401" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN402" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN402" TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN402" TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN402" TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN402" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN402" TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN402" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN402" TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN402" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN402" TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN402" TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN402" TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN402" TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN402" TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN402" TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN402" TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSN402" TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSN402" TO USER "OPTPSS  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSTAT " TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSTAT " TO USER "DCDBMOD " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSTAT " TO USER "EJMNAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSTAT " TO USER "EJMNAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSTAT " TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSTAT " TO USER "EMDAP31 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSTAT " TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSTAT " TO USER "EMDAP32 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSTAT " TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSTAT " TO USER "EMDAP33 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSTAT " TO USER "ETCHAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSTAT " TO USER "ETCHAP  " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSTAT " TO USER "ETCHAP1 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSTAT " TO USER "ETCHAP1 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSTAT " TO USER "ETCHAP2 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSTAT " TO USER "ETCHAP2 " ;

GRANT BIND ON PACKAGE "NULLID  "."SYSSTAT " TO USER "OPTPSS  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSTAT " TO USER "OPTPSS  " ;

----------------------------------------
-- Authorization Statements on Database 
----------------------------------------

 
GRANT CONNECT ON DATABASE  TO USER "DCDBMOD " ;

GRANT CONNECT ON DATABASE  TO USER "EJMNAP1 " ;

GRANT CONNECT ON DATABASE  TO USER "EMDAP31 " ;

GRANT CONNECT ON DATABASE  TO USER "EMDAP32 " ;

GRANT CONNECT ON DATABASE  TO USER "EMDAP33 " ;

GRANT CONNECT ON DATABASE  TO USER "ETCHAP  " ;

GRANT CONNECT ON DATABASE  TO USER "ETCHAP1 " ;

GRANT CONNECT ON DATABASE  TO USER "ETCHAP1 " ;

GRANT CONNECT ON DATABASE  TO USER "ETCHAP2 " ;

GRANT CONNECT ON DATABASE  TO USER "OPTPSS  " ;

-----------------------------------------------------
-- Authorization Statements on User Defined Functions 
-----------------------------------------------------

 
GRANT EXECUTE ON FUNCTION "SYSPROC "."BASE_TABLE"(VARCHAR(),VARCHAR()) TO USER "DCDBMOD " ;

--------------------------------------------------------
-- Database and Database Manager configuration parameters
--------------------------------------------------------

UPDATE DBM CFG USING cpuspeed 3.148961e-07;
UPDATE DBM CFG USING intra_parallel NO;
UPDATE DBM CFG USING comm_bandwidth 100.000000;
UPDATE DBM CFG USING federated NO;
UPDATE DBM CFG USING fed_noauth NO;

UPDATE DB CFG FOR ETCH USING locklist 6000;
UPDATE DB CFG FOR ETCH USING dft_degree 1;
UPDATE DB CFG FOR ETCH USING maxlocks 70;
UPDATE DB CFG FOR ETCH USING avg_appls 1;
UPDATE DB CFG FOR ETCH USING stmtheap 4096;
UPDATE DB CFG FOR ETCH USING dft_queryopt 5;
UPDATE DB CFG FOR ETCH USING cur_commit DISABLED;

---------------------------------
-- Environment Variables settings
---------------------------------

