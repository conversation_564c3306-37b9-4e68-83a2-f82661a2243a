package etch.jdbc;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;

public class ETCHColumnMapRowSetMapper {

	private String removePrefix;

	public ETCHColumnMapRowSetMapper setRemovePrefix(String removePrefix) {
		this.removePrefix = removePrefix;
		return this;
	}

	public Map<String, Object> mapRow(SqlRowSet rs, int rowNum) {
		SqlRowSetMetaData rsmd = rs.getMetaData();
		int columnCount = rsmd.getColumnCount();
		Map<String, Object> mapOfColValues = new HashMap<String, Object>(columnCount);
		for (int i = 1; i <= columnCount; i++) {
			String key = rsmd.getColumnName(i);
			if (removePrefix != null) {
				key = key.replaceFirst(removePrefix, "");
			}
			Object obj = getColumnValue(rs, i);
			if (obj instanceof String) {
				obj = StringUtils.trimToEmpty(obj.toString());
			}
			mapOfColValues.put(key, obj);
		}
		return mapOfColValues;
	}

	protected Object getColumnValue(SqlRowSet rs, int index) {
		Object obj = rs.getObject(index);
		if (obj instanceof String) {
			String str = (String) obj;
			str = str.trim();
		}
		return obj;
	}

}
