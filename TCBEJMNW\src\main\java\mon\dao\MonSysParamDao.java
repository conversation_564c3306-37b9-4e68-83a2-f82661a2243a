package mon.dao;

import java.util.List;

import mon.model.MonSysParam;

/**
 * <pre>
 * SysParamDao.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,new
 *          </ul>
 */
public interface MonSysParamDao extends GenericDao<MonSysParam> {
	public List<MonSysParam> findByParamList(List<String> keyList);

	public List<MonSysParam> findByParamKeyword(String keyword);

	public List<MonSysParam> findByParams(String... params);

	public int updateParamValue(String param, String paramValue);
}
