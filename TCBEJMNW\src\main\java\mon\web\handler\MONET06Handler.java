package mon.web.handler;

import java.util.Map;

import javax.annotation.Resource;

import mon.exception.AppException;
import mon.model.ETCHLogFile;
import mon.service.AuthRole;
import mon.web.core.AbstractHandler;
import mon.web.core.WebContext;
import mon.web.core.WebView;
import mon.web.model.MONET06VO;
import mon.web.model.WebUserProfile;
import mon.web.service.impl.MONET00Service;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;

/**
 * <pre>
 * MONMQ02Handler.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Controller
@AuthRole(role = { WebUserProfile.ROLE_MONITOR })
public class MONET06Handler extends AbstractHandler {

	@Resource
	MONET00Service monet00Service;

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.web.core.Handler#action(mon.web.core.WebContext, java.util.Map)
	 */
	@Override
	public WebView action(WebContext context, Map<String, String> paramMap) throws AppException {
		String url = "/mon/MONET06R.jsp";

		String date = StringUtils.trimToEmpty(paramMap.get("qdate"));
		String txId = StringUtils.trimToEmpty(paramMap.get("txId")).toUpperCase();
		String msgId = StringUtils.trimToEmpty(paramMap.get("msgId"));
		String requestId = StringUtils.trimToEmpty(paramMap.get("requestId"));
		String division = StringUtils.trimToEmpty(paramMap.get("division"));
		String querykey1 = StringUtils.trimToEmpty(paramMap.get("querykey1")).toUpperCase();
		String querykey2 = StringUtils.trimToEmpty(paramMap.get("querykey2"));
		String querykey3 = StringUtils.trimToEmpty(paramMap.get("querykey3"));
		String querykey4 = StringUtils.trimToEmpty(paramMap.get("querykey4"));
		String querykey5 = StringUtils.trimToEmpty(paramMap.get("querykey5"));
		String rc = StringUtils.trimToEmpty(paramMap.get("rc"));

		ETCHLogFile input = new ETCHLogFile();
		input.setQdate(date);
		input.setTxid(txId);
		input.setMsgid(msgId);
		input.setRequestid(requestId);
		input.setDivision(division);
		input.setQuerykey1(querykey1);
		input.setQuerykey2(querykey2);
		input.setQuerykey3(querykey3);
		input.setQuerykey4(querykey4);
		input.setQuerykey5(querykey5);
		input.setRc(rc);

		MONET06VO vo = new MONET06VO();
		vo.setInput(input);
		vo = this.monet00Service.doET06(vo);

		return WebView.forward(url).requestAttr("monet06data", vo);
	}

}
