package etch.dao.impl;

import java.util.List;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import etch.dao.TxidDao;
import etch.model.Txid;

/**
 * <pre>
 * TxidDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("txidDao")
public class TxidDaoImpl extends AbstractGenericDao<Txid> implements TxidDao {

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.dao.TxidDao#findByTxid()
	 */
	@Override
	public List<Txid> findByTxid(final String txid) {
		final String SQL = "SELECT * FROM TXID WHERE TXID=? ";
		return this.getJdbc().query(SQL, new Object[] { txid },
				ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));
	}

	@Override
	public int deleteByTxid(String txid) {
		String sql = "DELETE FROM TXID WHERE TXID=?";
		return this.getJdbc().update(sql, new Object[] { txid });
	}
}