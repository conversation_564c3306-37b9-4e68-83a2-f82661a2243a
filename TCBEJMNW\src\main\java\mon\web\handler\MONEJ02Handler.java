package mon.web.handler;

import java.util.Calendar;
import java.util.Map;

import javax.annotation.Resource;

import mon.exception.AppException;
import mon.service.AuthRole;
import mon.web.core.AbstractHandler;
import mon.web.core.WebContext;
import mon.web.core.WebView;
import mon.web.model.MONEJ02VO;
import mon.web.model.WebUserProfile;
import mon.web.service.impl.MONEJ00Service;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Controller;

/**
 * <pre>
 * MONMQ02Handler.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Controller
@AuthRole(role = { WebUserProfile.ROLE_MONITOR })
public class MONEJ02Handler extends AbstractHandler {

	@Resource
	MONEJ00Service monej00Service;

	@Override
	public WebView action(WebContext context, Map<String, String> paramMap) throws AppException {
		String url = "/mon/MONEJ02R.jsp";

		java.text.SimpleDateFormat spf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm");
		java.util.Calendar calendar = java.util.Calendar.getInstance();

		String term = StringUtils.trimToEmpty(paramMap.get("term"));

		String y1 = String.valueOf(NumberUtils.toInt(paramMap.get("Y1"), Integer.MIN_VALUE) + 1911);
		String m1 = StringUtils.trimToEmpty(paramMap.get("M1"));
		String d1 = StringUtils.trimToEmpty(paramMap.get("D1"));
		String h1 = StringUtils.trimToEmpty(paramMap.get("H1"));
		String mn1 = StringUtils.trimToEmpty(paramMap.get("MN1"));
		String y2 = String.valueOf(NumberUtils.toInt(paramMap.get("Y2"), Integer.MIN_VALUE) + 1911);
		String m2 = StringUtils.trimToEmpty(paramMap.get("M2"));
		String d2 = StringUtils.trimToEmpty(paramMap.get("D2"));
		String h2 = StringUtils.trimToEmpty(paramMap.get("H2"));
		String mn2 = StringUtils.trimToEmpty(paramMap.get("MN2"));
		String sysId = StringUtils.trimToEmpty(paramMap.get("sysId"));

		MONEJ02VO vo = new MONEJ02VO();
		vo.setHidY1(y1);
		vo.setHidM1(m1);
		vo.setHidD1(d1);
		vo.setHidH1(h1);
		vo.setHidMN1(mn1);
		vo.setHidY2(y2);
		vo.setHidM2(m2);
		vo.setHidD2(d2);
		vo.setHidH2(h2);
		vo.setHidMN2(mn2);

		String startDate = "";
		String endDate = "";
		if ("30min".equals(term)) {
			endDate = spf.format(calendar.getTime());
			vo.setHidH2(endDate.substring(8, 10));
			vo.setHidMN2(endDate.substring(10, 12));

			calendar.add(Calendar.MINUTE, -30);
			startDate = spf.format(calendar.getTime());
			vo.setHidH1(startDate.substring(8, 10));
			vo.setHidMN1(startDate.substring(10, 12));
		} else if ("60min".equals(term)) {
			endDate = spf.format(calendar.getTime());
			calendar.add(Calendar.MINUTE, -60);
			startDate = spf.format(calendar.getTime());
		} else {
			startDate = y1 + "-" + m1 + "-" + d1 + " " + h1 + ":" + mn1;
			endDate = y2 + "-" + m2 + "-" + d2 + " " + h2 + ":" + mn2;
		}
		startDate = startDate + ":00";
		endDate = endDate + ":59";

		vo.setStartDate(startDate);
		vo.setEndDate(endDate);
		vo.setSysType(sysId);

		this.monej00Service.doEJ02(vo);

		return WebView.forward(url).requestAttr("monej02data", vo);
	}
}
