package ejcic.web.core;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import ejcic.web.model.WebUserProfile;

/**
 * <pre>
 * WebContext.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class WebContext {
	private WebUserProfile userProfile = null;

	private HttpServletRequest webRequest;
	private HttpServletResponse webResponse;

	/**
	 * Returns the userProfile
	 * 
	 * @return the userProfile
	 */
	public WebUserProfile getUserProfile() {
		return userProfile;
	}

	/**
	 * Sets the userProfile
	 * 
	 * @param userProfile
	 *            the userProfile to set
	 */
	public void setUserProfile(WebUserProfile userProfile) {
		this.userProfile = userProfile;
	}

	/**
	 * Returns the webRequest
	 * 
	 * @return the webRequest
	 */
	public HttpServletRequest getWebRequest() {
		return webRequest;
	}

	/**
	 * Sets the webRequest
	 * 
	 * @param webRequest
	 *            the webRequest to set
	 */
	public void setWebRequest(HttpServletRequest webRequest) {
		this.webRequest = webRequest;
	}

	/**
	 * Returns the webResponse
	 * 
	 * @return the webResponse
	 */
	public HttpServletResponse getWebResponse() {
		return webResponse;
	}

	/**
	 * Sets the webResponse
	 * 
	 * @param webResponse
	 *            the webResponse to set
	 */
	public void setWebResponse(HttpServletResponse webResponse) {
		this.webResponse = webResponse;
	}

}
