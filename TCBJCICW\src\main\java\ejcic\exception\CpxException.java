package ejcic.exception;

/**
 * <pre>
 * CpxException.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class CpxException extends AppException {

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	public CpxException() {
		super();
	}

	/**
	 * @param message
	 * @param cause
	 */
	public CpxException(String message, Throwable cause) {
		super(message, cause);
	}

	/**
	 * @param message
	 */
	public CpxException(String message) {
		super(message);
	}

	/**
	 * @param cause
	 */
	public CpxException(Throwable cause) {
		super(cause);
	}

}
