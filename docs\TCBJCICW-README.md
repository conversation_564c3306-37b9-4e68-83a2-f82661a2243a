# TCBJCICW 專案技術文檔

## 專案概述

TCBJCICW 是一個金融交易系統的 Web 前端專案，主要用於處理與聯合徵信中心 (JCIC) 相關的查詢和交易。該系統提供了標準查詢、組合查詢、授權管理、系統參數設定等功能，並透過 MQ 與後端系統進行通訊。

## 系統架構

TCBJCICW 採用 Spring MVC 架構，基於 JDK 1.6 開發，使用 Maven 3.2.5 進行專案管理，Jetty 8.1.8 作為 Web 容器。

### 架構圖

```mermaid
graph TD
    Client[客戶端瀏覽器] --> WebLayer[Web 層]
    WebLayer --> ServiceLayer[服務層]
    ServiceLayer --> DAOLayer[數據訪問層]
    DAOLayer --> DB[(資料庫)]
    ServiceLayer --> MQ[消息佇列]
    MQ --> DISP[Dispatcher]
    DISP --> GW[Gateway]
    GW --> JCIC[聯合徵信中心]
    
    subgraph WebLayer
        JSP[JSP 頁面]
        Handler[Handler 控制器]
        Filter[過濾器]
    end
    
    subgraph ServiceLayer
        TxidService[交易服務]
        MQService[MQ 服務]
        AuthService[授權服務]
    end
    
    subgraph DAOLayer
        GenericDao[通用 DAO]
        ModelMeta[模型元數據]
    end
```

## 核心功能模組

### 1. 標準查詢模組

提供標準的聯徵中心查詢功能，包括個人信用報告、企業信用報告等。

```mermaid
classDiagram
    class StdTxidProfile {
        +String txid
        +String txidName
        +String txidDesc
        +String txidType
        +String authType
        +String needAuth
        +String needCharge
        +String needLog
        +String needPrint
        +String needSupervisor
        +String needCustId
    }
    
    class StdTxidCheckService {
        +boolean checkTxid(String txid, String custId)
        +boolean checkAuth(String txid, String custId, String userId)
        +boolean checkSupervisor(String txid, String custId, String userId)
    }
    
    StdTxidCheckService --> StdTxidProfile
```

### 2. 組合查詢模組

提供多種組合查詢功能，可以同時查詢多個標準查詢項目。

```mermaid
classDiagram
    class CPXTxidCheckService {
        +boolean checkCpxTxid(String txid, String custId)
        +Result processCpx(String txid, Map<String, String> params)
    }
    
    class CpxProductLoader {
        +void loadProducts()
        +Proc getProc(String txid)
        +WarnHandler getWarnHandler(String txid)
    }
    
    class Result {
        +String txid
        +String custId
        +String rc
        +String msg
        +Map<String, Object> data
    }
    
    CPXTxidCheckService --> CpxProductLoader
    CPXTxidCheckService --> Result
```

### 3. 授權管理模組

管理用戶對各種交易的授權。

```mermaid
classDiagram
    class SupervisorAuth {
        +String userId
        +String custId
        +String txid
        +String authType
        +Date startDate
        +Date endDate
        +String status
    }
    
    class AuthRole {
        +boolean hasRole(String userId, String role)
        +boolean hasAuth(String userId, String txid, String custId)
        +boolean isSupervisor(String userId, String txid, String custId)
    }
    
    AuthRole --> SupervisorAuth
```

### 4. 系統參數管理模組

管理系統的各種參數設定。

```mermaid
classDiagram
    class SysParam {
        +String paramId
        +String paramValue
        +String paramDesc
        +String paramType
    }
    
    class SysParamService {
        +String getParam(String paramId)
        +void setParam(String paramId, String paramValue)
        +List<SysParam> getAllParams()
    }
    
    SysParamService --> SysParam
```

## 主要業務流程

### 1. 標準查詢流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant Handler as Handler
    participant Service as TxidService
    participant MQ as MQService
    participant JCIC as 聯合徵信中心
    
    User->>Handler: 提交查詢請求
    Handler->>Service: 檢查授權
    Service->>Handler: 授權通過
    Handler->>MQ: 發送查詢請求
    MQ->>JCIC: 轉發請求
    JCIC-->>MQ: 返回查詢結果
    MQ-->>Handler: 返回結果
    Handler-->>User: 顯示查詢結果
```

### 2. 組合查詢流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant Handler as Handler
    participant CPXService as CPXService
    participant Proc as 處理器
    participant MQ as MQService
    participant JCIC as 聯合徵信中心
    
    User->>Handler: 提交組合查詢請求
    Handler->>CPXService: 處理組合查詢
    CPXService->>Proc: 獲取處理器
    Proc->>MQ: 發送多個查詢請求
    MQ->>JCIC: 轉發請求
    JCIC-->>MQ: 返回查詢結果
    MQ-->>Proc: 返回結果
    Proc->>CPXService: 處理結果
    CPXService-->>Handler: 返回處理結果
    Handler-->>User: 顯示查詢結果
```

### 3. 授權管理流程

```mermaid
sequenceDiagram
    participant Admin as 管理員
    participant Handler as Handler
    participant AuthService as AuthService
    participant DAO as DAO
    participant DB as 資料庫
    
    Admin->>Handler: 提交授權設定
    Handler->>AuthService: 處理授權請求
    AuthService->>DAO: 儲存授權資料
    DAO->>DB: 寫入資料庫
    DB-->>DAO: 確認寫入
    DAO-->>AuthService: 返回處理結果
    AuthService-->>Handler: 返回處理結果
    Handler-->>Admin: 顯示處理結果
```

## 系統組件

### 1. Dispatcher (分發器)

負責接收 Web 層的請求，並將其分發到相應的處理模組。

```mermaid
classDiagram
    class AbstractDispatcher {
        +void init()
        +void start()
        +void stop()
        +void dispatch(Message msg)
    }
    
    class JcicDispatcher {
        +void processMessage(Message msg)
        +void sendToJcic(Message msg)
        +void receiveFromJcic(Message msg)
    }
    
    class MonDispatcher {
        +void processMessage(Message msg)
        +void sendMonitorData(Message msg)
    }
    
    AbstractDispatcher <|-- JcicDispatcher
    AbstractDispatcher <|-- MonDispatcher
```

### 2. Gateway (閘道)

負責與聯合徵信中心進行通訊。

```mermaid
classDiagram
    class GWManager {
        +void init()
        +void start()
        +void stop()
        +void sendToJcic(Message msg)
        +void receiveFromJcic(Message msg)
    }
    
    class SendProcess {
        +void run()
        +void sendMessage(Message msg)
    }
    
    class RecvProcess {
        +void run()
        +Message receiveMessage()
    }
    
    GWManager --> SendProcess
    GWManager --> RecvProcess
```

### 3. MQ 服務

負責與 MQ 進行通訊。

```mermaid
classDiagram
    class MQService {
        +void sendMessage(String queueName, Message msg)
        +Message receiveMessage(String queueName)
    }
    
    class IbmMQAdapter {
        +void connect()
        +void disconnect()
        +void sendMessage(String queueName, Message msg)
        +Message receiveMessage(String queueName)
    }
    
    MQService --> IbmMQAdapter
```

## 技術特點

1. **ORM 映射**: 使用自定義的註解和反射機制實現簡單的 ORM 映射
2. **MQ 通訊**: 使用 IBM MQ 與後端系統和聯合徵信中心進行通訊
3. **Spring MVC**: 使用 Spring MVC 框架處理 Web 請求
4. **Handler 模式**: 使用 Handler 模式處理不同的業務邏輯
5. **安全機制**: 實現了多層次的授權和認證機制

## 部署說明

TCBJCICW 專案需要部署在支援 JDK 1.6 的環境中，並配置相應的資料庫連接和 MQ 連接。詳細的部署步驟請參考專案的部署文檔。

## 常見問題

1. **連接超時**: 檢查資料庫和 MQ 連接設定是否正確
2. **授權失敗**: 檢查用戶權限和授權設定
3. **查詢失敗**: 檢查聯合徵信中心連接是否正常

## 結論

TCBJCICW 專案是一個功能完整的金融交易系統前端，提供了豐富的查詢功能和管理功能，能夠有效地處理與聯合徵信中心的交易。通過本文檔，您可以了解 TCBJCICW 專案的架構、功能和業務流程，以便更好地使用和維護該系統。
