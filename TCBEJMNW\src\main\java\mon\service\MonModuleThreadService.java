package mon.service;

import java.util.List;

import mon.model.MonModuleThread;

/**
 * <pre>
 * MonModuleThreadService.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public interface MonModuleThreadService {
	public abstract List<MonModuleThread> doEJCICWebThread();

	public abstract List<MonModuleThread> doEJCICDispThread();

	public abstract List<MonModuleThread> doEJCICGWThread();

	public abstract List<MonModuleThread> doEloanDispThread();

	public abstract List<MonModuleThread> doETCHWebThread();

	public abstract List<MonModuleThread> doETCHDispThread();

	public abstract List<MonModuleThread> doETCHGWThread();
}
