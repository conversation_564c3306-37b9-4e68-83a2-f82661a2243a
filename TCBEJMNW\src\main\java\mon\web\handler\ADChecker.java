package mon.web.handler;

import javax.annotation.Resource;

import mon.service.ConfigService;
import mon.web.model.WebUserProfile;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ADChecker {
	protected static Logger logger = LoggerFactory.getLogger(ADChecker.class);

	private final WebUserProfile user = new WebUserProfile();

	@Resource
	ConfigService configService;

	public WebUserProfile getUserProfile() {
		return this.user;
	}

	public boolean check(final String branchId, final String userId) {
		WebUserProfile user = new WebUserProfile();
		user.setBranchId(branchId);
		user.setUserId(userId);

		return true;
	}

}
