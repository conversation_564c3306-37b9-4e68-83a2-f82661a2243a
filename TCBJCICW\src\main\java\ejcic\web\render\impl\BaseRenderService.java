package ejcic.web.render.impl;

import java.sql.Timestamp;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.json.simple.JSONArray;

import ejcic.service.CustIdAuthService;
import ejcic.service.ReturnCodeService;
import ejcic.service.impl.SpringContextHelper;
import ejcic.web.model.WebUserProfile;

/**
 * <pre>
 * BaseRenderService.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@SuppressWarnings({ "unchecked" })
public class BaseRenderService {

	protected String renderOption(String key) {
		return this.renderOption(key, key, false, false);
	}

	protected String renderOption(String key, String val) {
		return this.renderOption(key, val, false, false);
	}

	protected String renderOption(String key, String val, boolean showKey) {
		return this.renderOption(key, val, showKey, false);
	}

	protected String renderOption(String key, String val, boolean showKey, boolean selected) {
		return String.format("<option value='%1$s' %2$s >%3$s%4$s</option>", key, (selected ? " selected " : ""), (showKey ? key + "-" : ""), val);
	}

	/**
	 * @param codes
	 * @return
	 */
	public String outErrrMsgJS(String... codes) {
		ReturnCodeService rcService = SpringContextHelper.getService(ReturnCodeService.class);
		StringBuffer outstr = new StringBuffer("var errMsg={};");
		for (String code : codes) {
			outstr.append("errMsg['").append(code).append("']='").append(rcService.getCodeDesc(code)).append("';");
		}
		return outstr.toString();
	}

	public String getCustIdAuthList(WebUserProfile userInfo) {
		String branchcd = userInfo.getBranchId();
		CustIdAuthService custIdAuthService = SpringContextHelper.getService(CustIdAuthService.class);
		List<Map<String, Object>> list = custIdAuthService.getCustIdAuthList(branchcd);
		JSONArray jsons = new JSONArray();
		long currentTime = System.currentTimeMillis();
		for (Iterator<?> iterator = list.iterator(); iterator.hasNext();) {
			Map<String, Object> map = (Map<String, Object>) iterator.next();
			String status = MapUtils.getString(map, "STATUS");
			String custid = MapUtils.getString(map, "CUSTIDNO");
			Timestamp expiredTime = (Timestamp) MapUtils.getObject(map, "EXPIREDTIME");
			if ("AC".equals(status) == false || StringUtils.isEmpty(custid) || expiredTime == null || currentTime > expiredTime.getTime()) {
				continue;
			}
			String base64 = new String(Base64.encodeBase64(custid.getBytes()));
			if (jsons.contains(base64) == false) {
				jsons.add(base64);
			}
		}
		return jsons.toJSONString();
	}
}
