package etch.dao;

import java.util.List;

import etch.model.Atom;

/**
 * <pre>
 * AtomlistDao.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,new
 *          </ul>
 */
public interface AtomDao extends GenericDao<Atom> {
	public List<Atom> findByAtomId(final String atomId);

	public List<Atom> getMoreAtomListByAtomId(String atomId);

	public int delAtomList(String atomId);
}
