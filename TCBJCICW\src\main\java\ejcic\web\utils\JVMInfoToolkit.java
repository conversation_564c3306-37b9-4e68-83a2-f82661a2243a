package ejcic.web.utils;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.SortedMap;

/**
 * <pre>
 * JVMInfoToolkit.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class JVMInfoToolkit {
	public static String genJVMCharset() {
		StringBuilder sb = new StringBuilder();
		SortedMap<String, Charset> map = Charset.availableCharsets();
		for (Map.Entry<String, Charset> entry : map.entrySet()) {
			sb.append(entry.getKey()).append(":").append(entry.getValue().aliases()).append("\n");
		}
		return sb.toString();
	}

	public static String genJVMProperties() {
		StringBuilder sb = new StringBuilder();
		Properties props = System.getProperties();
		List<String> keylist = new ArrayList<String>();
		for (Object o : props.keySet()) {
			keylist.add(o.toString());
		}
		Collections.sort(keylist, String.CASE_INSENSITIVE_ORDER);
		for (String s : keylist) {
			sb.append(s).append("=").append(props.get(s)).append("\n");
		}
		return sb.toString();
	}
}
