package mon.service.impl;

import javax.annotation.Resource;

import mon.model.MonSysUsage;
import mon.service.MQService;
import mon.service.MonSysUsageService;
import mon.service.SysParamService;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <pre>
 * MonitorServiceImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Service("monSysUsageService")
public class MonSysUsageServiceImpl implements MonSysUsageService {
	@Resource
	@Qualifier("EJCICMQService")
	MQService ejcicMQService;

	@Resource
	@Qualifier("ETCHMQService")
	MQService etchMQService;

	@Resource
	SysParamService sysParamService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonSysUsageService#getEJCICWebSysUsage()
	 */
	@Override
	public MonSysUsage doEJCICWebSysUsage() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonSysUsageService#getEJCICDispSysUsage()
	 */
	@Override
	public MonSysUsage doEJCICDispSysUsage() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonSysUsageService#getEJCICGWSysUsage()
	 */
	@Override
	public MonSysUsage doEJCICGWSysUsage() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonSysUsageService#getEloanDispSysUsage()
	 */
	@Override
	public MonSysUsage doEloanDispSysUsage() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonSysUsageService#getETCHWebSysUsage()
	 */
	@Override
	public MonSysUsage doETCHWebSysUsage() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonSysUsageService#getETCHDispSysUsage()
	 */
	@Override
	public MonSysUsage doETCHDispSysUsage() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonSysUsageService#getETCHGWSysUsage()
	 */
	@Override
	public MonSysUsage doETCHGWSysUsage() {

		return null;
	}

}
