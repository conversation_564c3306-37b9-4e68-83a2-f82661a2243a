package etch.model;

import java.sql.Timestamp;
import java.util.List;

/**
 * <pre>
 * SchMain.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@TModel(tbName = "SCH_MAIN")
public class SchMain extends AbstractModelMeta {
	public static final String STATUS_RUNNING = "0001";
	public static final String STATUS_INTERRUPT = "9999";
	public static final String STATUS_SUCCESS = "0000";
	public static final String STATUS_INIT = "";
	public static final String DEL_YES = "Y";
	public static final String DEL_NO = "N";

	@TKeyField
	private long sch_Sno;

	@TField
	private Timestamp sch_Time;

	@TField
	private String sch_Status;

	@TField
	private String item_Cnt;

	@TField
	private Timestamp crDate;

	@TField
	private String isDel;

	@TField
	private Timestamp delTime;

	@TField
	private String teamNo;

	@TField
	private String requestId;

	private List<SchItem> itemList;

	/**
	 * Returns the sch_Sno
	 * 
	 * @return the sch_Sno
	 */
	public long getSch_Sno() {
		return this.sch_Sno;
	}

	/**
	 * Sets the sch_Sno
	 * 
	 * @param sch_Sno
	 *            the sch_Sno to set
	 */
	public void setSch_Sno(long sch_Sno) {
		this.sch_Sno = sch_Sno;
	}

	/**
	 * Returns the sch_Status
	 * 
	 * @return the sch_Status
	 */
	public String getSch_Status() {
		return this.sch_Status;
	}

	/**
	 * Sets the sch_Status
	 * 
	 * @param sch_Status
	 *            the sch_Status to set
	 */
	public void setSch_Status(String sch_Status) {
		this.sch_Status = sch_Status;
	}

	/**
	 * Returns the item_Cnt
	 * 
	 * @return the item_Cnt
	 */
	public String getItem_Cnt() {
		return this.item_Cnt;
	}

	/**
	 * Sets the item_Cnt
	 * 
	 * @param item_Cnt
	 *            the item_Cnt to set
	 */
	public void setItem_Cnt(String item_Cnt) {
		this.item_Cnt = item_Cnt;
	}

	public String getIsDel() {
		return this.isDel;
	}

	public void setIsDel(String isDel) {
		this.isDel = isDel;
	}

	/**
	 * Returns the delTime
	 * 
	 * @return the delTime
	 */
	public Timestamp getDelTime() {
		return this.delTime;
	}

	/**
	 * Sets the delTime
	 * 
	 * @param delTime
	 *            the delTime to set
	 */
	public void setDelTime(Timestamp delTime) {
		this.delTime = delTime;
	}

	public String getTeamNo() {
		return this.teamNo;
	}

	public void setTeamNo(String teamNo) {
		this.teamNo = teamNo;
	}

	public List<SchItem> getItemList() {
		return this.itemList;
	}

	public void setItemList(List<SchItem> itemList) {
		this.itemList = itemList;
	}

	public String getRequestId() {
		return this.requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	/**
	 * Returns the sch_Time
	 * 
	 * @return the sch_Time
	 */
	public Timestamp getSch_Time() {
		return this.sch_Time;
	}

	/**
	 * Sets the sch_Time
	 * 
	 * @param sch_Time
	 *            the sch_Time to set
	 */
	public void setSch_Time(Timestamp sch_Time) {
		this.sch_Time = sch_Time;
	}

	/**
	 * Returns the crDate
	 * 
	 * @return the crDate
	 */
	public Timestamp getCrDate() {
		return this.crDate;
	}

	/**
	 * Sets the crDate
	 * 
	 * @param crDate
	 *            the crDate to set
	 */
	public void setCrDate(Timestamp crDate) {
		this.crDate = crDate;
	}

}
