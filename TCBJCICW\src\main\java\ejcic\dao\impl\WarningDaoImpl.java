package ejcic.dao.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import ejcic.dao.WarningDao;
import ejcic.model.Warning;

/**
 * <pre>
 * WorningDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("warningDao")
public class WarningDaoImpl extends AbstractGenericDao<Warning> implements WarningDao {

	@Override
	public int deleteAndInsert(Warning warn, boolean doDelFlag) {
		if (doDelFlag) {
			this.delete(warn.getIdn(), warn.getQid());
		}
		return this.insert(warn);
	}

	@Override
	public void deleteAll(List<Warning> warnList) {
		// Map<String, Boolean> deletedMap = new HashMap<String, Boolean>();
		// if (warnList != null) {
		// for (int i = 0, size = warnList.size(); i < size; i++) {
		// Warning warn = warnList.get(i);
		//
		// String delkey = warn.getIdn() + warn.getQid();
		// Boolean deletedFlag = deletedMap.get(delkey);
		// if (deletedFlag == null) {
		// deletedFlag = new Boolean(false);
		// }
		//
		// if (!deletedFlag.booleanValue()) {
		// this.delete(warn);
		// deletedFlag = true;
		// deletedMap.put(delkey, deletedFlag);
		// }
		// }
		// }
		if (warnList != null) {
			for (int i = 0, size = warnList.size(); i < size; i++) {
				Warning warn = warnList.get(i);
				if (warn.getDesc() != null) {
					this.delete(warn);
				}
			}
		}
	}

	@Override
	public void InsertAll(List<Warning> warnList) {
		if (warnList != null) {
			for (int i = 0, size = warnList.size(); i < size; i++) {
				Warning warn = warnList.get(i);
				if (warn.getDesc() != null) {
					this.insert(warn);
				}
			}
		}
	}

	@Override
	public void deleteAndInsertAll(List<Warning> warnList) {
		Map<String, Boolean> deletedMap = new HashMap<String, Boolean>();
		if (warnList != null) {
			for (int i = 0, size = warnList.size(); i < size; i++) {
				Warning warn = warnList.get(i);

				String delkey = warn.getIdn() + warn.getQid();
				Boolean deletedFlag = deletedMap.get(delkey);
				if (deletedFlag == null) {
					deletedFlag = new Boolean(false);
				}

				if (!deletedFlag.booleanValue()) {
					this.delete(warn);
					deletedFlag = true;
					deletedMap.put(delkey, deletedFlag);
				}
				if (warn.getDesc() != null) {
					this.insert(warn);
				}
			}
		}
	}

	@Override
	public List<String> findByIdAndTxid(Warning model) {
		String sql = "SELECT DESC FROM WORNING WHERE IDN=? and QID=? order by QID,DESC ";
		List<Map<String, Object>> rs = this.getJdbc()
				.queryForList(sql, new String[] { model.getIdn(), model.getQid() });
		List<String> result = new ArrayList<String>();
		if (rs != null && rs.size() > 0) {
			for (Map<String, Object> row : rs) {
				result.add((String) row.get("DESC"));
			}
		}
		return result;
	}

}