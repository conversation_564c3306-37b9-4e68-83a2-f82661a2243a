<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
    xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    	   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config />

    <!-- 設定檔BEGIN -->
    <bean class="org.jasypt.spring.properties.EncryptablePropertyPlaceholderConfigurer">
        <constructor-arg ref="encryptor" />
        <property name="locations">
            <list>
                <value>classpath:ejcic.dbcfg.j006.properties</value>
                <value>classpath:ejcic.properties</value>
            </list>
        </property>
    </bean>

    <bean id="encryptor" class="org.jasypt.encryption.pbe.StandardPBEStringEncryptor">
        <property name="config" ref="environmentConfig" />
    </bean>

	<bean id="environmentConfig" class="ejcic.fortify.fix.TcbEnvironmentStringPBEConfig">
		<property name="algName" value="default" />
		<!--<property name="passwordEnvName" value="CAS_PBE_PWD" /> -->
		<property name="passw0rd" value="TCB_EJCIC_ETCH" />
	</bean>
    <!-- 設定檔END -->

    <bean id="log4jInitialization" class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetClass" value="org.springframework.util.Log4jConfigurer" />
        <property name="targetMethod" value="initLogging" />
        <property name="arguments">
            <list>
                <value>classpath:spring/log4j-disp4web.xml</value>
            </list>
        </property>
    </bean>

    <bean id="springContext" class="ejcic.service.impl.SpringContextHelper" />
    <util:properties id="ejcicProperties" location="classpath:ejcic.properties" />
    <util:properties id="errorProperties" location="classpath:ejcic.error.desc.properties" />

    <import resource="classpath:spring/datasource-ap.xml" />
    <import resource="classpath:spring/jdbc.xml" />
    <import resource="classpath:spring/services-base.xml" />
</beans>