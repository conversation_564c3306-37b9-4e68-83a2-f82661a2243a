package ejcic.dao.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import ejcic.dao.AtomListProfileDao;
import ejcic.model.AtomListProfile;

/**
 * <pre>
 * AtomlistprofileDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("atomListProfileDao")
public class AtomListProfileDaoImpl extends AbstractGenericDao<AtomListProfile> implements AtomListProfileDao {
	@Override
	public List<Map<String, Object>> findAllAtomId() {
		final String SQL = "select AtomID from AtomListProfile";
		return this.getJdbc().queryForList(SQL, new Object[] {});
	}

	public List<AtomListProfile> findByTxid(String txid) {
		final String SQL = "SELECT  O.TXID,O.ATOMID,A.CNAME,A.WEIGHT  FROM OPTLIST O  LEFT JOIN ATOMLISTPROFILE A ON O.ATOMID=A.ATOMID WHERE O.TXID=? ORDER BY  O.TXID,O.ATOMID";
		Object[] args = new Object[] { txid };
		SqlRowSet srs = this.getJdbc().queryForRowSet(SQL, args);
		List<AtomListProfile> itemList = new ArrayList<AtomListProfile>();
		while (srs.next()) {
			AtomListProfile model = new AtomListProfile();
			model.setAtomid(srs.getString("ATOMID"));
			model.setCname(srs.getString("CNAME"));
			model.setWeight(srs.getInt("WEIGHT"));
			itemList.add(model);
		}

		return itemList;
	}

}