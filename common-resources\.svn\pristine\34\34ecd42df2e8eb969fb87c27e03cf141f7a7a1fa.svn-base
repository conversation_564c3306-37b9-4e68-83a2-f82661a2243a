CREATE TABLE "EJCICAP1"."CUSTIDAUTH"  (
		  "UUID" VARCHAR(64) NOT NULL , 
		  "BUSINESSDATE" VARCHAR(8) NOT NULL , 
		  "BRANCHCD" VARCHAR(8) NOT NULL , 
		  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" VARCHAR(20) NOT NULL , 
		  "CUST<PERSON><PERSON>" VARCHAR(256) NOT NULL , 
		  "USERID" VARCHAR(20) NOT NULL , 
		  "USERNAME" VARCHAR(256) NOT NULL , 
		  "CREATETIME" TIMESTAMP NOT NULL , 
		  "STATUS" VARCHAR(10) NOT NULL , 
		  "SUPID" VARCHAR(20) , 
		  "SUPNAME" VARCHAR(256) , 
		  "VERIFYTIME" TIMESTAMP , 
		  "EXPIREDTIME" TIMESTAMP , 
		  "ISPRINT" VARCHAR(10) , 
		  "LASTMODIFIEDTIME" TIMESTAMP NOT NULL )   
		 IN "J006TABSP4K" ; 


-- DDL Statements for Primary Key on Table "EJCICAP1"."CUSTIDAUTH"

ALTER TABLE "EJCICAP1"."CUSTIDAUTH" 
	ADD PRIMARY KEY
		("UUID");



-- DDL Statements for Indexes on Table "EJCICAP1"."CUSTIDAUTH"

CREATE INDEX "EJCICAP1"."IDX_CUSTIDAUTH_01" ON "EJCICAP1"."CUSTIDAUTH" 
		("BRANCHCD" ASC,
		 "CUSTIDNO" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;

------------------------------------------------
-- DDL Statements for Table "EJCICAP1"."SUPERVISORAUTH"
------------------------------------------------
 

CREATE TABLE "EJCICAP1"."SUPERVISORAUTH"  (
		  "UUID" VARCHAR(32) NOT NULL , 
		  "BUSINESSDATE" VARCHAR(8) NOT NULL , 
		  "BRANCHCD" VARCHAR(6) NOT NULL , 
		  "ETABSUSERID" VARCHAR(2) NOT NULL , 
		  "ETABSUSERNAME" VARCHAR(256) NOT NULL , 
		  "CUSTIDNO" VARCHAR(10) NOT NULL , 
		  "ETABSSUPID" VARCHAR(4) NOT NULL , 
		  "ETABSSUPNAME" VARCHAR(256) NOT NULL , 
		  "TXNID" VARCHAR(6) NOT NULL , 
		  "TXNDATA" VARCHAR(1024) NOT NULL , 
		  "ISPRINT" VARCHAR(2) , 
		  "CREATETIME" TIMESTAMP NOT NULL , 
		  "LASTMODIFIEDTIME" TIMESTAMP NOT NULL )   
		 IN "J006TABSP4K" ; 


-- DDL Statements for Primary Key on Table "EJCICAP1"."SUPERVISORAUTH"

ALTER TABLE "EJCICAP1"."SUPERVISORAUTH" 
	ADD PRIMARY KEY
		("UUID");



-- DDL Statements for Indexes on Table "EJCICAP1"."SUPERVISORAUTH"

CREATE INDEX "EJCICAP1"."IDX_SUPERVISORAUTH_01" ON "EJCICAP1"."SUPERVISORAUTH" 
		("BUSINESSDATE" ASC,
		 "BRANCHCD" ASC,
		 "CUSTIDNO" ASC)
		
		COMPRESS NO ALLOW REVERSE SCANS;