package ejcic.msg;

import ejcic.service.ConfigService;
import ejcic.service.Env;
import ejcic.service.impl.ConfigServiceImpl;
import ejcic.utils.FormatUtils;

/**
 * <pre>
 * L001Message.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class L001Message {
	private static Message msg = new GWMessage("L001");

	public String toString() {
		ConfigService configService = ConfigServiceImpl.getInstance();
		return msg.compose() + FormatUtils.rightPad(configService.getProperty(Env.GW_USERID), 8, " ")
				+ FormatUtils.rightPad(configService.getProperty(Env.GW_PWD), 8, " ");

	}

}
