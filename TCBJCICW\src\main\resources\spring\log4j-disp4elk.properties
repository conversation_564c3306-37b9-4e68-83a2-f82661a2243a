# Log4J configuring file for application logging
#
# Define the default log level, and the default appenders
# LEVEL : OFF\u3001FATAL\u3001ERROR\u3001WARN\u3001INFO\u3001DEBUG\u3001ALL
#log4j.rootLogger=INFO,Stdout

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.conversionPattern=%d [%t] | %X{reqURI} | %-20.20c{1} [%-5p] %m%n


log4j.appender.EJCIC=org.apache.log4j.DailyRollingFileAppender
log4j.appender.EJCIC.layout=org.apache.log4j.PatternLayout
log4j.appender.EJCIC.encoding=ms950
log4j.appender.EJCIC.File=/aphome/ejcic/logs/DISP_ELK.log
log4j.appender.EJCIC.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.EJCIC.layout.ConversionPattern=%d [%t] | %X{reqURI} | %-20.20c{1} [%-5p] %m%n

log4j.appender.JDBC=org.apache.log4j.DailyRollingFileAppender
log4j.appender.JDBC.layout=org.apache.log4j.PatternLayout
log4j.appender.JDBC.encoding=ms950
log4j.appender.JDBC.File=/aphome/ejcic/logs/ELK_SQL.log
log4j.appender.JDBC.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.JDBC.layout.ConversionPattern=%d [%t] | %X{reqURI} | %-20.20c{1} [%-5p] %m%n

log4j.appender.SocketMonitor=tw.com.iisi.mnm.commons.socketappender.MnmSocketAppender
log4j.appender.SocketMonitor.RemoteHost=${REMOTEHOST}
log4j.appender.SocketMonitor.Port=4560
log4j.appender.SocketMonitor.LocationInfo=true
log4j.appender.SocketMonitor.Application=$(COMPUTERNAME)-EJCIC_DISP_ELK
log4j.appender.SocketMonitor.Threshold=DEBUG
log4j.appender.SocketMonitor.layout=org.apache.log4j.PatternLayout
log4j.appender.SocketMonitor.layout.ConversionPattern=%d [%t]|%-5p|%X{SESSION_ID}|%X{CLIENT_IP}|%X{BRANCH_ID}|%X{USER_ID}|%-20.20c{1}|%m%n

# ----- APPLICATION
log4j.rootLogger=DEBUG,EJCIC,SocketMonitor

log4j.logger.org=ERROR
log4j.logger.net=ERROR

#log4j.logger.ejcic.dao=DEBUG
#log4j.logger.ejcic.cpx=DEBUG
#log4j.logger.ejcic.disp=DEBUG

#log4j.logger.ejcic.jdbc=DEBUG,EJCIC,JDBC
#log4j.additivity.ejcic.jdbc = false

# ----- OTHER OPEN SOURCE PACKAGES
# avoid misleading log "No service named XXX is available"
# More on this topic: http://wiki.apache.org/ws/FrontPage/Axis/DealingWithCommonExceptions

log4j.logger.org.springframework.security=WARN
log4j.logger.org.apache.commons=WARN
log4j.logger.org.apache.velocity=WARN
log4j.logger.org.springframework=WARN

log4j.logger.org.springframework.beans.factory=WARN
log4j.logger.org.springframework.beans.factory.support=WARN
log4j.logger.org.springframework.transaction=WARN

log4j.logger.com.mchange.v2=WARN

# ----- SUBSTITUTE SYMBOL
# %c Logger, %c{2 } last 2 partial names
# %C Class name (full agony), %C{2 } last 2 partial names
# %d{dd MMM yyyy HH:MM:ss } Date, format see java.text.SimpleDateFormat, If no date format specifier is given then ISO8601 format is assumed.
# %F File name
# %l Location (caution: compiler-option-dependently)
# %L Line number
# %m user-defined message
# %M Method name
# %p Level
# %r Milliseconds since program start
# %t Threadname
# %x, %X see Doku
# %% individual percentage sign
# Caution: %C, %F, %l, %L, %M slow down program run!

