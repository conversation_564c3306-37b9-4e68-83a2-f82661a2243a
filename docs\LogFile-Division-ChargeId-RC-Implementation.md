# LogFile.division 和 LogFile.chargeid 特定分行處理功能實現

## 功能需求

當 `LogFile.division` 或 `LogFile.chargeid` 為指定分行（例如 9999）時：
1. 底層程式不發送查詢到 JCIC
2. 將 `LogFile.rc` 設為 8888
3. 相應地更新 `AUDITLOG` 表中的記錄

## 系統流程分析

根據現有代碼分析，系統處理流程為：
```
LogFile -> 發查不發查判斷 -> AUDITLOG -> 回寫 LogFile
```

因此，我們需要在「發查不發查判斷」這個環節中加入對 `division` 和 `chargeid` 的檢查邏輯。

## 關鍵代碼位置

### 1. 發查不發查判斷邏輯

主要位於 `JCICST00Service.java` 的 `getW2DStdMessage` 方法中，該方法負責處理是否強制向聯徵查詢的邏輯：

```java
// 判斷查詢項目是否強制向聯徵查詢
String forceFlag = logfile.getForceflag();
String[] forceToJcic = StringUtils.split(StringUtils.trimToEmpty(this.sysParamService.getProperty(SysParamConst.STDTXID_FORCE_TOJCIC)), ",");
if (ArrayUtils.contains(forceToJcic, logfile.getTxid())) {
    forceFlag = "Y";
    logger.warn("查詢項目[{}]在強制向聯徵查詢的清單內，將F_BFORCEFLAG : {} --> {}", new String[] { logfile.getTxid(), logfile.getForceflag(), forceFlag });
}
msg.setItem(W2DStdMessage.F_BFORCEFLAG, forceFlag);
```

### 2. LogFile 相關欄位

根據數據庫表結構和代碼分析，`LogFile` 類中包含以下關鍵欄位：
- `division`: 分行代碼，CHAR(4)
- `chargeid`: 收費單位代碼，CHAR(9)
- `rc`: 回傳碼，CHAR(4)
- `forceflag`: 查詢方式，CHAR(1)，可能值為 "A"（自動）、"Y"（強制查詢）、"N"（資料庫查詢）
- `tojcic`: 是否有送聯徵中心查詢，CHAR(1)，可能值為 "Y"（是）、"N"（否）

## 修改建議

### 1. 系統參數配置

首先，需要在系統參數中添加一個新的參數，用於配置需要特殊處理的分行代碼：

```properties
# 不發送查詢的分行代碼，多個代碼以逗號分隔
SPECIAL_BRANCH_CODES=9999
```

### 2. 修改 JCICST00Service.java

在 `getW2DStdMessage` 方法中，添加對 `division` 和 `chargeid` 的檢查邏輯：

```java
// 判斷查詢項目是否強制向聯徵查詢
String forceFlag = logfile.getForceflag();
String[] forceToJcic = StringUtils.split(StringUtils.trimToEmpty(this.sysParamService.getProperty(SysParamConst.STDTXID_FORCE_TOJCIC)), ",");
if (ArrayUtils.contains(forceToJcic, logfile.getTxid())) {
    forceFlag = "Y";
    logger.warn("查詢項目[{}]在強制向聯徵查詢的清單內，將F_BFORCEFLAG : {} --> {}", new String[] { logfile.getTxid(), logfile.getForceflag(), forceFlag });
}

// 檢查 division 和 chargeid 是否為特殊分行代碼
String[] specialBranchCodes = StringUtils.split(StringUtils.trimToEmpty(this.sysParamService.getProperty("SPECIAL_BRANCH_CODES")), ",");
if (specialBranchCodes != null && specialBranchCodes.length > 0) {
    String division = StringUtils.trimToEmpty(logfile.getDivision());
    String chargeid = StringUtils.trimToEmpty(logfile.getChargeid());
    
    if ((StringUtils.isNotEmpty(division) && ArrayUtils.contains(specialBranchCodes, division)) || 
        (StringUtils.isNotEmpty(chargeid) && ArrayUtils.contains(specialBranchCodes, chargeid))) {
        // 設置 forceFlag 為 "N"，強制不發送查詢
        forceFlag = "N";
        // 設置 LogFile.rc 為 8888
        logfile.setRc("8888");
        logger.warn("分行代碼[{}]或收費單位[{}]在特殊處理清單內，將F_BFORCEFLAG : {} --> {}, RC : --> 8888", 
                   new String[] { division, chargeid, logfile.getForceflag(), forceFlag });
    }
}

msg.setItem(W2DStdMessage.F_BFORCEFLAG, forceFlag);
```

### 3. 修改 AuditLog 處理邏輯

在處理 `AuditLog` 的相關方法中，需要確保當 `LogFile.rc` 被設為 8888 時，`AUDITLOG.TOJCIC` 被設為 "N"：

```java
// 在保存 AuditLog 的方法中添加以下邏輯
if ("8888".equals(logfile.getRc())) {
    auditLog.setTojcic(LogFile.TOJCIC_NO);
}
```

## 測試案例

### 案例 1：正常分行代碼

- 輸入：`LogFile.division = "0001"`, `LogFile.chargeid = "0001"`, `LogFile.forceflag = "A"`
- 預期結果：系統根據 `forceflag` 和其他條件決定是否發送查詢

### 案例 2：特殊分行代碼（division）

- 輸入：`LogFile.division = "9999"`, `LogFile.chargeid = "0001"`, `LogFile.forceflag = "A"`
- 預期結果：
  - 系統不發送查詢
  - `LogFile.rc` 被設為 "8888"
  - `AUDITLOG.TOJCIC` 被設為 "N"

### 案例 3：特殊分行代碼（chargeid）

- 輸入：`LogFile.division = "0001"`, `LogFile.chargeid = "9999"`, `LogFile.forceflag = "A"`
- 預期結果：
  - 系統不發送查詢
  - `LogFile.rc` 被設為 "8888"
  - `AUDITLOG.TOJCIC` 被設為 "N"

### 案例 4：強制查詢但是特殊分行代碼

- 輸入：`LogFile.division = "9999"`, `LogFile.chargeid = "0001"`, `LogFile.forceflag = "Y"`
- 預期結果：
  - 系統不發送查詢（特殊分行代碼的處理優先於強制查詢）
  - `LogFile.rc` 被設為 "8888"
  - `AUDITLOG.TOJCIC` 被設為 "N"

## 流程圖

```mermaid
flowchart TD
    A[開始] --> B{檢查 txid 是否在強制查詢清單中}
    B -->|是| C[設置 forceFlag = 'Y']
    B -->|否| D[保持原有 forceFlag]
    C --> E{檢查 division 或 chargeid 是否為特殊分行代碼}
    D --> E
    E -->|是| F[設置 forceFlag = 'N', rc = '8888']
    E -->|否| G[保持原有設置]
    F --> H[設置 msg.F_BFORCEFLAG = forceFlag]
    G --> H
    H --> I[繼續處理]
    I --> J{執行查詢}
    J -->|forceFlag = 'Y'| K[向聯徵中心發送查詢]
    J -->|forceFlag = 'N'| L[從資料庫取得資料]
    K --> M[設置 AUDITLOG.TOJCIC = 'Y']
    L --> N[設置 AUDITLOG.TOJCIC = 'N']
    M --> O[回寫 LogFile]
    N --> O
    O --> P[結束]
```

## 注意事項

1. 需要確保系統參數 `SPECIAL_BRANCH_CODES` 已正確配置
2. 特殊分行代碼的處理邏輯應優先於強制查詢的邏輯
3. 當 `LogFile.rc` 被設為 8888 時，需要確保 `AUDITLOG.TOJCIC` 被設為 "N"
4. 建議在日誌中記錄特殊處理的情況，以便後續追蹤和調試
