<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd">

	<bean id="Core" class="com.alfalumia.eternity.core.Core" factory-method="getInstance" />

	<bean id="Package" factory-bean="Core" factory-method="createPackage">
		<!-- Constructor arguments -->
		<!-- className -->
		<constructor-arg value="com.alfalumia.eternity.core.Package" />
		<!-- packageName -->
		<constructor-arg value="tw.com.iisi.tcb.etabs.server.ws" />
		<!-- Properties -->
		<!-- <property name="domain"><null/></property> -->
		<property name="domain" value="EJCIC-WS" />
		<property name="modules">
			<map>
				<entry key="WsConnector-MQ" value-ref="WsConnector-MQ" />
			</map>
		</property>
	</bean>

	<import resource="ws/wsconnector_mq.xml"/>

</beans>