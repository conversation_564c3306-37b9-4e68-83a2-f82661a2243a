/*************************************
*		printPage
**************************************/
@media screen {
	div.divHeader {
		display: none;
	}
	div.divFooter {
		display: none;
	}
	.watermark {
		font-size: 500%;
		letter-spacing: 5pt;
		color: #D0D0D0;
		z-index: -1;
		position: absolute;
		left: 160px;
	}
	.no-show {
		display: none;
	}
	.btn_default {
		color: #fff;
		font-weight: normal;
		padding: 4px 6px;
		margin: 2px;
		background: #097a38;
		border: 1px solid #EAECEE;
	}

	.showGreenMsg {
		font-size: 15px;
		color: green;
		line-height: 150%;
	}
	.showBlueMsg {
		font-size: 15px;
		color: #00f;
		line-height: 150%;
	}
	.showRedMsg {
		font-size: 15px;
		color: #f00;
		line-height: 150%;
	}
	.titleSmall {
		font-size: 13px;
	}
	.contentF12 {
		font-size: 12px;
	}
}

@media print {
	.btn_default {
		display: none;
	}
	.no-print {
		display: none;
	}
	.watermark {
		display: none;
	}
	div.divHeader {
		position: fixed;
		top: 200px;
		font-size: 500%;
		letter-spacing: 5pt;
		color: #D0D0D0;
		z-index: -1;
		left: 160px;
	}
	div.divFooter {
		position: fixed;
		bottom: 200px;
		font-size: 500%;
		letter-spacing: 5pt;
		color: #D0D0D0;
		z-index: -1;
		left: 160px;
	}
	.showGreenMsg {
		font-size: 15px;
		color: green;
		line-height: 150%;
	}
	.showBlueMsg {
		font-size: 15px;
		color: #00f;
		line-height: 150%;
	}
	.showRedMsg {
		font-size: 15px;
		color: #f00;
		line-height: 150%;
	}
	.titleSmall {
		font-size: 13px;
	}
	.contentF12 {
		font-size: 12px;
	}

	.printTable table{
		text-align: left;
	    width: 650px;
	}
	.printTable td, .printTable th {
		font-size: 11px;
	    white-space: nowrap;
	}
	.printTable input{
	    display:none;
	    visibility:hidden;
	}
}