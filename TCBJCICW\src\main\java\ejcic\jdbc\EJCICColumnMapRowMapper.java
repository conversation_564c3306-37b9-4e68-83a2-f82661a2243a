package ejcic.jdbc;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.ColumnMapRowMapper;

public class EJCICColumnMapRowMapper extends ColumnMapRowMapper {

	private String removePrefix;
	private boolean isTrim = true;

	public EJCICColumnMapRowMapper setRemovePrefix(String removePrefix) {
		this.removePrefix = removePrefix;
		return this;
	}

	@Override
	public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
		ResultSetMetaData rsmd = rs.getMetaData();
		int columnCount = rsmd.getColumnCount();
		Map<String, Object> mapOfColValues = createColumnMap(columnCount);
		for (int i = 1; i <= columnCount; i++) {
			String key = getColumnKey(rsmd.getColumnName(i));
			if (removePrefix != null) {
				key = key.replaceFirst(removePrefix, "");
			}
			Object obj = getColumnValue(rs, i);
			if (isTrim && obj instanceof String) {
				obj = StringUtils.trimToEmpty(obj.toString());
			}
			mapOfColValues.put(key, obj);
		}
		return mapOfColValues;
	}

	@Override
	protected Object getColumnValue(ResultSet rs, int index) throws SQLException {
		Object obj = super.getColumnValue(rs, index);
		if (obj instanceof String) {
			String str = (String) obj;
			if (isTrim) {
				str = str.trim();
			}
		}
		return obj;
	}

	public void setTrim(boolean isTrim) {
		this.isTrim = isTrim;
	}

}
