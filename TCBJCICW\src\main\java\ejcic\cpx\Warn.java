package ejcic.cpx;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * <pre>
 * Warn.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class Warn extends RuleBase {
	private String txid;
	private String msgT;
	private String msgF;
	private String className;

	private List<Proc> procs = new ArrayList<Proc>();

	/**
	 * @return
	 */
	public String getMsgF() {
		return msgF;
	}

	/**
	 * @return
	 */
	public String getMsgT() {
		return msgT;
	}

	/**
	 * @param string
	 */
	public void setMsgF(String string) {
		msgF = string;
	}

	/**
	 * @param string
	 */
	public void setMsgT(String string) {
		msgT = string;
	}

	/**
	 * @return
	 */
	public List<Proc> getProcs() {
		return procs;
	}

	/**
	 * @return
	 */
	public String getTxid() {
		return txid;
	}

	/**
	 * @param list
	 */
	public void setProcs(List<Proc> list) {
		procs = list;
	}

	/**
	 * @param string
	 */
	public void setTxid(String string) {
		txid = string;
	}

	/**
	 * Returns the className
	 * 
	 * @return the className
	 */
	public String getClassName() {
		return className;
	}

	/**
	 * Sets the className
	 * 
	 * @param className
	 *            the className to set
	 */
	public void setClassName(String className) {
		this.className = className;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		ToStringBuilder out = new ToStringBuilder(this);
		out.append("TXID", this.txid);
		out.append("MSGT", this.msgT);
		out.append("MSGF", this.msgF);
		out.append("CLASSNAME", this.className);
		out.getStringBuffer().append("\n");

		for (Rule rule : this.rules) {
			out.getStringBuffer().append("\t");
			out.append(rule.toString());
			out.getStringBuffer().append("\n");
		}

		for (Proc proc : procs) {
			out.getStringBuffer().append("\t");
			out.append(proc.toString());
			out.getStringBuffer().append("\n");
		}

		return out.toString();
	}
}
