package mon.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import mon.dao.ELJobLogDao;
import mon.jdbc.JdbcSqlUtils;
import mon.model.ELJobLog;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * <pre>
 * LogfileDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("ELJobLogDaoImpl")
public class ELJobLogDaoImpl implements ELJobLogDao {
	protected final Logger logger = LoggerFactory.getLogger(ELJobLogDaoImpl.class);

	@Resource
	@Qualifier("eloanJcbcTemplate")
	private JdbcTemplate jdbc;

	@Override
	public List<ELJobLog> query(ELJobLog model) {
		List<Object> strList = new ArrayList<Object>();
		StringBuffer whereStr = new StringBuffer();

		if (model.getDate() != null) {
			whereStr.append(" AND DATE >= ? ");
			strList.add(model.getDate());
		}

		if (model.getDateEnd() != null) {
			whereStr.append(" AND DATE <= ? ");
			strList.add(model.getDateEnd());
		}

		if (StringUtils.isNotBlank(model.getJmName())) {
			whereStr.append(" AND jmName = ? ");
			strList.add(model.getJmName());
		}

		if (model.getSeq() > 0) {
			whereStr.append(" AND seq = ? ");
			strList.add(model.getSeq());
		}

		if (StringUtils.isNotBlank(model.getTbName())) {
			whereStr.append(" AND tbName = ? ");
			strList.add(model.getTbName());
		}

		if (StringUtils.isNotBlank(model.getType())) {
			whereStr.append(" AND type = ? ");
			strList.add(model.getType());
		}

		if (StringUtils.isNotBlank(model.getRc())) {
			whereStr.append(" AND rc = ? ");
			strList.add(model.getRc());
		}

		String sql = " select * from JOBLOG where 1=1 " + StringEscapeUtils.escapeSql(whereStr.toString())
				+ " order by date,jmName,seq,tbName asc FETCH FIRST 1000 ROWS ONLY with ur ";

		JdbcSqlUtils.printSql(sql, strList.toArray());

		long t1 = System.currentTimeMillis();
		try {
			return this.jdbc.query(sql, strList.toArray(),
					ParameterizedBeanPropertyRowMapper.newInstance(ELJobLog.class));
		} finally {
			logger.debug("JdbcTemplate spend {} ms", System.currentTimeMillis() - t1);
		}
	}
}