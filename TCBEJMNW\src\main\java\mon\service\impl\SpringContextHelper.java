package mon.service.impl;

import java.beans.Introspector;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.ClassUtils;

/**
 * <pre> SpringContextHelper.java </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul> <li>2003/06/30,PGU,NEW </ul>
 */
public class SpringContextHelper implements ApplicationContextAware {

	private static ApplicationContext context;

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		SpringContextHelper.context = applicationContext;
	}

	public static Object getBean(String beanName) {
		return context.getBean(beanName);
	}

	// @SuppressWarnings("unchecked")
	// public static <T> T getService(String serviceName) throws BeansException
	// {
	// return (T) context.getBean(serviceName);
	// }

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> T getService(Class clazz) throws BeansException {
		String serviceName = Introspector.decapitalize(ClassUtils.getShortName(clazz.getName()));
		return (T) context.getBean(serviceName);
	}

}
