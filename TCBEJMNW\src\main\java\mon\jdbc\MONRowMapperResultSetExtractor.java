package mon.jdbc;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.Assert;

public class MONRowMapperResultSetExtractor<T> implements ResultSetExtractor<List<T>> {

	private final RowMapper<T> rowMapper;

	private final int startRow;

	private final int fetchSize;

	/**
	 * EloanRowMapperResultSetExtractor
	 * 
	 * @param rowMapper
	 *            the RowMapper which creates an object for each row
	 * @param startRow
	 *            the start row
	 * @param fetchSize
	 *            the fetech size
	 */
	public MONRowMapperResultSetExtractor(RowMapper<T> rowMapper, int startRow, int fetchSize) {
		Assert.notNull(rowMapper, "RowMapper is required");
		this.rowMapper = rowMapper;
		this.startRow = startRow;
		this.fetchSize = fetchSize;
	}

	/**
	 * extractData
	 * 
	 * @param rs
	 *            ResultSet
	 */
	@Override
	public List<T> extractData(ResultSet rs) throws SQLException {
		List<T> results = new ArrayList<T>();
		if (startRow > 1) {
			for (int i = 0; i < startRow - 1; i++) {
				if (!rs.next()) {
					return results;
				}
			}
		}
		for (int rows = 0; rows < fetchSize && rs.next(); rows++) {
			results.add(this.rowMapper.mapRow(rs, rows));
		}
		return results;
	}

}
