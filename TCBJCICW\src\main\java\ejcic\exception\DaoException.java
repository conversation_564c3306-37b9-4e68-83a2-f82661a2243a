package ejcic.exception;

/**
 * <pre>
 * DaoException.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,new
 *          </ul>
 */
@SuppressWarnings("rawtypes")
public class DaoException extends RuntimeException {

	private Class modelClass;
	private String action;

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	public DaoException() {
		super();
	}

	/**
	 * @param message
	 * @param cause
	 */
	public DaoException(String message, Throwable cause) {
		super(message, cause);
	}

	/**
	 * @param message
	 */
	public DaoException(String message) {
		super(message);
	}

	/**
	 * @param cause
	 */
	public DaoException(Throwable cause) {
		super(cause);
	}

	/**
	 * Returns the modelClass
	 * 
	 * @return the modelClass
	 */
	public Class getModelClass() {
		return this.modelClass;
	}

	/**
	 * Sets the modelClass
	 * 
	 * @param modelClass
	 *            the modelClass to set
	 */
	public DaoException setModelClass(Class modelClass) {
		this.modelClass = modelClass;
		return this;
	}

	/**
	 * Returns the action
	 * 
	 * @return the action
	 */
	public String getAction() {
		return this.action;
	}

	/**
	 * Sets the action
	 * 
	 * @param action
	 *            the action to set
	 */
	public DaoException setAction(String action) {
		this.action = action;
		return this;
	}

}
