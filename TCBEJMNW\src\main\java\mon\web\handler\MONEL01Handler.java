package mon.web.handler;

import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Map;

import javax.annotation.Resource;

import mon.exception.AppException;
import mon.model.ELJobLog;
import mon.service.AuthRole;
import mon.web.core.AbstractHandler;
import mon.web.core.WebContext;
import mon.web.core.WebView;
import mon.web.model.MONEL01VO;
import mon.web.model.WebUserProfile;
import mon.web.service.impl.MONEL00Service;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Controller;

/**
 * <pre>
 * MONEL01Handler.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Controller
@AuthRole(role = { WebUserProfile.ROLE_MONITOR })
public class MONEL01Handler extends AbstractHandler {

	@Resource
	MONEL00Service monel00Service;

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.web.core.Handler#action(mon.web.core.WebContext, java.util.Map)
	 */
	@Override
	public WebView action(WebContext context, Map<String, String> paramMap) throws AppException {
		String url = "/mon/MONEL01R.jsp";

		String dateStart = StringUtils.trimToEmpty(paramMap.get("dateStart"));
		String dateEnd = StringUtils.trimToEmpty(paramMap.get("dateEnd"));
		String jmName = StringUtils.trimToEmpty(paramMap.get("jmName")).toUpperCase();
		String seq = StringUtils.trimToEmpty(paramMap.get("seq"));
		String tbName = StringUtils.trimToEmpty(paramMap.get("tbName"));

		String rc = StringUtils.trimToEmpty(paramMap.get("rc"));
		String type = StringUtils.trimToEmpty(paramMap.get("type"));

		ELJobLog input = new ELJobLog();

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

		try {
			input.setDate(new Date(formatter.parse(dateStart).getTime()));
			input.setDateEnd(new Date(formatter.parse(dateEnd).getTime()));
			input.setJmName(jmName);
			if (StringUtils.isNotEmpty(seq)) {
				input.setSeq(NumberUtils.toInt(seq));
			}
			input.setTbName(tbName);

			input.setType(type);
			input.setRc(rc);

			MONEL01VO vo = new MONEL01VO();
			vo.setInput(input);

			vo = this.monel00Service.doEL01(vo);
			return WebView.forward(url).requestAttr("monel01data", vo);

		} catch (ParseException e) {
			throw new AppException(e);
		}

	}

}
