package ejcic.dao.impl;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.apache.commons.io.IOUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.support.AbstractLobCreatingPreparedStatementCallback;
import org.springframework.jdbc.support.lob.DefaultLobHandler;
import org.springframework.jdbc.support.lob.LobCreator;
import org.springframework.jdbc.support.lob.LobHandler;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import ejcic.dao.OptQueryLogDao;
import ejcic.exception.DaoException;
import ejcic.model.OptQueryLog;

/**
 * <pre>
 * OptquerylogDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("optQueryLogDao")
public class OptQueryLogDaoImpl extends AbstractGenericDao<OptQueryLog> implements OptQueryLogDao {

	@Override
	public int deleteAndInsert(OptQueryLog optQueryLog) {
		final String SQL = " DELETE FROM OPTQUERYLOG WHERE TXID=? AND QUERYKEY=? ";
		this.getJdbc().update(SQL, new Object[] { optQueryLog.getTxid(), optQueryLog.getQuerykey() });
		return this.insert(optQueryLog);
	}

	@Override
	public String findDateByTxidQKey1(String txid, String querykey) {
		final String SQL = " SELECT DATE FROM OPTQUERYLOG WHERE TXID = ? AND QUERYKEY=? ORDER BY DATE DESC FETCH FIRST 1 rows only ";

		Object[] args = new Object[] { txid, querykey };
		SqlRowSet srs = this.getJdbc().queryForRowSet(SQL, args);
		return srs.next() ? srs.getString("DATE") : null;
	}

	@Override
	public String findHtmlByTxidQKey1(final String txid, final String querykey) {
		final String SQL = " SELECT DATA FROM OPTQUERYLOG WHERE TXID = ? AND QUERYKEY=? ORDER BY DATE DESC ";
		Object[] args = new Object[] { txid, querykey };

		try {
			List<String> result = this.getJdbc().query(SQL, args, new RowMapper<String>() {
				@Override
				public String mapRow(ResultSet rs, int arg1) throws SQLException {
					String data = null;
					LobHandler lobHandler = new DefaultLobHandler();
					InputStream is = null;
					try {
						is = lobHandler.getBlobAsBinaryStream(rs, "DATA");
						data = DaoHelper.stream2str(is);
					} finally {
						IOUtils.closeQuietly(is);
					}
					return data;
				}
			});
			return (result.size() > 0) ? result.get(0) : null;
		} catch (Exception ex) {
			throw new DaoException(ex);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see ejcic.dao.impl.AbstractGenericDao#insert(java.lang.Object)
	 */
	@Override
	public int insert(OptQueryLog model) {
		final String date = model.getDate();
		final String txid = model.getTxid();
		final String querykey = model.getQuerykey();
		final String data = model.getData();

		LobHandler lobHandler = new DefaultLobHandler();

		return this.getJdbc().execute(model.getInsertSql(),
				new AbstractLobCreatingPreparedStatementCallback(lobHandler) {
					@Override
					protected void setValues(PreparedStatement ps, LobCreator lobCreator) throws SQLException {
						ByteArrayInputStream bais = DaoHelper.str2stream(data);
						ps.setString(1, date);
						ps.setString(2, txid);
						ps.setString(3, querykey);
						lobCreator.setBlobAsBinaryStream(ps, 4, bais, bais.available());
					}
				});
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see ejcic.dao.impl.AbstractGenericDao#findByKey(java.lang.Object)
	 */
	@Override
	public OptQueryLog findByKey(OptQueryLog std) {
		try {
			List<OptQueryLog> result = this.getJdbc().query(std.getFindByKeySql(), std.toKeyArgs(),
					new RowMapper<OptQueryLog>() {
						@Override
						public OptQueryLog mapRow(ResultSet rs, int arg1) throws SQLException {
							OptQueryLog model = new OptQueryLog();
							model.setDate(rs.getString("DATE"));
							model.setTxid(rs.getString("TXID"));
							model.setQuerykey(rs.getString("QUERYKEY"));
							LobHandler lobHandler = new DefaultLobHandler();
							InputStream is = null;
							try {
								is = lobHandler.getBlobAsBinaryStream(rs, "DATA");
								model.setData(DaoHelper.stream2str(is));
							} finally {
								IOUtils.closeQuietly(is);
							}
							return model;
						}
					});
			return (result.size() > 0) ? result.get(0) : null;
		} catch (Exception ex) {
			throw new DaoException(ex);
		}
	}
}