# TCB-EJCIC 系統整體規格文件

## 專案概述

TCB-EJCIC 是一個金融交易和監控系統，由三個主要子專案組成：

- **TCBJCICW**: 聯合徵信中心交易系統
- **TCBETCHW**: 票據交換所交易系統  
- **TCBEJMNW**: 系統監控平台

## 技術架構

### 技術棧
- **JDK**: 1.6
- **Web框架**: Spring MVC
- **建置工具**: Maven 3.2.5
- **Web容器**: Jetty 8.1.8
- **資料庫**: IBM DB2
- **訊息佇列**: IBM MQ
- **前端技術**: JSP, JavaScript, CSS

### 整體系統架構

```mermaid
graph TB
    subgraph "前端層"
        UI[Web UI - JSP]
    end
    
    subgraph "應用層"
        JCICW[TCBJCICW<br/>聯徵交易系統]
        ETCHW[TCBETCHW<br/>票交系統]
        EJMNW[TCBEJMNW<br/>監控系統]
    end
    
    subgraph "服務層"
        JDISP[JCIC Dispatcher]
        TDISP[TCH Dispatcher]
        JGW[JCIC Gateway]
        TGW[TCH Gateway]
    end
    
    subgraph "資料層"
        DB[(IBM DB2<br/>資料庫)]
        MQ[IBM MQ<br/>訊息佇列]
    end
    
    subgraph "外部系統"
        JCIC[聯合徵信中心]
        TCH[票據交換所]
    end
    
    UI --> JCICW
    UI --> ETCHW
    UI --> EJMNW
    
    JCICW --> JDISP
    ETCHW --> TDISP
    EJMNW --> MQ
    
    JDISP --> JGW
    TDISP --> TGW
    
    JGW --> JCIC
    TGW --> TCH
    
    JCICW --> DB
    ETCHW --> DB
    EJMNW --> DB
    
    JDISP --> MQ
    TDISP --> MQ
    JGW --> MQ
    TGW --> MQ
    
    EJMNW -.-> JCICW
    EJMNW -.-> ETCHW
```

## 系統流程圖

### 整體業務流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant Web as Web應用
    participant Disp as Dispatcher
    participant GW as Gateway
    participant Ext as 外部系統
    participant Mon as 監控系統
    
    User->>Web: 提交交易請求
    Web->>Disp: 轉發請求
    Disp->>GW: 處理請求
    GW->>Ext: 發送至外部系統
    Ext-->>GW: 回傳結果
    GW-->>Disp: 處理回應
    Disp-->>Web: 回傳結果
    Web-->>User: 顯示結果
    
    Note over Mon: 全程監控
    Mon->>Web: 監控狀態
    Mon->>Disp: 監控狀態
    Mon->>GW: 監控狀態
```

### 資料流程圖

```mermaid
flowchart TD
    A[使用者輸入] --> B{授權檢查}
    B -->|通過| C[建立交易]
    B -->|失敗| D[拒絕交易]
    C --> E[發送MQ訊息]
    E --> F[Dispatcher處理]
    F --> G[Gateway轉發]
    G --> H[外部系統處理]
    H --> I[回傳結果]
    I --> J[更新資料庫]
    J --> K[回應使用者]
    
    L[監控系統] --> M[收集系統狀態]
    M --> N[分析效能指標]
    N --> O[產生監控報告]
```

## 物件關聯圖

```mermaid
erDiagram
    USER ||--o{ TRANSACTION : creates
    USER ||--o{ AUTHORIZATION : has
    TRANSACTION ||--|| TXID_PROFILE : uses
    TRANSACTION ||--o{ LOG_FILE : generates
    TXID_PROFILE ||--o{ CPX_PRODUCT : contains
    SYSTEM ||--o{ MODULE_STATUS : has
    SYSTEM ||--o{ SYS_USAGE : monitors
    QUEUE ||--o{ Q_DEPTH : measures
    
    USER {
        string userId PK
        string userName
        string department
        string role
    }
    
    TRANSACTION {
        string txId PK
        string custId
        string userId FK
        datetime createTime
        string status
        string result
    }
    
    TXID_PROFILE {
        string txId PK
        string txIdName
        string txIdType
        string authType
        boolean needAuth
        boolean needCharge
    }
    
    AUTHORIZATION {
        string userId FK
        string txId FK
        string custId
        datetime startDate
        datetime endDate
        string status
    }
    
    LOG_FILE {
        string logId PK
        string txId FK
        datetime logTime
        string logType
        string content
    }
    
    SYSTEM {
        string sysId PK
        string sysName
        string hostId
        string status
    }
    
    MODULE_STATUS {
        string sysId FK
        string moduleId PK
        string moduleName
        string status
        datetime updateTime
    }
    
    SYS_USAGE {
        string sysId FK
        datetime runTime PK
        int cpuUsage
        int memoryUsage
        string vmstat
    }
    
    QUEUE {
        string queueId PK
        string queueName
        string queueDesc
        string sysId FK
    }
    
    Q_DEPTH {
        string queueId FK
        datetime measureTime PK
        int depth
        string status
    }
```

## 核心模組設計

### 1. Web層架構

```mermaid
classDiagram
    class WebController {
        +String HANDLER_PARAM
        +doGet(HttpServletRequest, HttpServletResponse)
        +doPost(HttpServletRequest, HttpServletResponse)
        +processRequest(HttpServletRequest, HttpServletResponse)
    }
    
    class AbstractHandler {
        <<abstract>>
        +WebView action(WebContext, Map)
        +boolean checkAuth(WebContext)
        +void logTransaction(WebContext)
    }
    
    class WebContext {
        +HttpServletRequest request
        +HttpServletResponse response
        +String userId
        +String custId
        +Map parameters
    }
    
    class WebView {
        +String viewName
        +Map model
        +boolean isRedirect
    }
    
    WebController --> AbstractHandler
    AbstractHandler --> WebContext
    AbstractHandler --> WebView
```

### 2. 服務層架構

```mermaid
classDiagram
    class TxidService {
        <<interface>>
        +boolean checkTxid(String, String)
        +boolean checkAuth(String, String, String)
        +Result processTransaction(String, Map)
    }
    
    class MQService {
        <<interface>>
        +void sendMessage(String, Message)
        +Message receiveMessage(String)
        +boolean isConnected()
    }
    
    class AuthService {
        <<interface>>
        +boolean hasAuth(String, String, String)
        +void grantAuth(String, String, String)
        +void revokeAuth(String, String, String)
    }
    
    class TxidServiceImpl {
        +boolean checkTxid(String, String)
        +boolean checkAuth(String, String, String)
        +Result processTransaction(String, Map)
    }
    
    TxidService <|-- TxidServiceImpl
    TxidServiceImpl --> MQService
    TxidServiceImpl --> AuthService
```

### 3. 資料存取層架構

```mermaid
classDiagram
    class GenericDao~T~ {
        <<interface>>
        +T findById(Object)
        +List~T~ findAll()
        +List~T~ findByExample(T)
        +void save(T)
        +void update(T)
        +void delete(T)
    }
    
    class AbstractModelMeta {
        <<abstract>>
        +String getTableName()
        +String[] getKeyFields()
        +Map getFieldMap()
    }
    
    class JdbcGenericDao~T~ {
        +T findById(Object)
        +List~T~ findAll()
        +List~T~ findByExample(T)
        +void save(T)
        +void update(T)
        +void delete(T)
    }
    
    GenericDao <|-- JdbcGenericDao
    JdbcGenericDao --> AbstractModelMeta
```

## 系統配置

### 資料庫配置
- **連線池**: JNDI DataSource
- **最大連線數**: 50
- **連線逾時**: 10秒
- **Schema**: EJCICAP1, ETCHAP1, EJMNAP1

### MQ配置
- **Queue Manager**: MQJ006D, MQT006D, MQM006D
- **連線方式**: TCP/IP
- **埠號**: 1414
- **通道**: SYSTEM.DEF.SVRCONN

### Web容器配置
- **容器**: Jetty 8.1.8
- **JVM參數**: -Xms512m -Xmx1024m
- **編碼**: UTF-8
- **Session逾時**: 30分鐘

## 安全機制

### 授權控制
1. **使用者認證**: 透過Notes系統驗證
2. **交易授權**: 基於TXID和客戶ID的細粒度授權
3. **主管授權**: 特定交易需要主管二次授權
4. **時間控制**: 授權具有有效期限

### 資料安全
1. **資料加密**: 敏感資料使用PBE加密
2. **傳輸安全**: MQ訊息加密傳輸
3. **存取記錄**: 完整的交易日誌記錄
4. **資料遮罩**: 敏感資料顯示遮罩

## 效能指標

### 系統效能要求
- **回應時間**: 一般查詢 < 3秒，複雜查詢 < 10秒
- **併發使用者**: 支援100個併發使用者
- **可用性**: 99.5%
- **資料一致性**: 強一致性

### 監控指標
- **CPU使用率**: < 80%
- **記憶體使用率**: < 85%
- **佇列深度**: < 1000
- **錯誤率**: < 1%

## 部署架構

### 環境分離
- **開發環境**: 單機部署，使用測試資料
- **測試環境**: 模擬生產環境配置
- **生產環境**: 高可用性部署

### 部署拓撲
```mermaid
graph TB
    subgraph "DMZ區域"
        LB[負載平衡器]
    end
    
    subgraph "應用區域"
        WEB1[Web Server 1]
        WEB2[Web Server 2]
        DISP1[Dispatcher 1]
        DISP2[Dispatcher 2]
        GW1[Gateway 1]
        GW2[Gateway 2]
    end
    
    subgraph "資料區域"
        DB1[(主資料庫)]
        DB2[(備援資料庫)]
        MQ1[MQ Server 1]
        MQ2[MQ Server 2]
    end
    
    LB --> WEB1
    LB --> WEB2
    WEB1 --> DISP1
    WEB2 --> DISP2
    DISP1 --> GW1
    DISP2 --> GW2
    
    WEB1 --> DB1
    WEB2 --> DB1
    DB1 --> DB2
    
    DISP1 --> MQ1
    DISP2 --> MQ1
    MQ1 --> MQ2
```

## 各專案詳細規格

### TCBJCICW (聯合徵信中心交易系統)

#### 功能模組
1. **標準查詢模組**
   - 個人信用報告查詢
   - 企業信用報告查詢
   - 信用評分查詢
   - 授信餘額查詢

2. **組合查詢模組 (CPX)**
   - 多項查詢組合
   - 規則引擎處理
   - 結果整合分析
   - 警示處理

3. **授權管理模組**
   - 使用者授權設定
   - 主管授權控制
   - 授權期限管理
   - 授權記錄查詢

#### 主要類別結構
```mermaid
classDiagram
    class StdTxidProfile {
        +String txid
        +String txidName
        +String txidType
        +String authType
        +boolean needAuth
        +boolean needCharge
        +boolean needSupervisor
    }

    class CPXTxidCheckService {
        +boolean checkCpxTxid(String, String)
        +Result processCpx(String, Map)
        +void loadCpxProducts()
    }

    class SupervisorAuth {
        +String userId
        +String custId
        +String txid
        +Date startDate
        +Date endDate
        +String status
    }
```

### TCBETCHW (票據交換所交易系統)

#### 功能模組
1. **票據查詢模組**
   - 支票查詢
   - 本行票據查詢
   - 他行票據查詢
   - 票據狀態查詢

2. **票據狀態管理**
   - 票據狀態更新
   - 退票處理
   - 止付處理
   - 狀態歷程記錄

3. **區域碼處理**
   - 不同區域票交所
   - 區域碼轉換
   - 路由規則設定

#### 主要類別結構
```mermaid
classDiagram
    class TchTxidProfile {
        +String txid
        +String txidName
        +String txidType
        +String areaCode
        +boolean needAuth
    }

    class BillStatus {
        +String billNo
        +String billType
        +String status
        +Date statusDate
        +String operator
    }

    class AreaCodeHandler {
        +String getAreaCode(String)
        +String routeToTch(String, String)
    }
```

### TCBEJMNW (系統監控平台)

#### 功能模組
1. **系統資源監控**
   - CPU使用率監控
   - 記憶體使用率監控
   - 磁碟空間監控
   - 網路狀態監控

2. **模組狀態監控**
   - Web模組狀態
   - Dispatcher狀態
   - Gateway狀態
   - 服務狀態檢查

3. **佇列深度監控**
   - MQ佇列深度
   - 佇列狀態監控
   - 告警機制
   - 歷史趨勢分析

4. **日誌查詢系統**
   - EJCIC交易日誌
   - ETCH交易日誌
   - ELOAN作業日誌
   - 系統錯誤日誌

#### 主要類別結構
```mermaid
classDiagram
    class MonSysUsage {
        +String sysId
        +String hostId
        +int cpuUsage
        +int memoryUsage
        +Timestamp runTime
        +String vmstat
    }

    class MonQDepth {
        +String sysId
        +String queueId
        +int depth
        +Timestamp measureTime
        +String status
    }

    class EJCICLogFile {
        +Date date
        +String txid
        +String msgid
        +String custId
        +String rc
        +String queryKey
    }
```

## 資料庫設計

### 核心資料表

#### 使用者授權相關
```sql
-- 使用者基本資料
CREATE TABLE USER_PROFILE (
    USER_ID VARCHAR(20) NOT NULL PRIMARY KEY,
    USER_NAME VARCHAR(50),
    DEPT_CODE VARCHAR(10),
    ROLE_CODE VARCHAR(10),
    STATUS CHAR(1),
    CREATE_DATE DATE,
    UPDATE_DATE DATE
);

-- 交易授權設定
CREATE TABLE SUPERVISOR_AUTH (
    USER_ID VARCHAR(20) NOT NULL,
    CUST_ID VARCHAR(20) NOT NULL,
    TXID VARCHAR(10) NOT NULL,
    AUTH_TYPE VARCHAR(10),
    START_DATE DATE,
    END_DATE DATE,
    STATUS CHAR(1),
    PRIMARY KEY (USER_ID, CUST_ID, TXID)
);
```

#### 交易相關
```sql
-- 交易設定檔
CREATE TABLE STD_TXID_PROFILE (
    TXID VARCHAR(10) NOT NULL PRIMARY KEY,
    TXID_NAME VARCHAR(100),
    TXID_DESC VARCHAR(200),
    TXID_TYPE VARCHAR(10),
    AUTH_TYPE VARCHAR(10),
    NEED_AUTH CHAR(1),
    NEED_CHARGE CHAR(1),
    NEED_SUPERVISOR CHAR(1),
    STATUS CHAR(1)
);

-- 交易日誌
CREATE TABLE EJCIC_LOG_FILE (
    LOG_ID VARCHAR(50) NOT NULL PRIMARY KEY,
    LOG_DATE DATE,
    TXID VARCHAR(10),
    MSG_ID VARCHAR(50),
    CUST_ID VARCHAR(20),
    USER_ID VARCHAR(20),
    RC VARCHAR(10),
    CREATE_TIME TIMESTAMP
);
```

#### 監控相關
```sql
-- 系統使用狀況
CREATE TABLE MON_SYS_USAGE (
    SYS_ID VARCHAR(10) NOT NULL,
    RUN_TIME TIMESTAMP NOT NULL,
    HOST_ID VARCHAR(20),
    CPU_USAGE INTEGER,
    MEMORY_USAGE INTEGER,
    VMSTAT VARCHAR(500),
    PRIMARY KEY (SYS_ID, RUN_TIME)
);

-- 佇列深度監控
CREATE TABLE MON_Q_DEPTH (
    SYS_ID VARCHAR(10) NOT NULL,
    MEASURE_TIME TIMESTAMP NOT NULL,
    Q01_DEPTH INTEGER,
    Q02_DEPTH INTEGER,
    -- ... 其他佇列深度欄位
    PRIMARY KEY (SYS_ID, MEASURE_TIME)
);
```

## 介面設計規範

### Web介面設計原則
1. **一致性**: 統一的UI風格和操作模式
2. **易用性**: 簡潔明瞭的操作流程
3. **回應性**: 快速的頁面載入和回應
4. **相容性**: 支援主流瀏覽器

### 頁面結構
```
┌─────────────────────────────────────┐
│ 系統標題列                          │
├─────────────────────────────────────┤
│ 導航選單                            │
├─────────────────────────────────────┤
│ 功能區域                            │
│ ┌─────────────┐ ┌─────────────────┐ │
│ │ 查詢條件    │ │ 查詢結果        │ │
│ │             │ │                 │ │
│ └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│ 狀態列                              │
└─────────────────────────────────────┘
```

### 錯誤處理機制
1. **輸入驗證**: 前端JavaScript驗證 + 後端驗證
2. **錯誤顯示**: 統一的錯誤訊息格式
3. **異常處理**: 完整的異常捕獲和記錄
4. **使用者友善**: 提供明確的錯誤說明和解決建議

## 整合介面規範

### MQ訊息格式
```xml
<Message>
    <Header>
        <MessageId>MSG_20231201_001</MessageId>
        <Timestamp>2023-12-01T10:30:00</Timestamp>
        <Source>TCBJCICW</Source>
        <Target>JCICGW</Target>
        <TxId>J001</TxId>
        <UserId>USER001</UserId>
        <CustId>CUST001</CustId>
    </Header>
    <Body>
        <QueryData>
            <!-- 查詢資料內容 -->
        </QueryData>
    </Body>
</Message>
```

### REST API規範 (未來擴展)
```
GET    /api/v1/transactions/{txId}     - 取得交易資訊
POST   /api/v1/transactions           - 建立新交易
PUT    /api/v1/transactions/{txId}     - 更新交易
DELETE /api/v1/transactions/{txId}     - 刪除交易

GET    /api/v1/monitor/systems        - 取得系統狀態
GET    /api/v1/monitor/queues         - 取得佇列狀態
```
