package mon.web.model;

import java.util.List;

import mon.model.STJLogFile;

/**
 * <pre>
 * MONTJ01VO.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class MONTJ01VO {
	boolean ok = false;
	// input
	STJLogFile model;

	// output
	List<STJLogFile> dataList;

	public boolean isOk() {
		return this.ok;
	}

	/**
	 * Sets the ok
	 * 
	 * @param ok
	 *            the ok to set
	 */
	public void setOk(boolean ok) {
		this.ok = ok;
	}

	/**
	 * Returns the input
	 * 
	 * @return the input
	 */
	public STJLogFile getInput() {
		return this.model;
	}

	/**
	 * Sets the input
	 * 
	 * @param input
	 *            the input to set
	 */
	public void setInput(STJLogFile input) {
		this.model = input;
	}

	/**
	 * Returns the input
	 * 
	 * @return the input
	 */
	public STJLogFile getOutput() {
		return this.model;
	}

	/**
	 * Sets the input
	 * 
	 * @param input
	 *            the input to set
	 */
	public void setOutput(STJLogFile output) {
		this.model = output;
	}

	/**
	 * Returns the dataList
	 * 
	 * @return the dataList
	 */
	public List<STJLogFile> getDataList() {
		return this.dataList;
	}

	/**
	 * Sets the dataList
	 * 
	 * @param dataList
	 *            the dataList to set
	 */
	public void setDataList(List<STJLogFile> dataList) {
		this.dataList = dataList;
	}

}
