package mon.service;

import mon.model.MonSysUsage;

/**
 * <pre>
 * MonitorService.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public interface MonSysUsageService {
	public abstract MonSysUsage doEJCICWebSysUsage();

	public abstract MonSysUsage doEJCICDispSysUsage();

	public abstract MonSysUsage doEJCICGWSysUsage();

	public abstract MonSysUsage doEloanDispSysUsage();

	public abstract MonSysUsage doETCHWebSysUsage();

	public abstract MonSysUsage doETCHDispSysUsage();

	public abstract MonSysUsage doETCHGWSysUsage();
}
