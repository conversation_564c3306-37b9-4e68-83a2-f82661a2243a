package mon.model;

/**
 * <pre>
 * BaseModel.java
 * </pre>
 *
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,new
 *          </ul>
 */
public interface ModelMeta {
	public Object[] toInsertArgs();
	public Object[] toKeyArgs();
	public Object[] toUpdateByKeyArgs();

	public String getInsertSql();

	public String getUpdateByKeySql();

	public String getDeleteByKeySql();

	public String getFindAllSql();

	public String getFindByKeySql();
}
