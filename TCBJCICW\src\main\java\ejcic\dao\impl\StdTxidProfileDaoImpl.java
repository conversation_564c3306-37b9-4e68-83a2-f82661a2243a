package ejcic.dao.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import ejcic.dao.StdTxidProfileDao;
import ejcic.model.StdTxidProfile;

/**
 * <pre>
 * StdtxidprofileDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("stdTxidProfileDao")
public class StdTxidProfileDaoImpl extends AbstractGenericDao<StdTxidProfile> implements StdTxidProfileDao {

	@Override
	public List<StdTxidProfile> findByFunc(String func) {
		final String SQL = "SELECT * FROM StdTxidProfile WHERE Func=? AND TXID NOT LIKE 'U%' AND TXID NOT LIKE 'I%' AND TXID NOT LIKE 'Q%' ORDER BY TXID ";
		return this.getJdbc().query(SQL, new Object[] { func }, ParameterizedBeanPropertyRowMapper.newInstance(modelType));
	}

	@Override
	public List<StdTxidProfile> findByFunc4SA(String func) {
		final String SQL = "SELECT STP.* FROM StdTxidProfile STP " + " JOIN  EJCICAP1.STDTXIDAUTH STA ON STP.TXID=STA.TXID "
				+ " Where STP.Func=? AND STP.TXID NOT LIKE 'U%' AND STP.TXID NOT LIKE 'I%' AND STP.TXID NOT LIKE 'Q%' AND STA.TEAMNO='SA' ORDER BY STP.TXID ";
		return this.getJdbc().query(SQL, new Object[] { func }, ParameterizedBeanPropertyRowMapper.newInstance(modelType));
	}

	@Override
	public List<StdTxidProfile> getUserPro06_Query() {
		return this.findByAll();
	}

	@Override
	public String getUserPro06_Insert(StdTxidProfile model) {
		final String SQL_CNT = "select COUNT(*) from StdTxidProfile where Txid=? ";
		int count = this.getJdbc().queryForInt(SQL_CNT, new Object[] { model.getTxid() });
		if (count > 0) {
			return "4";
		}
		count = this.insert(model);
		if (count < 0) {
			return "97";
		} else {
			return "1";
		}
	}

	@Override
	public List<StdTxidProfile> findByChinese() {
		final String SQL = "SELECT TXID,Txcname,Reason FROM StdTxidProfile Where Func='T' AND TXID NOT LIKE 'U%' AND TXID NOT LIKE 'I%' ORDER BY TXID";
		return this.getJdbc().query(SQL, new Object[] {}, ParameterizedBeanPropertyRowMapper.newInstance(modelType));
	}

	@Override
	public List<StdTxidProfile> findByChinese4SA() {
		final String SQL = "SELECT STP.TXID,STP.Txcname,STP.Reason FROM StdTxidProfile STP " + " JOIN  EJCICAP1.STDTXIDAUTH STA ON STP.TXID=STA.TXID "
				+ " Where STP.Func='T' AND STP.TXID NOT LIKE 'U%' AND STP.TXID NOT LIKE 'I%' AND STA.TEAMNO='SA' ORDER BY STP.TXID";
		return this.getJdbc().query(SQL, new Object[] {}, ParameterizedBeanPropertyRowMapper.newInstance(modelType));
	}

	@Override
	public List<StdTxidProfile> find4JCICCT02() {
		final String SQL = "SELECT TXID,Txcname,IDTYPE,Reason FROM StdTxidProfile Where Func='C' AND TXID NOT LIKE 'U%' AND TXID NOT LIKE 'I%' AND TXID NOT LIKE 'Q%' ORDER BY TXID";
		return this.getJdbc().query(SQL, new Object[] {}, ParameterizedBeanPropertyRowMapper.newInstance(modelType));
	}

	@Override
	public List<StdTxidProfile> find4JCICCT02_SA() {
		final String SQL = "SELECT STP.TXID,STP.Txcname,STP.Reason FROM StdTxidProfile STP " + " JOIN  EJCICAP1.STDTXIDAUTH STA ON STP.TXID=STA.TXID "
				+ " Where STP.Func='C' AND STP.TXID NOT LIKE 'U%' AND STP.TXID NOT LIKE 'I%' AND STP.TXID NOT LIKE 'Q%' AND STA.TEAMNO='SA' ORDER BY STP.TXID";
		return this.getJdbc().query(SQL, new Object[] {}, ParameterizedBeanPropertyRowMapper.newInstance(modelType));
	}

	@Override
	public List<String> findAllBelong() {
		final String SQL = "SELECT BELONG FROM STDTXIDPROFILE  WHERE FUNC='S' AND TXID NOT LIKE 'U%' AND TXID NOT LIKE 'I%' GROUP BY BELONG";
		SqlRowSet srs = this.getJdbc().queryForRowSet(SQL, new Object[] {});
		List<String> result = new ArrayList<String>();
		while (srs.next()) {
			result.add(srs.getString("BELONG"));
		}
		return result;
	}

	@Override
	public boolean getUserPro06_Edit(StdTxidProfile model) {
		return this.update(model) > 0;
	}

	@Override
	public boolean getUserPro06_Delete(String txId) {
		return this.delete(new String[] { txId }) > 0;
	}
}