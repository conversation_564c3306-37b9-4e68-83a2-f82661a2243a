package mon.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;

import org.apache.log4j.Logger;

public class MsgDumpHelper {
	protected static Logger aplog = Logger.getLogger(MsgDumpHelper.class);
	private static boolean USE_FULL_DUMP = false;

	public static String dump(byte[] data) {
		return dump(data, "UTF-8");
	}

	public synchronized static void setFullDump(boolean flag) {
		USE_FULL_DUMP = flag;
	}

	public static String dump(byte[] data, String enc) {
		long t1 = System.currentTimeMillis();
		StringBuffer result = new StringBuffer();
		if (data == null) {
			return null;
		}
		int totalLen = data.length;
		try {
			if (USE_FULL_DUMP) {
				ByteArrayOutputStream outStr = new ByteArrayOutputStream();
				PrintWriter out;
				InputStream in = null;

				try {
					out = new PrintWriter(outStr);
					in = new ByteArrayInputStream(data, 0, totalLen);
					out.println();
					print(in, out, enc);
					result.append(outStr.toString(enc));
				} catch (Exception ex) {
					return null;
				} finally {
					try {
						if (in != null)
							in.close();
					} catch (IOException ex) {
					}
					result.append("$LEN=[").append(totalLen).append("] ");
					result.append("TIME=[").append((System.currentTimeMillis() - t1)).append("]ms!\n");
				}
			} else {
				result.append(bytes2HexStr(data, enc));
				result.append("@LEN=[").append(totalLen).append("] ");
				result.append("TIME=[").append((System.currentTimeMillis() - t1)).append("]ms\n");

			}
		} catch (Exception ex) {
			ex.printStackTrace();
			aplog.warn("dump() Exception!!", ex);
		}
		return result.toString();
	}

	private static String bytes2HexStr(byte[] dest, String enc) {
		StringBuffer sb = new StringBuffer("\n");
		int idx = 0;
		int lasPos = -1;
		int len = 0;
		String stemp = null;
		for (int i = 0, size = dest.length; i < size; i++) {
			if (i == 0) {
				sb.append(padZero(len));
			}
			if (idx % 16 == 0 && i > 0) {
				stemp = new String(dest, i - 16 < 0 ? 0 : i - 16, 16);
				stemp = stemp.replace('\r', '.').replace('\n', '.');
				sb.append("|").append(stemp).append("|").append("\n");
				sb.append(padZero(len += 16));
				idx = 0;
				lasPos = i;
			}
			if (idx % 8 == 0) {
				sb.append(" ");
			}
			sb.append(toHex(dest[i]) + " ");
			idx++;
		}
		if (idx > 0) {
			int spaceLen = 16 - (dest.length - lasPos);
			for (int i = 0; i < spaceLen; i++) {
				sb.append(".. ");
				if (spaceLen - i == 9) {
					sb.append(" ");
				}
			}

			stemp = new String(dest, lasPos, dest.length - lasPos);
			stemp = stemp.replace('\r', '.').replace('\n', '.');
			sb.append("|").append(stemp);
			for (int i = 0; i < spaceLen; i++) {
				sb.append(" ");
			}
			sb.append("|").append("\n");
		}
		return sb.toString();
	}

	private static String padZero(int val) {
		String sval = "0000000000" + val;
		return "D:" + sval.substring(sval.length() - 7);
	}

	private static String toHex(byte b) {
		return ("" + "0123456789ABCDEF".charAt(0xf & b >> 4) + "0123456789ABCDEF".charAt(b & 0xf));
	}

	private static void print(InputStream in, PrintWriter out, String enc) {
		if (out == null) {
			return;
		}

		if (in == null) {
			throw new NullPointerException("Input(InputStream) is null!!");
		}

		try {
			byte[] data; // Input data block
			int off; // Leftover data bytes
			int addr; // Data address

			// Read and dump the contents of the input stream
			data = new byte[64 * 1024];
			off = 0;
			addr = 0;

			for (;;) {
				int len, dlen;

				len = in.read(data, off, data.length - off);

				if (len <= 0) {
					len = 0;
				}

				dlen = (off + len) & ~0x0000000F;
				printAt(out, data, 0, dlen, addr, enc);
				addr += dlen;
				off = off + len - dlen;

				if (off > 0) {
					System.arraycopy(data, dlen, data, 0, off);
				}

				if (len == 0) {
					printAt(out, data, 0, off, addr, enc);
					addr += off;
					break;
				}
			}

			out.flush();
		} catch (IOException ex) {
			// Ignore
		}
	}

	private static void printAt(PrintWriter out, byte[] data, int off, int len, int base, String enc) {
		int loc, end;
		enc = enc == null ? "" : enc.trim();

		if (off >= data.length) {
			off = data.length;
		}

		end = off + len;

		if (end >= data.length) {
			end = data.length;
		}

		len = end - off;

		if (len <= 0) {
			return;
		}

		loc = (off / 0x10) * 0x10;

		for (int i = loc; i < end; i += 0x10, loc += 0x10) {
			int j, v;

			v = base + loc;

			for (j = (8 - 1) * 4; j >= 0; j -= 4) {
				int d;
				d = (v >>> j) & 0x0F;
				d = (d < 0xA ? d + '0' : d - 0xA + 'A');
				out.print((char) d);
			}

			out.print("  ");

			for (j = 0x00; i + j < off; j++) {
				out.print(".. ");
			}

			for (; j < 0x10 && i + j < end; j++) {
				int ch, d;

				if (j == 0x08) {
					out.print(' ');
				}

				ch = data[i + j] & 0xFF;

				d = (ch >>> 4);
				d = (d < 0xA ? d + '0' : d - 0xA + 'A');
				out.print((char) d);
				d = (ch & 0x0F);
				d = (d < 0xA ? d + '0' : d - 0xA + 'A');
				out.print((char) d);
				out.print(' ');
			}

			for (; j < 0x10; j++) {
				if (j == 0x08) {
					out.print(' ');
				}
				out.print(".. ");
			}

			byte[] tmp = null;

			if (!"".equals(enc)) {
				try {
					tmp = new String(data, enc).getBytes();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

			out.print(" |");

			for (j = 0x00; i + j < off; j++) {
				out.print(' ');
			}

			for (; j < 0x10 && i + j < end; j++) {
				int ch = 0;

				if (enc.equals("937")) {
					ch = tmp[i + j] & 0xFF;
				} else {
					ch = data[i + j] & 0xFF;
				}

				if (ch < 0x20 || ch >= 0x7F && ch < 0xA0 || ch > 0xFF) {
					ch = '.';
				}

				out.print((char) ch);
			}

			for (; j < 0x10; j++) {
				out.print(' ');
			}

			out.println("|");
		}
	}

}
