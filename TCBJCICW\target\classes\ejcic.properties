###########################################
#\u8cc7\u6599\u5eab\u9023\u7dda\u8a2d\u5b9a
###########################################
DB_DATASOURCE=java:comp/env/jdbc/EJCIC
DB_DRIVER=com.ibm.db2.jcc.DB2Driver
DB_URL=************************************************************************************************;
#DB_URL=********************************************************************************************;
DB_MAX_CONN=50
DB_CONN_TIMEOUT=10000
###########################################
#EJCIC MQ\u8a2d\u5b9a
###########################################
#MQ SERVER\u7684IP\u4f4d\u5740
MQ_SERVER=127.0.0.1
#MQ_SERVER=*********
#MQ SERVER\u7684\u9023\u63a5\u57e0
MQ_PORT=1414
#Queue Manager\u540d\u7a31
MQ_MGR=MQJ006D
#MQ\u4f3a\u670d\u5668\u9023\u7dda\u901a\u9053\u540d\u7a31
MQ_CHANNEL=MQJ006D.SVRCONN.JCIC
#\u8a0a\u606f\u5728\u4f47\u5217\u7684\u6709\u6548\u6642\u9593(\u79d2)
MQ_MSG_EXPIRY=86400
#\u524d\u7aef\u6536\u53d6MQ\u903e\u6642\u6642\u9593(\u79d2)
MQ_TIMEOUT=300
#MQ \u6536\u53d6\u9593\u9694\u7b49\u5f85\u6642\u9593(\u6beb\u79d2ms)
MQ_WAIT_FOR_RECV=20000
#\u6aa2\u67e5\u8a2d\u5b9a\u6a94\u8cc7\u6599\u662f\u5426\u5df2\u7d93\u66f4\u65b0\u4e4b\u9031\u671f(\u79d2)
SYS_CACHE_REFRESH=1800
#\u53d6\u5f97\u66f4\u65b0\u65e5\u671f\u7684Queue(RefreshManager\u4f7f\u7528)
MQ_RFM=
#CCSID
MQ_CCSID=950
#\u53d6\u5f97\u66f4\u65b0\u65e5\u671f\u7684\u7d44\u5408\u7de8\u865f
MQ_RFM_QXXX=
#MQ\u6e2c\u8a66\u4f7f\u7528\u7684QUEUE
MQ_TEST=MQJ006D.MONITOR.QL
#\u9810\u8a2d\u9001\u7d66JCIC\u8a0a\u606f\u5167REPLY_TO_QMGR\u503c
MQ_DEF_REPLY_TO_QMGR=J006
#\u9810\u8a2d\u9001\u7d66JCIC\u8a0a\u606f\u5167REPLY_TO_Q\u503c
MQ_DEF_REPLY_TO_QNAME=Q006IF01
#MQ\u4f7f\u7528\u8005ID
MQ_USER_ID=ejcicmq
#MQ\u4f7f\u7528\u8005pwd
MQ_USER_PWD=
###########################################
#MQ\u8a2d\u5b9a FOR GW
###########################################
#GW\u9001\u7d66JCIC\u7684QUEUE
MQ_SEND=MQJ006D.JMET.QR
#JCIC\u56de\u50b3\u7d66GW\u7684QUEUE
MQ_RECV=MQJ006D.006IF01.QL
#\u767b\u5165JCIC\u4f7f\u7528\u8005\u5e33\u865f
GW_USERID=006ET001
#\u767b\u5165JCIC\u4f7f\u7528\u8005\u5bc6\u78bc
GW_PASSW0RD=12345678
#GW JRECV\u57f7\u884c\u7dd2\u6578\u76ee(\u8acb\u8a2d\u62101)
GW_RECV_THREAD=1
#GW JSEND\u57f7\u884c\u7dd2\u6578\u76ee
GW_SEND_THREAD=1
#GW \u9023\u7dda\u72c0\u614b\u6a94
GW_STATUS_FILE=/aphome/ejcic/conf/ejcic.gw.status
#GW\u8655\u7406\u5931\u6578\u4e4b\u8a0a\u606f\u8a18\u9304\u76ee\u9304
GW_ERR_SAVE_DIR=/aphome/ejcic/logs/gw_error
###########################################
#MQ\u8a2d\u5b9a FOR HEADER QID (\u7576MSG QID\u8207MQ Q\u4e0d\u540c)
###########################################
#MSG HEADER QID\u8207MQ QUEUE\u7684\u540d\u7a31\u5de6\u908a\u5dee\u7570\u5b57\u4e32
QID_LEFT_PAD=MQJ006D.
#MSG HEADER QID\u8207MQ QUEUE\u7684\u540d\u7a31\u53f3\u908a\u5dee\u7570\u5b57\u4e32
QID_RIGHT_PAD=.QL

###########################################
#FOR WEB JMS
###########################################
#JMS QCF
JMS_JNDI_QCF=jms/EJCIC_QCF
#JMS QCF BEAN ID
JMS_JNDI_QCF_BEAN_ID=appWebQCF

###########################################
#\u7cfb\u7d71\u53c3\u6578,\u9810\u8a2d\u503c
###########################################
#\u9280\u884c\u4ee3\u865f
SYS_BANK_ID=006
#\u9280\u884c\u5206\u884c\u4ee3\u865f
SYS_BRANCH_ID=IF01
#\u6a19\u6e96\u67e5\u8a62\u4e4b\u6709\u6548\u5929\u6578
SYS_HCYCLE=7
#\u7d44\u5408\u67e5\u8a62\u4e4b\u6709\u6548\u5929\u6578
SYS_QCYCLE=30
#\u9810\u8a2d\u4ed8\u8cbb\u5206\u884c\u4ee3\u865f
SYS_HCHARGE=0012
#\u7d44\u5408\u67e5\u8a62\u908f\u8f2f\u5224\u65b7\u5b9a\u7fa9XML\u6a94\u4e4b\u8def\u5f91
CPX_RULE_XML=cpxxml
#\u6a94\u6848\u7de8\u8f2f\u4e4b\u5099\u4efd\u76ee\u9304
FILE_EDIT_BAKUP=/aphome/ejcic/bak
###########################################
#FOR DISPATCHER
###########################################
SYS_ID=W
#\u524d\u7aef\u7d66DISP\u7684QUEUE
MQ_W2D=MQJ006D.WAS2DIS.QL
#DISP\u7d66GW\u7684QUEUE
MQ_D2G=MQJ006D.JCICONN.QL
#GW\u7d66DISP\u7684QUEUE BASE\u540d\u7a31(\u6578\u91cf\u4f9dSYS_JCIC_THREAD\u6578\u503c: W01,W02,W03...)
MQ_G2D=MQJ006D.JCICW01.QL
#DISP\u7d66\u524d\u7aef\u7684QUEUE
MQ_D2W=MQJ006D.DIS2WAS.QL
#DISP\u5167Q3\u7d66Q1\u7684QUEUE
MQ_D2D=MQJ006D.JCICW00.QL
#\u63a5\u6536WEB\u7aef\u7684\u6700\u5927Thread\u6578\u91cf(\u6700\u5c0f=2)
SYS_WEB_THREAD=6
#\u63a5\u6536JCIC\u7aef\u7684\u6700\u5927Thread\u6578\u91cf(\u6700\u5927=9)
SYS_JCIC_THREAD=8
###########################################
#FOR ELOAN (-Dejcic.disp.sysid=ELOAN)
###########################################
ELOAN.SYS_ID=L
#\u524d\u7aef\u7d66DISP\u7684QUEUE
ELOAN.MQ_W2D=MQJ006D.ELN2DIS.QL
#DISP\u7d66GW\u7684QUEUE
ELOAN.MQ_D2G=MQJ006D.JCICONN.QL
#GW\u7d66DISP\u7684QUEUE BASE\u540d\u7a31(\u6578\u91cf\u4f9dSYS_JCIC_THREAD\u6578\u503c: W01,W02,W03...)
ELOAN.MQ_G2D=MQJ006D.JCICL01.QL
#DISP\u7d66\u524d\u7aef\u7684QUEUE
ELOAN.MQ_D2W=MQJ006D.DIS2ELN.QL
#DISP\u5167Q3\u7d66Q1\u7684QUEUE
ELOAN.MQ_D2D=MQJ006D.JCICL00.QL
#\u63a5\u6536WEB\u7aef\u7684\u6700\u5927Thread\u6578\u91cf(\u6700\u5c0f=2)
ELOAN.SYS_WEB_THREAD=6
#\u63a5\u6536JCIC\u7aef\u7684\u6700\u5927Thread\u6578\u91cf(\u6700\u5927=9)
ELOAN.SYS_JCIC_THREAD=8
###########################################
#FOR EXTERNAL (-Dejcic.disp.sysid=EXT)
###########################################
EXT.SYS_ID=X
#\u524d\u7aef\u7d66DISP\u7684QUEUE
EXT.MQ_W2D=MQJ006D.EXT2DIS.QL
#DISP\u7d66GW\u7684QUEUE
EXT.MQ_D2G=MQJ006D.JCICONN.QL
#GW\u7d66DISP\u7684QUEUE BASE\u540d\u7a31(\u6578\u91cf\u4f9dSYS_JCIC_THREAD\u6578\u503c: W01,W02,W03...)
EXT.MQ_G2D=MQJ006D.JCICX01.QL
#DISP\u7d66\u524d\u7aef\u7684QUEUE
EXT.MQ_D2W=MQJ006D.DIS2EXT.QL
#DISP\u5167Q3\u7d66Q1\u7684QUEUE
EXT.MQ_D2D=MQJ006D.JCICX00.QL
#\u63a5\u6536WEB\u7aef\u7684\u6700\u5927Thread\u6578\u91cf(\u6700\u5c0f=2)
EXT.SYS_WEB_THREAD=6
#\u63a5\u6536JCIC\u7aef\u7684\u6700\u5927Thread\u6578\u91cf(\u6700\u5927=9)
EXT.SYS_JCIC_THREAD=8
###########################################
#FOR EAI SYSTEM (-Dejcic.disp.sysid=EAI)
###########################################
EAI.SYS_ID=E
#\u524d\u7aef\u7d66DISP\u7684QUEUE
EAI.MQ_W2D=MQJ006D.EAI2DIS.QC
#DISP\u7d66GW\u7684QUEUE
EAI.MQ_D2G=MQJ006D.JCICONN.QL
#GW\u7d66DISP\u7684QUEUE BASE\u540d\u7a31(\u6578\u91cf\u4f9dSYS_JCIC_THREAD\u6578\u503c: W01,W02,W03...)
EAI.MQ_G2D=MQJ006D.JCICE01.QL
#DISP\u7d66\u524d\u7aef\u7684QUEUE
#EAI.MQ_D2W=MQJ006D.DIS2EAI.QL
EAI.MQ_D2W=MQEAID.AP.RQ.QC
#DISP\u5167Q3\u7d66Q1\u7684QUEUE
EAI.MQ_D2D=MQJ006D.JCICE00.QL
#\u63a5\u6536WEB\u7aef\u7684\u6700\u5927Thread\u6578\u91cf(\u6700\u5c0f=2)
EAI.SYS_WEB_THREAD=1
#\u63a5\u6536JCIC\u7aef\u7684\u6700\u5927Thread\u6578\u91cf(\u6700\u5927=9)
EAI.SYS_JCIC_THREAD=1
#EAI\u96fb\u6587\u7684\u5bc6\u78bc
EAI.USER_PWD=c6922b6ba9e0939583f973bc1682493351ad4fe8
###########################################
#\u8a0a\u606f\u653e\u5728QUEUE\u5167\u7684\u6709\u6548\u6642\u9593(\u79d2) MQ MSG EXPIRY (SEC)
###########################################
#\u8a0a\u606f\u5728WAS2DIS QUEUE\u7684\u6709\u6548\u79d2\u6578
MQ_MSG_EXPIRY_WAS2DIS=300
#\u8a0a\u606f\u5728DIS2GW QUEUE\u7684\u6709\u6548\u79d2\u6578
MQ_MSG_EXPIRY_DIS2GW=300
#\u8a0a\u606f\u5728GW2DIS QUEUE\u7684\u6709\u6548\u79d2\u6578
MQ_MSG_EXPIRY_GW2DIS=9000
#\u8a0a\u606f\u5728Q3TOQ1 QUEUE\u7684\u6709\u6548\u79d2\u6578
MQ_MSG_EXPIRY_Q3TOQ1=300
#\u8a0a\u606f\u5728Q1TOQ3 QUEUE\u7684\u6709\u6548\u79d2\u6578
MQ_MSG_EXPIRY_Q1TOQ3=300
#\u8a0a\u606f\u5728DIS2WAS QUEUE\u7684\u6709\u6548\u79d2\u6578
MQ_MSG_EXPIRY_DIS2WAS=300
#\u8a0a\u606f\u5728ELN2WAS QUEUE\u7684\u6709\u6548\u79d2\u6578
ELOAN.MQ_MSG_EXPIRY_DIS2WAS=1800
###########################################
#\u76e3\u63a7\u4f7f\u7528QUEUE
###########################################
MQ_MONITOR=MQJ006D.MONITOR.QL
MQ_MONREQ=MQJ006D.MONREQ.QL
MQ_MONRESP=MQJ006D.MONRESP.QL
###########################################
#NOTES
###########################################
#NOTES.SERVER=10.0.31.79
#NOTES.SERVER2=10.0.31.80
NOTES.SERVER=10.0.6.196
NOTES.SERVER2=10.0.59.19
NOTES.PORT=63148
NOTES.LOGINID=ejcicuser
NOTES.PASSW0RD=ejcicuser
NOTES.USER.DB=ELUSER.nsf
NOTES.USER.VW=V_All_User2
NOTES.DEPT.DB=ELBRANCH.nsf
NOTES.DEPT.VW=VEL05001
###########################################
#ETCH URL
###########################################
#\u9023\u7d50\u5230ETCH\u7684URL
URL_ETCH=http://*********:9081/TCBETCHW/WebController?_HANDLER_=ETCHCM02


###########################################
#\u5176\u5b83
###########################################
#\u6a19\u6e96\u67e5\u8a62\u904e\u6ffe\u5668(\u683c\u5f0f: \u67e5\u8a62\u9805\u76ee1|\u539f\u59cb\u5b57\u4e32|\u53d6\u4ee3\u4e4b\u5b57\u4e32,\u67e5\u8a62\u9805\u76ee1|\u539f\u59cb\u5b57\u4e32|\u53d6\u4ee3\u4e4b\u5b57\u4e32... )
#STD_TXID_BTN_BYPASS=HA12|<input type=submit value=\u67e5\u8a62>|<input type=submit value=\u67e5\u8a62 disabled>
STD_TXID_BTN_BYPASS=HA12|<input type=submit value=\u67e5\u8a62>|<input type=submit value=\u67e5\u8a62 disabled>

#\u662f\u5426\u5141\u8a31\u865b\u64ec\u4f7f\u7528\u8005\u767b\u5165(\u6b63\u5f0f\u74b0\u5883\u8acb\u8a2d\u70baN)
DEBUG_BYPASS_SSO=Y
