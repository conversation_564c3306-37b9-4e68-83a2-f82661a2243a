package mon.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import mon.dao.STJLogFileDao;
import mon.jdbc.JdbcSqlUtils;
import mon.model.STJLogFile;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * <pre>
 * STJLogFileDaoImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Repository("STJLogFileDao")
public class STJLogFileDaoImpl implements STJLogFileDao {

	@Resource
	@Qualifier("stjJcbcTemplate")
	private JdbcTemplate jdbc;

	@Override
	public List<STJLogFile> query(STJLogFile model) {

		List<String> strList = new ArrayList<String>();
		StringBuffer whereStr = new StringBuffer();

		if (StringUtils.isNotBlank(model.getYyy())) {
			whereStr.append(" AND YYY = ? ");
			strList.add(model.getYyy());
		}

		if (StringUtils.isNotBlank(model.getMm())) {
			whereStr.append(" AND MM = ? ");
			strList.add(model.getMm());
		}

		if (StringUtils.isNotBlank(model.getSendType())) {
			whereStr.append(" AND SENDTYPE = ? ");
			strList.add(model.getSendType());
		}

		if (StringUtils.isNotBlank(model.getStep())) {
			whereStr.append(" AND STEP = ? ");
			strList.add(model.getStep());
		}

		if (StringUtils.isNotBlank(model.getRunSNo())) {
			whereStr.append(" AND RUNSNO = ? ");
			strList.add(model.getRunSNo());
		}

		if (StringUtils.isNotBlank(model.getRc())) {
			if ("0000".equals(model.getRc()) || "0001".equals(model.getRc())) {
				whereStr.append(" AND RC = ? ");
				strList.add(model.getRc());
			} else {
				whereStr.append(" AND  RC <> '0000' AND  RC <> '0001' ");
			}
		}

		String sql = " select * from LOGFILE where 1=1 " + whereStr + " order by RUNSNO desc";

		JdbcSqlUtils.printSql(sql, strList.toArray());

		return this.jdbc
				.query(sql, strList.toArray(), ParameterizedBeanPropertyRowMapper.newInstance(STJLogFile.class));
	}

	@Override
	public STJLogFile findResult(String runSNo) {
		String sql = " select * from LOGFILE where runSNo=? ";

		String[] args = new String[] { runSNo };

		JdbcSqlUtils.printSql(sql, args);

		List<STJLogFile> rs = this.jdbc.query(sql, args,
				ParameterizedBeanPropertyRowMapper.newInstance(STJLogFile.class));
		if (rs.size() > 0) {
			return rs.get(0);
		}
		return null;
	}
}
