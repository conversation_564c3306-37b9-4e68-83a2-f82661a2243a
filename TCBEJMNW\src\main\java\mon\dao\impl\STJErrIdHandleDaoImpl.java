package mon.dao.impl;

import java.util.List;

import javax.annotation.Resource;

import mon.dao.STJErrIdHandleDao;
import mon.jdbc.JdbcSqlUtils;
import mon.model.STJDataTable;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

/**
 * <pre>
 * STJDataTableImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Repository("STJErrIdHandleDao")
public class STJErrIdHandleDaoImpl implements STJErrIdHandleDao {
	@Resource
	@Qualifier("stjJcbcTemplate")
	private JdbcTemplate jdbc;

	@Override
	public int delete(String id, List<STJDataTable> datatblList) {
		int cnt = 0;
		for (STJDataTable tbl : datatblList) {
			String sql = " delete from ERROR_ID_HANDLE where id=? and tableName=?";
			Object[] args = new Object[] { id,tbl.getTableName() };
			JdbcSqlUtils.printSql(sql, args);
			cnt += this.jdbc.update(sql, args);
		}

		return cnt;
	}
	
	
	@Override
	public int insert(String id, List<STJDataTable> datatblList) {
		int cnt = 0;
		for (STJDataTable tbl : datatblList) {
			String sql = " insert into ERROR_ID_HANDLE (id,tableName)VALUES(?,?)";
			Object[] args = new Object[] { id ,tbl.getTableName()};
			JdbcSqlUtils.printSql(sql, args);
			cnt += this.jdbc.update(sql, args);
		}

		return cnt;
	}
}
