# TCBJCICW 專案中 TOJCIC 參數分析

## 概述

本文檔分析 TCBJCICW 專案中處理 `isJcic` 參數的邏輯，該參數決定系統是否強制發查聯徵中心或直接從資料庫取得資料。

## 關鍵程式碼位置

### 1. 處理強制發查聯徵的核心邏輯

在 `JCICST00Service.java` 檔案中的 `getW2DStdMessage` 方法（第 620-655 行）中，有一段處理是否強制發查聯徵中心的邏輯：

```java
// 判斷查詢項目是否強制向聯徵查詢
String forceFlag = logfile.getForceflag();
String[] forceToJcic = StringUtils.split(StringUtils.trimToEmpty(this.sysParamService.getProperty(SysParamConst.STDTXID_FORCE_TOJCIC)), ",");
if (ArrayUtils.contains(forceToJcic, logfile.getTxid())) {
    forceFlag = "Y";
    logger.warn("查詢項目[{}]在強制向聯徵查詢的清單內，將F_BFORCEFLAG : {} --> {}", new String[] { logfile.getTxid(), logfile.getForceflag(), forceFlag });
}
msg.setItem(W2DStdMessage.F_BFORCEFLAG, forceFlag);
```

### 2. 前端參數處理

在 `JCICCS01Handler.java` 檔案中（第 120-132 行），可以看到處理前端傳入的 `force2Jcic` 參數：

```java
// 取得其他參數
String force2Jcic = StringUtils.trimToEmpty(paramMap.get("force2Jcic"));
String isprint = StringUtils.trimToEmpty(paramMap.get("CPRINT"));
isprint = isprint.equalsIgnoreCase("Y") ? "Y" : "N";
String reason = StringUtils.trimToEmpty(paramMap.get("reason"));
reason = (reason.length() < 3) ? "000" : reason;
// 設定參數
JCICST00VO vo = new JCICST00VO();
vo.setTxid(item);
LogFile logfile = new LogFile();
logfile.setQuerykey1(queryKey1);
logfile.setQuerykey2(queryKey2);
logfile.setTxid(item);
logfile.setForceflag(force2Jcic);
```

## 處理流程分析

1. **前端參數傳遞**：
   - 前端可以通過 `force2Jcic` 參數指定是否強制發查聯徵中心
   - 此參數會被設置到 `LogFile` 物件的 `forceflag` 屬性中

2. **系統參數配置**：
   - 系統參數 `SysParamConst.STDTXID_FORCE_TOJCIC` 中定義了需要強制向聯徵查詢的項目清單
   - 這個參數是以逗號分隔的查詢項目代碼列表

3. **強制查詢邏輯**：
   - 在建立查詢訊息時，系統會檢查當前查詢項目是否在強制查詢清單中
   - 如果在清單中，則無論前端傳入的 `force2Jcic` 是什麼，都會將 `forceFlag` 設為 "Y"
   - 系統會記錄一條警告日誌，說明已將 `forceFlag` 強制設為 "Y"

4. **訊息傳遞**：
   - 最終的決定是通過 `F_BFORCEFLAG` 欄位傳遞給後續處理程序
   - 這個欄位會影響系統是否向聯徵中心發送查詢請求或直接從資料庫取得資料

## 系統參數配置

系統參數 `SysParamConst.STDTXID_FORCE_TOJCIC` 的配置方式：

1. 在系統參數表中設定 `STDTXID_FORCE_TOJCIC` 參數
2. 參數值為以逗號分隔的查詢項目代碼列表，例如：`"HA01,HA02,HA03"`
3. 當查詢項目代碼在此列表中時，系統會強制向聯徵中心發送查詢

## 使用案例

### 案例一：正常查詢流程

1. 用戶發起查詢請求，未指定 `force2Jcic` 參數
2. 系統檢查查詢項目是否在強制查詢清單中
   - 如果在清單中，設置 `forceFlag = "Y"`
   - 如果不在清單中，保持 `forceFlag` 為原始值（通常為 `"N"` 或空值）
3. 系統根據 `forceFlag` 的值決定是否向聯徵中心發送查詢

### 案例二：用戶強制查詢

1. 用戶發起查詢請求，指定 `force2Jcic = "Y"`
2. 系統檢查查詢項目是否在強制查詢清單中
   - 無論是否在清單中，`forceFlag` 都會保持為 `"Y"`
3. 系統向聯徵中心發送查詢請求

### 案例三：系統強制查詢

1. 用戶發起查詢請求，指定 `force2Jcic = "N"`
2. 系統檢查查詢項目是否在強制查詢清單中
   - 如果在清單中，系統會覆蓋用戶設定，將 `forceFlag` 設為 `"Y"`
   - 系統記錄警告日誌
3. 系統向聯徵中心發送查詢請求，忽略用戶的設定

## 結論

TCBJCICW 專案中的 TOJCIC 機制允許系統管理員通過配置系統參數來控制某些特定查詢項目必須向聯徵中心發送查詢，而不是直接從資料庫取得資料。這種機制可能是為了確保獲取最新的信用資訊或符合特定的業務規則。

通過這種設計，系統可以在保持靈活性的同時，確保關鍵查詢始終獲取最新的聯徵中心資料，提高系統的可靠性和資料的準確性。
