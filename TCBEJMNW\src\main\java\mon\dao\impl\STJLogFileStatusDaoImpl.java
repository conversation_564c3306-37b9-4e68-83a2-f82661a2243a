package mon.dao.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import mon.dao.STJLogFileStatusDao;
import mon.jdbc.JdbcSqlUtils;
import mon.model.STJLogFileStatus;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * <pre>
 * STJLogFileStatusDaoImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Repository("STJLogFileStatusDao")
public class STJLogFileStatusDaoImpl implements STJLogFileStatusDao {

	@Resource
	@Qualifier("stjJcbcTemplate")
	private JdbcTemplate jdbc;

	@Override
	public int insertOrUpdate(STJLogFileStatus model) {
		int ret = 0;
		if (this.findByPhase(model).size() > 0) {
			ret = this.updateLogFileByYYYMM(model);
		} else {
			ret = this.insertLogFile(model);
		}
		return ret;
	}

	@Override
	public int insertLogFile(STJLogFileStatus model) {
		String sql = "INSERT INTO LOGFILE_ST(RunsNo,Yyy,Mm,SendType,phase,Rc,EndDate) VALUES(?,?,?,?,?,?,?)";
		Object[] args = new Object[] { model.getRunsNo(), model.getYyy(), model.getMm(), model.getSendType(),
				model.getPhase(), model.getRc(), model.getEndDate() };
		JdbcSqlUtils.printSql(sql, args);
		return this.jdbc.update(sql, args);
	}

	@Override
	public List<STJLogFileStatus> findByPhase(STJLogFileStatus model) {
		String sql = "select * from LOGFILE_ST  WHERE YYY=? AND MM=? AND sendType=? and phase=? ";
		Object[] args = new Object[] { model.getYyy(), model.getMm(), model.getSendType(), model.getPhase() };
		JdbcSqlUtils.printSql(sql, args);
		return this.jdbc.query(sql, args, ParameterizedBeanPropertyRowMapper.newInstance(STJLogFileStatus.class));
	}

	@Override
	public int updateLogFileByYYYMM(STJLogFileStatus model) {
		String sql = "UPDATE LOGFILE_ST SET Rc=?,EndDate=? WHERE YYY=? AND MM=? AND sendType=? and phase=?";
		Object[] args = new Object[] { model.getRc(), model.getEndDate(), model.getYyy(), model.getMm(),
				model.getSendType(), model.getPhase() };
		JdbcSqlUtils.printSql(sql, args);
		return this.jdbc.update(sql, args);
	}

	@Override
	public String queryRCByCondition(String phase, String year, String month, String sendType) {

		String sql = "SELECT TOP (1) RC FROM LOGFILE_ST WHERE (YYY = ?) AND (MM = ?) AND( SENDTYPE=?)  AND (PHASE = ?) ORDER BY RUNSNO DESC";

		Object[] args = new Object[] { year, month, sendType,phase};
		JdbcSqlUtils.printSql(sql, args);

		List<Map<String, Object>> rs = this.jdbc.queryForList(sql, args);

		if (rs != null && rs.size() > 0) {
			return (String) rs.get(0).get("RC");
		}
		return null;
	}
}
