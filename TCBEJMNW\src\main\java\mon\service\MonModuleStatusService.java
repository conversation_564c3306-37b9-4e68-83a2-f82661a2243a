package mon.service;

import java.util.List;

import mon.model.MonModuleStatus;

/**
 * <pre>
 * MonModuleStatusService.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public interface MonModuleStatusService {
	public abstract List<MonModuleStatus> doEJCICWebStatus();

	public abstract List<MonModuleStatus> doEJCICDispStatus();

	public abstract List<MonModuleStatus> doEJCICGWStatus();

	public abstract List<MonModuleStatus> doEloanDispStatus();

	public abstract List<MonModuleStatus> doETCHWebStatus();

	public abstract List<MonModuleStatus> doETCHDispStatus();

	public abstract List<MonModuleStatus> doETCHGWStatus();
}
