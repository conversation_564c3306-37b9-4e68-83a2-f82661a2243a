--CREATE BUFFERPOOL EJMN_4KBP SIZE AUTOMATIC PAGESIZE 4096 ;

--CREATE LARGE TABLESPACE EJMNTABSP4K  PAGESIZE 4096 MANAGED BY AUTOMATIC STORAGE BUFFERPOOL EJMN_4KBP;

------------------------------------------------
-- DDL Statements for Table "EJMNAP  "."MON_SYSUSAGE"
------------------------------------------------
 
DROP TABLE IF EXISTS "EJMNAP"."MON_SYSUSAGE";
CREATE TABLE "EJMNAP  "."MON_SYSUSAGE"  (
		  "SYSID" VARCHAR(5) NOT NULL , 
		  "SCHSNO" VARCHAR(16) NOT NULL , 
		  "HOSTID" VARCHAR(8) , 
		  "CPU_AP" DOUBLE , 
		  "CPU_DB" DOUBLE , 
		  "MEM_WEB" DOUBLE , 
		  "MEM_WEB_MAX" DOUBLE , 
		  "MEM_DISP1" DOUBLE , 
		  "MEM_DISP1_MAX" DOUBLE , 
		  "MEM_DISP2" DOUBLE , 
		  "MEM_DISP2_MAX" DOUBLE , 
		  "MEM_GW" DOUBLE , 
		  "MEM_GW_MAX" DOUBLE , 
		  "RUNTS" TIMESTAMP , 
		  "VMSTAT_AP" VARCHAR(1000) , 
		  "VMSTAT_DB" VARCHAR(1000) )   
		 IN "EJMNTABSP4K" ; 


-- DDL Statements for Primary Key on Table "EJMNAP  "."MON_SYSUSAGE"

ALTER TABLE "EJMNAP  "."MON_SYSUSAGE" 
	ADD CONSTRAINT "P_MON_QDEPTH" PRIMARY KEY
		("SYSID",
		 "SCHSNO");


------------------------------------------------
-- DDL Statements for Table "EJMNAP  "."MON_SYSPARAM"
------------------------------------------------
 
DROP TABLE IF EXISTS "EJMNAP"."MON_SYSPARAM";
CREATE TABLE "EJMNAP  "."MON_SYSPARAM"  (
		  "PARAM" VARCHAR(50) NOT NULL , 
		  "PARAMVALUE" VARCHAR(1000) NOT NULL , 
		  "PARAMDESC" VARCHAR(300) , 
		  "MODIFYBY" VARCHAR(10) , 
		  "MODIFYTIME" TIMESTAMP WITH DEFAULT CURRENT TIMESTAMP )   
		 IN "EJMNTABSP4K" ; 


-- DDL Statements for Primary Key on Table "EJMNAP  "."MON_SYSPARAM"

ALTER TABLE "EJMNAP  "."MON_SYSPARAM" 
	ADD CONSTRAINT "P_MON_SYSPARAM" PRIMARY KEY
		("PARAM");


------------------------------------------------
-- DDL Statements for Table "EJMNAP  "."MON_QDEPTH"
------------------------------------------------
 
DROP TABLE IF EXISTS "EJMNAP"."MON_QDEPTH";
CREATE TABLE "EJMNAP  "."MON_QDEPTH"  (
		  "SYSID" VARCHAR(5) NOT NULL , 
		  "SCHSNO" VARCHAR(16) NOT NULL , 
		  "Q01_DEPTH" INTEGER , 
		  "Q02_DEPTH" INTEGER , 
		  "Q03_DEPTH" INTEGER , 
		  "Q04_DEPTH" INTEGER , 
		  "Q05_DEPTH" INTEGER , 
		  "Q06_DEPTH" INTEGER , 
		  "Q07_DEPTH" INTEGER , 
		  "Q08_DEPTH" INTEGER , 
		  "Q09_DEPTH" INTEGER , 
		  "Q10_DEPTH" INTEGER , 
		  "Q11_DEPTH" INTEGER , 
		  "Q12_DEPTH" INTEGER , 
		  "Q13_DEPTH" INTEGER , 
		  "Q14_DEPTH" INTEGER , 
		  "Q15_DEPTH" INTEGER , 
		  "Q16_DEPTH" INTEGER , 
		  "Q17_DEPTH" INTEGER , 
		  "Q18_DEPTH" INTEGER , 
		  "Q19_DEPTH" INTEGER , 
		  "Q20_DEPTH" INTEGER , 
		  "Q21_DEPTH" INTEGER , 
		  "Q22_DEPTH" INTEGER , 
		  "Q23_DEPTH" INTEGER , 
		  "Q24_DEPTH" INTEGER , 
		  "Q25_DEPTH" INTEGER , 
		  "Q26_DEPTH" INTEGER , 
		  "Q27_DEPTH" INTEGER , 
		  "Q28_DEPTH" INTEGER , 
		  "Q29_DEPTH" INTEGER , 
		  "Q30_DEPTH" INTEGER , 
		  "Q31_DEPTH" INTEGER , 
		  "Q32_DEPTH" INTEGER , 
		  "Q33_DEPTH" INTEGER , 
		  "Q34_DEPTH" INTEGER , 
		  "Q35_DEPTH" INTEGER , 
		  "Q36_DEPTH" INTEGER , 
		  "Q37_DEPTH" INTEGER , 
		  "Q38_DEPTH" INTEGER , 
		  "Q39_DEPTH" INTEGER , 
		  "Q40_DEPTH" INTEGER , 
		  "RUNTS" TIMESTAMP )   
		 IN "EJMNTABSP4K" ; 


-- DDL Statements for Primary Key on Table "EJMNAP  "."MON_QDEPTH"

ALTER TABLE "EJMNAP  "."MON_QDEPTH" 
	ADD CONSTRAINT "P_MON_QDEPTH" PRIMARY KEY
		("SYSID",
		 "SCHSNO");
		 
---------------------------------
-- DDL Statements for User Defined Functions
---------------------------------

	 
CREATE OR REPLACE FUNCTION "EJMNAP  "."MONVMSTAT" 
		(
		)
 
		RETURNS  VARCHAR(1000)
		SPECIFIC MONVMSTAT 
		EXTERNAL NAME 'SysInfo!getVmstat()' 
		LANGUAGE JAVA     
		PARAMETER STYLE JAVA     
		VARIANT 
		FENCED THREADSAFE 
		NULL CALL 
		NO SQL 
		NO EXTERNAL ACTION 
		NO SCRATCHPAD 
		NO FINAL CALL 
		DISALLOW PARALLEL 
		NO DBINFO 
		NOT SECURED;
		
-- 這邊缺一個註冊JAR檔的指令，目前尚不知道jar檔在哪跟裡面有什麼