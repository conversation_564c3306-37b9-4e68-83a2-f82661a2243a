package ejcic.exception;

/**
 * <pre>
 * AppException.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class AppException extends RuntimeException {
	private static final long serialVersionUID = 1L;

	protected String errorCode;

	/**
	 *
	 */
	public AppException() {
		super();
	}

	/**
	 * @param message
	 * @param cause
	 */
	public AppException(String message, Throwable cause) {
		super(message, cause);
	}

	/**
	 * @param message
	 */
	public AppException(String message) {
		super(message);
	}

	/**
	 * @param cause
	 */
	public AppException(Throwable cause) {
		super(cause);
	}

	/**
	 * Returns the errorCode
	 * 
	 * @return the errorCode
	 */
	public String getErrorCode() {
		return errorCode;
	}

	/**
	 * Sets the errorCode
	 * 
	 * @param errorCode
	 *            the errorCode to set
	 */
	public AppException setErrorCode(String errorCode) {
		this.errorCode = errorCode;
		return this;
	}

}
