<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:p="http://www.springframework.org/schema/p"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

	<bean id="jcbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate"
		p:dataSource-ref="ejcic-db">
	</bean>

	<bean id="ejcicTxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="ejcic-db" />
	</bean>

	<tx:advice id="ejcicTxAdvice" transaction-manager="ejcicTxManager">
		<tx:attributes>
			<tx:method name="list*" read-only="true" propagation="NOT_SUPPORTED" />
			<tx:method name="find*" read-only="true" propagation="NOT_SUPPORTED" />
			<tx:method name="get*" read-only="true" propagation="NOT_SUPPORTED" />
			<tx:method name="*" timeout="45" rollback-for="Throwable"
				propagation="REQUIRED" />
		</tx:attributes>
	</tx:advice>

	<aop:config proxy-target-class="true">
		<aop:pointcut id="ejcicServiceOperation" expression="execution(* ejcic.dao.*.*(..))" />
		<aop:advisor advice-ref="ejcicTxAdvice" pointcut-ref="ejcicServiceOperation" />
	</aop:config> 
</beans>