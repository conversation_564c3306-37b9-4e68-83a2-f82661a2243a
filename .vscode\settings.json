{"java.jdt.ls.java.home": "C:\\Program Files\\jdk1.6.0_45", "java.configuration.runtimes": [{"name": "JavaSE-1.6", "path": "C:\\Program Files\\jdk1.6.0_45", "default": true}], "terminal.integrated.env.windows": {"JAVA_HOME": "C:\\Program Files\\jdk1.6.0_45", "MAVEN_HOME": "C:\\apache-maven-3.2.5", "JETTY_HOME": "C:\\jetty-distribution-8.1.8.v20121106", "PATH": "C:\\Program Files\\jdk1.6.0_45\\bin;C:\\apache-maven-3.2.5\\bin;${env:PATH}"}, "java.project.referencedLibraries": ["common-resources/lib/**/*.jar"], "java.project.sourcePaths": ["TCBJCICW/src/main/java", "TCBEJMNW/src/main/java", "TCBETCHW/src/main/java"], "java.project.outputPath": "", "java.compile.nullAnalysis.mode": "disabled", "java.configuration.updateBuildConfiguration": "automatic", "java.format.enabled": true, "editor.formatOnSave": true, "files.encoding": "cp950", "files.autoGuessEncoding": true, "java.errors.incompleteClasspath.severity": "ignore", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m", "java.import.maven.enabled": true, "java.maven.downloadSources": false, "java.maven.updateSnapshots": false, "java.completion.importOrder": [], "java.completion.enabled": true, "java.completion.guessMethodArguments": "off", "java.completion.filteredTypes": ["java.awt.*", "com.sun.*", "sun.*", "jdk.*"], "java.codeGeneration.useBlocks": true, "java.signatureHelp.enabled": true, "java.autobuild.enabled": false, "java.server.launchMode": "Standard", "java.inlayHints.parameterNames.enabled": "none", "java.jdt.ls.lombokSupport.enabled": true, "files.exclude": {"**/.factorypath": true}, "java.import.generatesMetadataFilesAtProjectRoot": true, "java.compile.nullAnalysis.nonnull": [], "java.compile.nullAnalysis.nullable": [], "problems.decorations.enabled": false, "java.dependency.syncWithFolderExplorer": true, "java.project.explorer.showNonJavaResources": true}