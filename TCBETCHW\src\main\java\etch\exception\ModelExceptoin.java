package etch.exception;

/**
 * <pre>
 * ModelExceptoin.java
 * </pre>
 *
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,new
 *          </ul>
 */
public class ModelExceptoin extends RuntimeException {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	public ModelExceptoin() {
	}

	/**
	 * @param arg0
	 */
	public ModelExceptoin(String arg0) {
		super(arg0);
	}

	/**
	 * @param arg0
	 */
	public ModelExceptoin(Throwable arg0) {
		super(arg0);
	}

	/**
	 * @param arg0
	 * @param arg1
	 */
	public ModelExceptoin(String arg0, Throwable arg1) {
		super(arg0, arg1);
		// TODO Auto-generated constructor stub
	}

}
