<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
    xmlns:tx="http://www.springframework.org/schema/tx"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <!-- JNDI Datasource -->
    <bean id="ejmn-db" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>jdbc/EJMN</value>
        </property>
    </bean>

    <bean id="ejcic-db" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>jdbc/EJCIC</value>
        </property>
    </bean>

    <bean id="etch-db" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>jdbc/ETCH</value>
        </property>
    </bean>

    <bean id="stj-db" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>jdbc/STJ</value>
        </property>
    </bean>

    <bean id="eloan-db" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>jdbc/ELOAN</value>
        </property>
    </bean>

</beans>