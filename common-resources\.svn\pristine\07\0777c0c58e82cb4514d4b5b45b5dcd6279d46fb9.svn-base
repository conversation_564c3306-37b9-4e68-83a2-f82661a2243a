services:
  mqsrv:
    build:
      context: .
      dockerfile: Dockerfile-tcb
      args:
        QM_NAME: ${QM_NAME}
        PROJECT_NAME: ${COMPOSE_PROJECT_NAME}
    container_name: mq-srv-${COMPOSE_PROJECT_NAME}
    hostname: devhost
    privileged: true
    environment:
      LICENSE: accept
      MQ_QMGR_NAME: ${QM_NAME}
      MQ_ADMIN_PASSWORD: 1qaz2wsx
    ports:
      - "${S_PORT}:1414"
      - "${C_PORT}:9443"
    volumes:
      - mq_data:/mnt/mqm

volumes:
    mq_data:
    