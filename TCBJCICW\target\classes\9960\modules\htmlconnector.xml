<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd">
	
	<bean id="Core" class="com.alfalumia.eternity.core.Core" factory-method="getInstance" />

	<bean id="Package" factory-bean="Core" factory-method="createPackage">
		<!-- Constructor arguments -->
		<constructor-arg value="com.alfalumia.eternity.core.Package" />
		<constructor-arg value="com.alfalumia.eternity.core.connectorserver" />
		<!-- Properties -->
		<property name="domain"><null/></property>
		<property name="modules">
			<map>
				<entry key="htmlConnectorServer" value-ref="HtmlConnectorServer" />
			</map>
		</property>
	</bean>

	<!-- <bean id="MnmRmiConnectorServer" factory-bean="ModuleFactory" factory-method="createModule"> 
		<constructor-arg value="com.alfalumia.eternity.core.core.connectorserver.rmi.MnmRmiConnectorServer" 
		/> <constructor-arg value="MnmRmiConnectorServer" /> <constructor-arg ref="ModuleContext" 
		/> <property name="autoStart" value="true" /> <property name="port" value="9961" 
		/> <property name="autoPortShift" value="4" /> <property name="autoPortShiftTimes" 
		value="1" /> <property name="createRegistry" value="true" /> <property name="bindAddress" 
		value="0.0.0.0" /> </bean> -->

	<bean id="HtmlConnectorServer" factory-bean="Package" factory-method="createModule">
		<!-- Constructor arguments -->
		<constructor-arg value="com.alfalumia.eternity.core.connectorserver.html.HtmlConnectorServer" />
		<constructor-arg value="htmlConnectorServer" />
		<!-- Properties -->
		<property name="autoStart" value="true" />
		<property name="portIncreament" value="2" />
		<property name="autoPortShift" value="4" />
		<property name="autoPortShiftTimes" value="1" />
		<property name="provider" value="MX4J" />
		<property name="bindAddress" value="0.0.0.0" />
		<property name="authorizedUsers">
			<map>
				<entry key="mnmadmin" value="mnmadmin" />
				<entry key="mnmuser" value="mnmuser" />
			</map>
		</property>
	</bean>
	
</beans>