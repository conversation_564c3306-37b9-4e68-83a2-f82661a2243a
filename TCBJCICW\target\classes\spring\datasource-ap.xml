<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:p="http://www.springframework.org/schema/p"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

	<!-- <PERSON><PERSON><PERSON>urce -->

	<bean id="ejcic-db" class="org.apache.commons.dbcp.BasicDataSource">
		<property name="driverClassName" value="${DB_DRIVER}" />
		<property name="url" value="${DB_URL}" />
		<property name="username" value="${DB_USER_ID}" />
		<property name="password" value="${DB_USER_PWD}" />
		<property name="initialSize" value="5" />
		<property name="maxActive" value="${DB_MAX_CONN}" />
	</bean>

</beans>