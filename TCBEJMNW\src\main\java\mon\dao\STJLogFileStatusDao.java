package mon.dao;

import java.util.List;

import mon.model.STJLogFileStatus;

/**
 * <pre>
 * MonSysConfig.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public interface STJLogFileStatusDao {
	public int insertLogFile(STJLogFileStatus model);

	public int insertOrUpdate(STJLogFileStatus model);

	public List<STJLogFileStatus> findByPhase(STJLogFileStatus model);

	public int updateLogFileByYYYMM(STJLogFileStatus model);

	public String queryRCByCondition(String phase, String year, String month, String sendType);

}
