package ejcic.dao.impl;

import java.util.List;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import ejcic.dao.StdTxidAuthDao;
import ejcic.model.StdTxidAuth;

/**
 * <pre>
 * ParaDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("stdTxidAuthDao")
public class StdTxidAuthDaoImpl extends AbstractGenericDao<StdTxidAuth> implements StdTxidAuthDao {

	@Override
	public List<StdTxidAuth> findByTeamNo(String teamNo) {
		final String SQL = "SELECT * FROM StdTxidAuth Where teamNo=? ORDER BY TXID";
		return this.getJdbc().query(SQL, new Object[] { teamNo },
				ParameterizedBeanPropertyRowMapper.newInstance(modelType));
	}

}