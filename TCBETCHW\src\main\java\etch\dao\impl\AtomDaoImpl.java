package etch.dao.impl;

import java.util.List;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import etch.dao.AtomDao;
import etch.model.Atom;

/**
 * <pre>
 * AtomDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("atomDao")
public class AtomDaoImpl extends AbstractGenericDao<Atom> implements AtomDao {
	@Override
	public List<Atom> findByAtomId(final String atomId) {
		final String SQL = "SELECT * FROM ATOM WHERE ATOMID=? ORDER BY COLSEQ";
		return this.getJdbc().query(SQL, new Object[] { atomId },
				ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));
	}

	@Override
	public List<Atom> getMoreAtomListByAtomId(String atomId) {
		final String SQL = "select ColName,DataTYPE,DataLen,Cname,ColSeq from Atom where AtomID=? order by ColSeq";
		return this.getJdbc().query(SQL, new Object[] { atomId.toLowerCase() },
				ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));

	}

	@Override
	public int delAtomList(String atomId) {
		final String SQL = "delete from Atom where AtomID=?";
		return this.getJdbc().update(SQL, new Object[] { atomId.toLowerCase() });

	}

}