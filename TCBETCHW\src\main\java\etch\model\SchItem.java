package etch.model;

import java.sql.Timestamp;

/**
 * <pre>
 * SchItem.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@TModel(tbName = "SCH_ITEM")
public class SchItem extends AbstractModelMeta {
	public static final String RC_RUNNING = "0001";
	public static final String RC_INTERRUPT = "9999";
	public static final String RC_SUCCESS = "0000";
	public static final String RC_INIT = "";

	@TKeyField
	private long detail_Sno;

	@TField
	private long sch_Sno;

	@TField
	private String msgId;

	@TField
	private String txid;

	@TField
	private String productId;

	@TField
	private String querykey1;

	@TField
	private String querykey2;

	@TField
	private String querykey3;

	@TField
	private String querykey4;

	@TField
	private String querykey5;

	@TField
	private String chargeId;

	@TField
	private String rc;

	@TField
	private String requestId;

	@TField
	private String ucName;

	@TField
	private String division;

	@TField
	private String dpName;

	@TField
	private String forceFlag;

	@TField
	private Timestamp begin_Date;

	@TField
	private Timestamp end_Date;

	@TField
	private Timestamp crDate;

	@TField
	private String isDel;

	@TField
	private Timestamp delTime;

	/**
	 * Returns the detail_Sno
	 * 
	 * @return the detail_Sno
	 */
	public long getDetail_Sno() {
		return this.detail_Sno;
	}

	/**
	 * Sets the detail_Sno
	 * 
	 * @param detail_Sno
	 *            the detail_Sno to set
	 */
	public void setDetail_Sno(long detail_Sno) {
		this.detail_Sno = detail_Sno;
	}

	/**
	 * Returns the sch_Sno
	 * 
	 * @return the sch_Sno
	 */
	public long getSch_Sno() {
		return this.sch_Sno;
	}

	/**
	 * Sets the sch_Sno
	 * 
	 * @param sch_Sno
	 *            the sch_Sno to set
	 */
	public void setSch_Sno(long sch_Sno) {
		this.sch_Sno = sch_Sno;
	}

	public String getMsgId() {
		return this.msgId;
	}

	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}

	public String getTxid() {
		return this.txid;
	}

	public void setTxid(String txid) {
		this.txid = txid;
	}

	public String getProductId() {
		return this.productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public String getQuerykey1() {
		return this.querykey1;
	}

	public void setQuerykey1(String querykey1) {
		this.querykey1 = querykey1;
	}

	public String getQuerykey2() {
		return this.querykey2;
	}

	public void setQuerykey2(String querykey2) {
		this.querykey2 = querykey2;
	}

	public String getQuerykey3() {
		return this.querykey3;
	}

	public void setQuerykey3(String querykey3) {
		this.querykey3 = querykey3;
	}

	public String getQuerykey4() {
		return this.querykey4;
	}

	public void setQuerykey4(String querykey4) {
		this.querykey4 = querykey4;
	}

	public String getQuerykey5() {
		return this.querykey5;
	}

	public void setQuerykey5(String querykey5) {
		this.querykey5 = querykey5;
	}

	public String getChargeId() {
		return this.chargeId;
	}

	public void setChargeId(String chargeId) {
		this.chargeId = chargeId;
	}

	public String getRc() {
		return this.rc;
	}

	public void setRc(String rc) {
		this.rc = rc;
	}

	public String getRequestId() {
		return this.requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getUcName() {
		return this.ucName;
	}

	public void setUcName(String ucName) {
		this.ucName = ucName;
	}

	public String getDivision() {
		return this.division;
	}

	public void setDivision(String division) {
		this.division = division;
	}

	public String getDpName() {
		return this.dpName;
	}

	public void setDpName(String dpName) {
		this.dpName = dpName;
	}

	public String getForceFlag() {
		return this.forceFlag;
	}

	public void setForceFlag(String forceFlag) {
		this.forceFlag = forceFlag;
	}

	/**
	 * Returns the begin_Date
	 * 
	 * @return the begin_Date
	 */
	public Timestamp getBegin_Date() {
		return this.begin_Date;
	}

	/**
	 * Sets the begin_Date
	 * 
	 * @param begin_Date
	 *            the begin_Date to set
	 */
	public void setBegin_Date(Timestamp begin_Date) {
		this.begin_Date = begin_Date;
	}

	/**
	 * Returns the end_Date
	 * 
	 * @return the end_Date
	 */
	public Timestamp getEnd_Date() {
		return this.end_Date;
	}

	/**
	 * Sets the end_Date
	 * 
	 * @param end_Date
	 *            the end_Date to set
	 */
	public void setEnd_Date(Timestamp end_Date) {
		this.end_Date = end_Date;
	}

	/**
	 * Returns the crDate
	 * 
	 * @return the crDate
	 */
	public Timestamp getCrDate() {
		return this.crDate;
	}

	/**
	 * Sets the crDate
	 * 
	 * @param crDate
	 *            the crDate to set
	 */
	public void setCrDate(Timestamp crDate) {
		this.crDate = crDate;
	}

	public String getIsDel() {
		return this.isDel;
	}

	public void setIsDel(String isDel) {
		this.isDel = isDel;
	}

	/**
	 * Returns the delTime
	 * 
	 * @return the delTime
	 */
	public Timestamp getDelTime() {
		return this.delTime;
	}

	/**
	 * Sets the delTime
	 * 
	 * @param delTime
	 *            the delTime to set
	 */
	public void setDelTime(Timestamp delTime) {
		this.delTime = delTime;
	}

}
