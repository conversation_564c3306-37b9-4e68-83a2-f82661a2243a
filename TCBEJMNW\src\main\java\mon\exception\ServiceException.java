package mon.exception;

/**
 * <pre> ServiceException.java </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul> <li>2003/06/30,PGU,NEW </ul>
 */
public class ServiceException extends RuntimeException {
	private static final long serialVersionUID = 1L;

	protected String errorCode;

	/**
	 *
	 */
	public ServiceException() {
		super();
	}

	/**
	 * @param message
	 * @param cause
	 */
	public ServiceException(String message, Throwable cause) {
		super(message, cause);
	}

	/**
	 * @param message
	 */
	public ServiceException(String message) {
		super(message);
	}

	/**
	 * @param cause
	 */
	public ServiceException(Throwable cause) {
		super(cause);
	}

	/**
	 * Returns the errorCode
	 * 
	 * @return the errorCode
	 */
	public String getErrorCode() {
		return this.errorCode;
	}

	/**
	 * Sets the errorCode
	 * 
	 * @param errorCode
	 *            the errorCode to set
	 */
	public ServiceException setErrorCode(String errorCode) {
		this.errorCode = errorCode;
		return this;
	}

}
