package ejcic.model;

/**
 * <pre>
 * BaseModelObj.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class ModelMetaData {

	public String[] insertArgsName;

	public String[] keyArgsName;

	public String[] updateByKeyArgsName;

	public String insertSql;

	public String updateByKeySql;

	public String deleteByKeySql;

	public String findAllSql;

	public String findByKeySql;

	/**
	 * Returns the insertArgsName
	 * 
	 * @return the insertArgsName
	 */
	public String[] getInsertArgsName() {
		return this.insertArgsName;
	}

	/**
	 * Sets the insertArgsName
	 * 
	 * @param insertArgsName
	 *            the insertArgsName to set
	 */
	public void setInsertArgsName(String[] insertArgsName) {
		this.insertArgsName = insertArgsName;
	}

	/**
	 * Returns the keyArgsName
	 * 
	 * @return the keyArgsName
	 */
	public String[] getKeyArgsName() {
		return this.keyArgsName;
	}

	/**
	 * Sets the keyArgsName
	 * 
	 * @param keyArgsName
	 *            the keyArgsName to set
	 */
	public void setKeyArgsName(String[] keyArgsName) {
		this.keyArgsName = keyArgsName;
	}

	/**
	 * Returns the updateByKeyArgsName
	 * 
	 * @return the updateByKeyArgsName
	 */
	public String[] getUpdateByKeyArgsName() {
		return this.updateByKeyArgsName;
	}

	/**
	 * Sets the updateByKeyArgsName
	 * 
	 * @param updateByKeyArgsName
	 *            the updateByKeyArgsName to set
	 */
	public void setUpdateByKeyArgsName(String[] updateByKeyArgsName) {
		this.updateByKeyArgsName = updateByKeyArgsName;
	}

	/**
	 * Returns the insertSql
	 * 
	 * @return the insertSql
	 */
	public String getInsertSql() {
		return this.insertSql;
	}

	/**
	 * Sets the insertSql
	 * 
	 * @param insertSql
	 *            the insertSql to set
	 */
	public void setInsertSql(String insertSql) {
		this.insertSql = insertSql;
	}

	/**
	 * Returns the updateByKeySql
	 * 
	 * @return the updateByKeySql
	 */
	public String getUpdateByKeySql() {
		return this.updateByKeySql;
	}

	/**
	 * Sets the updateByKeySql
	 * 
	 * @param updateByKeySql
	 *            the updateByKeySql to set
	 */
	public void setUpdateByKeySql(String updateByKeySql) {
		this.updateByKeySql = updateByKeySql;
	}

	/**
	 * Returns the deleteByKeySql
	 * 
	 * @return the deleteByKeySql
	 */
	public String getDeleteByKeySql() {
		return this.deleteByKeySql;
	}

	/**
	 * Sets the deleteByKeySql
	 * 
	 * @param deleteByKeySql
	 *            the deleteByKeySql to set
	 */
	public void setDeleteByKeySql(String deleteByKeySql) {
		this.deleteByKeySql = deleteByKeySql;
	}

	/**
	 * Returns the findAllSql
	 * 
	 * @return the findAllSql
	 */
	public String getFindAllSql() {
		return this.findAllSql;
	}

	/**
	 * Sets the findAllSql
	 * 
	 * @param findAllSql
	 *            the findAllSql to set
	 */
	public void setFindAllSql(String findAllSql) {
		this.findAllSql = findAllSql;
	}

	/**
	 * Returns the findByKeySql
	 * 
	 * @return the findByKeySql
	 */
	public String getFindByKeySql() {
		return this.findByKeySql;
	}

	/**
	 * Sets the findByKeySql
	 * 
	 * @param findByKeySql
	 *            the findByKeySql to set
	 */
	public void setFindByKeySql(String findByKeySql) {
		this.findByKeySql = findByKeySql;
	}

}
