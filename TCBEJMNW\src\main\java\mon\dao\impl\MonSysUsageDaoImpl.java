package mon.dao.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import mon.dao.MonSysUsageDao;
import mon.model.MonSysUsage;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * <pre>
 * MonSysUsageDaoImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Repository("monSysUsageDao")
public class MonSysUsageDaoImpl extends AbstractGenericDao<MonSysUsage> implements MonSysUsageDao {

	@Override
	public List<MonSysUsage> findSysUseageByRunts(Timestamp startDate, Timestamp endDate, String sysId) {
		String sql = " select * from MON_SYSUSAGE where RUNTS >= ? and RUNTS <= ? and SYSID = ? order by RUNTS DESC";
		return this.getJdbc().query(sql, null, new Object[] { startDate, endDate, sysId }, 0, 500,
				ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));

	}

	@Override
	public int updateSysUseage(String sysId, String schNo, int updateTtype, int cupV, int memV, int memMax,
			String vmStat) {

		String sql = " update MON_SYSUSAGE ";
		List<Object> args = new ArrayList<Object>();
		switch (updateTtype) {
		case 1:// disp
			sql += " SET CPU_AP=? ,MEM_DISP1=?,MEM_DISP1_MAX=?,VMSTAT_AP=? ";
			args.add(cupV);
			args.add(memV);
			args.add(memMax);
			args.add(vmStat);
			break;
		case 2:// eloan
			sql += " SET MEM_DISP1=?,MEM_DISP1_MAX=? ";
			args.add(memV);
			args.add(memMax);
			break;
		case 3:// web
			sql += " SET MEM_WEB=?,MEM_WEB_MAX=?  ";
			args.add(memV);
			args.add(memMax);
			break;
		case 4:// gw
			sql += " SET MEM_GW=?,MEM_GW_MAX=? ";
			args.add(memV);
			args.add(memMax);
			break;
		case 5:// db
			sql += " SET CPU_DB=?,VMSTAT_DB=? ";
			args.add(cupV);
			args.add(vmStat);
			break;
		default:
			sql += "";
			break;
		}
		sql += " WHERE SYSID=? and SCHSNO=?";
		args.add(sysId);
		args.add(schNo);

		return this.getJdbc().update(sql, args.toArray());
	}

	@Override
	public String getVmstat() {
		String sql = " select EJMNAP.MONVMSTAT() from SYSIBM.SYSDUMMY1";
		List<String> rs = this.getJdbc().query(sql, new Object[] {},
				ParameterizedBeanPropertyRowMapper.newInstance(String.class));
		if (rs.size() > 0) {
			return rs.get(0);
		}
		return null;
	}
}
