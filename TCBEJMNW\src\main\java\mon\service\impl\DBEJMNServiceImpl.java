package mon.service.impl;

import java.net.InetSocketAddress;
import java.nio.charset.Charset;

import javax.annotation.Resource;

import mon.exception.ServiceException;
import mon.service.DBEJMNService;
import mon.service.SysParamConst;
import mon.service.SysParamService;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.mina.core.future.ConnectFuture;
import org.apache.mina.core.service.IoConnector;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IoSession;
import org.apache.mina.filter.codec.ProtocolCodecFilter;
import org.apache.mina.filter.codec.textline.TextLineCodecFactory;
import org.apache.mina.filter.logging.LoggingFilter;
import org.apache.mina.transport.socket.nio.NioSocketConnector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service("DBEJMNService")
public class DBEJMNServiceImpl implements DBEJMNService {
	private static final Logger logger = LoggerFactory.getLogger(DBEJMNServiceImpl.class);

	@Resource
	SysParamService sysParamService;

	@Override
	public String doService(String cmd) {
		String serverIp = this.sysParamService.getValue(SysParamConst.DB_EJMN_AGENT_IP);
		if (StringUtils.isEmpty(serverIp)) {
			serverIp = "127.0.0.1";
		}
		int serverPort = NumberUtils.toInt(this.sysParamService.getValue(SysParamConst.DB_EJMN_AGENT_PORT), 9999);

		logger.debug("[doService]serverIp={},port={}", serverIp, serverPort);

		IoConnector connector = new NioSocketConnector();

		connector.getFilterChain().addLast("logger", new LoggingFilter());
		connector.getFilterChain().addLast("codec",
				new ProtocolCodecFilter(new TextLineCodecFactory(Charset.forName("UTF-8"))));
		connector.setConnectTimeoutMillis(5000);

		IoHandlerAdapter handler = new DBEJMNClientHandler(cmd);
		connector.setHandler(handler);

		ConnectFuture future = connector.connect(new InetSocketAddress(serverIp, serverPort));
		future.awaitUninterruptibly();

		if (!future.isConnected()) {
			logger.warn("[doService]can't connect to agent!!");
			throw new ServiceException("can't connect to agent!!");
		}
		IoSession session = future.getSession();
		session.getConfig().setUseReadOperation(true);
		session.getCloseFuture().awaitUninterruptibly();
		connector.dispose();

		return ((DBEJMNClientHandler) handler).getResult();
	}
}
