package mon.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import mon.dao.MonQDepthDao;
import mon.model.MonQDepth;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * <pre>
 * MonQDepthDaoImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Repository("monQDepthDao")
public class MonQDepthDaoImpl extends AbstractGenericDao<MonQDepth> implements MonQDepthDao {
	@Override
	public List<MonQDepth> findQDepthByRunts(Timestamp startDate, Timestamp endDate, String sysId) {
		String sql = " select * from MON_QDEPTH where RUNTS >= ? and RUNTS <= ? and SysId = ? ORDER BY RUNTS DESC,SCHSNO DESC";
		return this.getJdbc().query(sql, null, new Object[] { startDate, endDate, sysId }, 0, 500,
				ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));
	}

}
