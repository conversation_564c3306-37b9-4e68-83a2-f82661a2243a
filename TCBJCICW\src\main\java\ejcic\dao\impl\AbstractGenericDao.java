package ejcic.dao.impl;

import java.lang.reflect.ParameterizedType;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;

import ejcic.dao.GenericDao;
import ejcic.exception.DaoException;
import ejcic.jdbc.EJCICJdbcTemplate;
import ejcic.model.ModelMeta;

/**
 * <pre>
 * AbstractEJCICJdbc.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,new
 *          </ul>
 */
public abstract class AbstractGenericDao<T> implements GenericDao<T> {

	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	private EJCICJdbcTemplate jdbc;

	protected Class<T> modelType;

	// @Resource(name = "ejcic-db")
	// public void setDataSource(DataSource dataSource) {
	// jdbc = new EJCICJdbcTemplate(dataSource);
	// }

	@SuppressWarnings("unchecked")
	public AbstractGenericDao() {
		try {
			this.modelType = (Class<T>) ((ParameterizedType) this.getClass().getGenericSuperclass())
					.getActualTypeArguments()[0];
		} catch (ClassCastException e) {
			Class<T> clazz = (Class<T>) this.getClass().getGenericSuperclass();
			this.modelType = (Class<T>) ((ParameterizedType) clazz.getGenericSuperclass()).getActualTypeArguments()[0];
		}
	}

	@Override
	public EJCICJdbcTemplate getJdbc() {
		return this.jdbc;
	}

	@Override
	public int insert(T model) {
		try {
			ModelMeta baseModel = (ModelMeta) model;
			return this.jdbc.update(baseModel.getInsertSql(), baseModel.toInsertArgs());
		} catch (Exception ex) {
			throw new DaoException(ex).setModelClass(model.getClass()).setAction("insert");
		}
	}

	@Override
	public int update(T model) {
		try {
			ModelMeta baseModel = (ModelMeta) model;
			return this.jdbc.update(baseModel.getUpdateByKeySql(), baseModel.toUpdateByKeyArgs());
		} catch (Exception ex) {
			throw new DaoException(ex).setModelClass(model.getClass()).setAction("update");
		}
	}

	@Override
	public int delete(T model) {
		try {
			ModelMeta baseModel = (ModelMeta) model;
			return this.jdbc.update(baseModel.getDeleteByKeySql(), baseModel.toKeyArgs());
		} catch (Exception ex) {
			throw new DaoException(ex).setModelClass(model.getClass()).setAction("delete");
		}
	}

	@Override
	public List<T> findByAll() {
		ModelMeta baseModel = null;
		try {
			baseModel = (ModelMeta) this.modelType.newInstance();
			return this.getJdbc().query(baseModel.getFindAllSql(), null,
					ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));
		} catch (Exception ex) {
			throw new DaoException(ex).setModelClass(baseModel != null ? baseModel.getClass() : null).setAction(
					"findByAll");
		}
	}

	@Override
	public T findByKey(T model) {
		try {
			ModelMeta baseModel = (ModelMeta) model;
			List<T> result = this.getJdbc().query(baseModel.getFindByKeySql(), baseModel.toKeyArgs(),
					ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));
			return (result.size() > 0) ? result.get(0) : null;
		} catch (Exception ex) {
			throw new DaoException(ex).setModelClass(model.getClass()).setAction("findByKey");
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see ejcic.dao.BaseDao#delete(java.lang.String[])
	 */
	@Override
	public int delete(String... cols) {
		ModelMeta baseModel = null;
		try {
			baseModel = (ModelMeta) this.modelType.newInstance();
			return this.jdbc.update(baseModel.getDeleteByKeySql(), cols);
		} catch (Exception ex) {
			throw new DaoException(ex).setModelClass(baseModel != null ? baseModel.getClass() : null).setAction(
					"delete");
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see ejcic.dao.BaseDao#findByKey(java.lang.String[])
	 */
	@Override
	public T findByKey(String... cols) {
		ModelMeta baseModel = null;
		try {
			baseModel = (ModelMeta) this.modelType.newInstance();
			List<T> result = this.getJdbc().query(baseModel.getFindByKeySql(), cols,
					ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));
			return (result.size() > 0) ? result.get(0) : null;
		} catch (Exception ex) {
			throw new DaoException(ex).setModelClass(baseModel != null ? baseModel.getClass() : null).setAction(
					"findByKey");
		}
	}

}
