# TCBJCICW 專案詳細規格文件

## 專案概述

TCBJCICW (Taiwan Cooperative Bank - Joint Credit Information Center Web) 是聯合徵信中心交易系統的Web前端專案，主要負責處理與聯合徵信中心相關的查詢和交易業務。

### 主要功能
- **標準查詢**: 個人/企業信用報告查詢
- **組合查詢 (CPX)**: 多項查詢組合處理
- **授權管理**: 使用者權限和主管授權控制
- **系統管理**: 參數設定和系統維護

## 技術架構

### 技術棧
- **JDK**: 1.6
- **Web框架**: Spring MVC 2.x
- **建置工具**: Maven 3.2.5
- **Web容器**: Jetty 8.1.8
- **資料庫**: IBM DB2
- **訊息佇列**: IBM MQ
- **前端**: JSP + JavaScript + CSS

### 專案結構

```
TCBJCICW/
├── src/main/java/ejcic/
│   ├── cpx/                    # 組合查詢模組
│   ├── dao/                    # 資料存取層
│   ├── disp/                   # 分發器模組
│   ├── exception/              # 例外處理
│   ├── fortify/                # 安全強化
│   ├── gw/                     # 閘道模組
│   ├── jdbc/                   # JDBC工具
│   ├── ma/                     # 管理功能
│   ├── model/                  # 資料模型
│   ├── module/                 # 模組管理
│   ├── msg/                    # 訊息處理
│   ├── service/                # 服務層
│   ├── utils/                  # 工具類別
│   └── web/                    # Web層
│       ├── constant/           # 常數定義
│       ├── core/               # 核心框架
│       ├── filter/             # 過濾器
│       ├── handler/            # 請求處理器
│       ├── model/              # Web模型
│       ├── render/             # 渲染器
│       ├── service/            # Web服務
│       └── utils/              # Web工具
├── src/main/resources/
│   ├── spring/                 # Spring配置
│   ├── ejcic.properties        # 主配置檔
│   └── etc/                    # 其他配置
└── WebContent/                 # Web資源
    ├── WEB-INF/
    ├── jsp/                    # JSP頁面
    ├── js/                     # JavaScript
    └── css/                    # 樣式表
```

## 核心架構設計

### Web層架構

```mermaid
classDiagram
    class DispatchServlet {
        +void doGet(HttpServletRequest, HttpServletResponse)
        +void doPost(HttpServletRequest, HttpServletResponse)
        +void processRequest(HttpServletRequest, HttpServletResponse)
        -Handler getHandler(String)
    }
    
    class AbstractHandler {
        <<abstract>>
        +WebView action(WebContext, Map)
        +String getErrorBackUrl()
        #Logger logger
    }
    
    class WebContext {
        +HttpServletRequest request
        +HttpServletResponse response
        +WebUserProfile userProfile
        +String userId
        +String custId
        +Map parameters
    }
    
    class WebView {
        +String viewName
        +Map model
        +boolean isRedirect
        +boolean isJson
        +String jsonOutput
        +static WebView jsp(String)
        +static WebView redirect(String)
        +static WebView json()
    }
    
    DispatchServlet --> AbstractHandler
    AbstractHandler --> WebContext
    AbstractHandler --> WebView
```

### Handler模式實作

系統使用Handler模式處理不同的業務請求，每個Handler負責特定的業務功能：

#### 主要Handler類別
- **JCICRR01Handler**: 個人信用報告查詢
- **JCICRR02Handler**: 企業信用報告查詢
- **JCICRR03Handler**: 組合查詢處理
- **JCICMA01Handler**: 授權管理
- **JCICCS01Handler**: 系統參數設定

#### Handler實作範例
```java
@Controller
public class JCICRR01Handler extends AbstractHandler {
    
    @Resource
    private TxidService txidService;
    
    @Override
    public WebView action(WebContext context, Map<String, String> paramMap) 
            throws AppException {
        
        // 1. 參數驗證
        String custId = paramMap.get("custId");
        String idNo = paramMap.get("idNo");
        
        // 2. 授權檢查
        if (!txidService.checkAuth("RR01", custId, context.getUserId())) {
            throw new AppException("授權不足");
        }
        
        // 3. 業務處理
        Result result = txidService.processTransaction("RR01", paramMap);
        
        // 4. 回傳結果
        return WebView.jsp("jcic/rr01_result")
                     .addModel("result", result);
    }
}
```

### 服務層架構

```mermaid
classDiagram
    class TxidService {
        <<interface>>
        +boolean checkTxid(String txid, String custId)
        +boolean checkAuth(String txid, String custId, String userId)
        +Result processTransaction(String txid, Map params)
    }
    
    class CPXTxidCheckService {
        <<interface>>
        +boolean checkCpxTxid(String txid, String custId)
        +Result processCpx(String txid, Map params)
        +void loadCpxProducts()
    }
    
    class MQService {
        <<interface>>
        +void sendMessage(String queueName, Message msg)
        +Message receiveMessage(String queueName)
        +boolean isConnected()
    }
    
    class AuthService {
        <<interface>>
        +boolean hasAuth(String userId, String txid, String custId)
        +boolean isSupervisor(String userId, String txid, String custId)
        +void grantAuth(String userId, String txid, String custId)
    }
    
    TxidService --> MQService
    TxidService --> AuthService
    CPXTxidCheckService --> TxidService
```

### 資料存取層架構

```mermaid
classDiagram
    class GenericDao~T~ {
        <<interface>>
        +T findById(Object id)
        +List~T~ findAll()
        +List~T~ findByExample(T example)
        +void save(T entity)
        +void update(T entity)
        +void delete(T entity)
        +List~T~ findBySQL(String sql, Object[] params)
    }
    
    class AbstractModelMeta {
        <<abstract>>
        +String getTableName()
        +String[] getKeyFields()
        +Map~String,String~ getFieldMap()
        +Object getFieldValue(String fieldName)
        +void setFieldValue(String fieldName, Object value)
    }
    
    class JdbcGenericDao~T~ {
        -JdbcTemplate jdbcTemplate
        +T findById(Object id)
        +List~T~ findAll()
        +List~T~ findByExample(T example)
        +void save(T entity)
        +void update(T entity)
        +void delete(T entity)
        -String buildSelectSQL(T example)
        -String buildInsertSQL(T entity)
        -String buildUpdateSQL(T entity)
    }
    
    GenericDao <|-- JdbcGenericDao
    JdbcGenericDao --> AbstractModelMeta
```

#### 自定義ORM註解
```java
@TModel(tableName = "STD_TXID_PROFILE")
public class StdTxidProfile extends AbstractModelMeta {
    
    @TKeyField
    @TField(columnName = "TXID")
    private String txid;
    
    @TField(columnName = "TXID_NAME")
    private String txidName;
    
    @TField(columnName = "NEED_AUTH")
    private String needAuth;
    
    // getters and setters...
}
```

## 主要業務模組

### 1. 標準查詢模組

#### 查詢類型
- **RR01**: 個人信用報告
- **RR02**: 企業信用報告  
- **RR03**: 個人信用評分
- **RR04**: 企業信用評分
- **RR05**: 授信餘額查詢
- **RR06**: 保證人查詢

#### 查詢流程
```mermaid
sequenceDiagram
    participant User as 使用者
    participant Handler as Handler
    participant TxidService as TxidService
    participant MQ as MQService
    participant JCIC as 聯徵中心
    
    User->>Handler: 提交查詢請求
    Handler->>TxidService: 檢查TXID授權
    TxidService-->>Handler: 授權通過
    Handler->>TxidService: 處理交易
    TxidService->>MQ: 發送MQ訊息
    MQ->>JCIC: 轉發至聯徵中心
    JCIC-->>MQ: 回傳查詢結果
    MQ-->>TxidService: 接收回應
    TxidService-->>Handler: 回傳處理結果
    Handler-->>User: 顯示查詢結果
```

### 2. 組合查詢模組 (CPX)

#### CPX產品類型
- **CPX001**: 個人綜合查詢
- **CPX002**: 企業綜合查詢
- **CPX003**: 授信風險評估
- **CPX004**: 保證人風險評估

#### CPX處理架構
```mermaid
classDiagram
    class CpxProductLoader {
        +void loadProducts()
        +Proc getProc(String txid)
        +WarnHandler getWarnHandler(String txid)
        +Map~String,CpxProduct~ products
    }
    
    class CpxProduct {
        +String txid
        +String productName
        +List~String~ subTxids
        +Proc processor
        +WarnHandler warnHandler
    }
    
    class Proc {
        <<interface>>
        +Result process(Map params)
    }
    
    class WarnHandler {
        <<interface>>
        +void handleWarning(Result result)
    }
    
    CpxProductLoader --> CpxProduct
    CpxProduct --> Proc
    CpxProduct --> WarnHandler
```

### 3. 授權管理模組

#### 授權層級
1. **使用者授權**: 基本交易權限
2. **客戶授權**: 特定客戶的查詢權限
3. **主管授權**: 高風險交易的二次授權
4. **時間授權**: 授權有效期限控制

#### 授權檢查流程
```mermaid
flowchart TD
    A[開始授權檢查] --> B{檢查使用者權限}
    B -->|無權限| C[拒絕存取]
    B -->|有權限| D{檢查客戶授權}
    D -->|無授權| C
    D -->|有授權| E{檢查時間限制}
    E -->|已過期| C
    E -->|有效| F{需要主管授權?}
    F -->|是| G{檢查主管授權}
    F -->|否| H[授權通過]
    G -->|無授權| C
    G -->|有授權| H
```

## 資料模型設計

### 核心資料表

#### 交易設定檔
```sql
CREATE TABLE STD_TXID_PROFILE (
    TXID VARCHAR(10) NOT NULL PRIMARY KEY,
    TXID_NAME VARCHAR(100),
    TXID_DESC VARCHAR(200),
    TXID_TYPE VARCHAR(10),
    AUTH_TYPE VARCHAR(10),
    NEED_AUTH CHAR(1),
    NEED_CHARGE CHAR(1),
    NEED_SUPERVISOR CHAR(1),
    NEED_CUSTID CHAR(1),
    STATUS CHAR(1)
);
```

#### 使用者授權
```sql
CREATE TABLE SUPERVISOR_AUTH (
    USER_ID VARCHAR(20) NOT NULL,
    CUST_ID VARCHAR(20) NOT NULL,
    TXID VARCHAR(10) NOT NULL,
    AUTH_TYPE VARCHAR(10),
    START_DATE DATE,
    END_DATE DATE,
    STATUS CHAR(1),
    CREATE_USER VARCHAR(20),
    CREATE_DATE TIMESTAMP,
    PRIMARY KEY (USER_ID, CUST_ID, TXID)
);
```

#### 交易日誌
```sql
CREATE TABLE EJCIC_LOG_FILE (
    DATE_YYYYMMDD DATE NOT NULL,
    TXID VARCHAR(10) NOT NULL,
    MSGID VARCHAR(50) NOT NULL,
    PRODUCTID VARCHAR(20),
    REQUESTID VARCHAR(50),
    DIVISION VARCHAR(10),
    QUERYKEY1 VARCHAR(50),
    QUERYKEY2 VARCHAR(50),
    ISPRINT CHAR(1),
    RC VARCHAR(10),
    TOJCIC CHAR(1),
    CREATE_TIME TIMESTAMP,
    PRIMARY KEY (DATE_YYYYMMDD, TXID, MSGID)
);
```

### 資料模型類別

#### 基礎模型類別
```java
@TModel(tableName = "STD_TXID_PROFILE")
public class StdTxidProfile extends AbstractModelMeta {
    
    @TKeyField
    @TField(columnName = "TXID")
    private String txid;
    
    @TField(columnName = "TXID_NAME")
    private String txidName;
    
    @TField(columnName = "NEED_AUTH")
    private String needAuth;
    
    @TField(columnName = "NEED_SUPERVISOR")
    private String needSupervisor;
    
    // 業務邏輯方法
    public boolean isNeedAuth() {
        return "Y".equals(needAuth);
    }
    
    public boolean isNeedSupervisor() {
        return "Y".equals(needSupervisor);
    }
}
```

## MQ訊息處理

### MQ配置
```properties
# MQ Server設定
MQ_SERVER=127.0.0.1
MQ_PORT=1414
MQ_MGR=MQJ006D

# 佇列設定
MQ_JCIC_REQ=MQJ006D.JCIC.REQ.QL
MQ_JCIC_RESP=MQJ006D.JCIC.RESP.QL
MQ_MONITOR=MQJ006D.MONITOR.QL
```

### 訊息格式
```xml
<JCICMessage>
    <Header>
        <MessageId>MSG_20231201_001</MessageId>
        <Timestamp>2023-12-01T10:30:00</Timestamp>
        <TxId>RR01</TxId>
        <UserId>USER001</UserId>
        <CustId>CUST001</CustId>
    </Header>
    <Body>
        <QueryData>
            <IdNo>A123456789</IdNo>
            <QueryType>PERSONAL</QueryType>
        </QueryData>
    </Body>
</JCICMessage>
```

### MQ服務實作
```java
@Service
public class MQServiceImpl implements MQService {
    
    @Resource
    private QueueConnectionFactory queueConnectionFactory;
    
    @Override
    public void sendMessage(String queueName, Message message) {
        try {
            QueueConnection connection = queueConnectionFactory.createQueueConnection();
            QueueSession session = connection.createQueueSession(false, Session.AUTO_ACKNOWLEDGE);
            Queue queue = session.createQueue(queueName);
            QueueSender sender = session.createSender(queue);
            
            TextMessage textMessage = session.createTextMessage(message.toXml());
            sender.send(textMessage);
            
            connection.close();
        } catch (JMSException e) {
            throw new ServiceException("MQ發送失敗", e);
        }
    }
}
```

## 安全機制

### 認證機制
1. **Notes認證**: 透過Lotus Notes系統驗證使用者身份
2. **Session管理**: 維護使用者登入狀態
3. **逾時控制**: 自動登出機制

### 授權控制
1. **角色基礎**: 基於使用者角色的權限控制
2. **功能授權**: 特定功能的存取權限
3. **資料授權**: 特定客戶資料的存取權限
4. **時間控制**: 授權有效期限

### 資料保護
1. **敏感資料遮罩**: 顯示時部分遮蔽
2. **加密傳輸**: MQ訊息加密
3. **稽核記錄**: 完整的操作記錄

## 配置管理

### Spring配置結構
```
spring/
├── applicationContext-web.xml      # 主配置檔
├── datasource-jndi.xml            # 資料源配置
├── jdbc.xml                       # JDBC配置
├── services-base.xml              # 基礎服務
└── services-web.xml               # Web服務
```

### 主要配置項目
```xml
<!-- 資料源配置 -->
<jee:jndi-lookup id="dataSource" jndi-name="java:comp/env/jdbc/EJCIC"/>

<!-- MQ連線工廠 -->
<jee:jndi-lookup id="appWebQCF" jndi-name="java:comp/env/jms/QCF"/>

<!-- 屬性檔載入 -->
<util:properties id="ejcicProperties" location="classpath:ejcic.properties"/>

<!-- 元件掃描 -->
<context:component-scan base-package="ejcic.web.handler"/>
<context:component-scan base-package="ejcic.web.service.impl"/>
```

## 部署架構

### Web容器配置
- **容器**: Jetty 8.1.8
- **JVM參數**: -Xms512m -Xmx1024m -XX:PermSize=256m
- **編碼**: UTF-8
- **Session逾時**: 30分鐘

### 資料庫連線
- **連線池**: JNDI DataSource
- **最大連線數**: 50
- **連線逾時**: 10秒
- **Schema**: EJCICAP1

### MQ連線
- **Queue Manager**: MQJ006D
- **連線方式**: TCP/IP
- **埠號**: 1414
- **通道**: SYSTEM.DEF.SVRCONN
