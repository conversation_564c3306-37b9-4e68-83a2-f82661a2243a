<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    	   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 監控系統Alive Check排程 BEGIN -->
	<bean id="aliveCheckJob" class="com.mega.eloan.common.schedule.AliveCheckJob" />

	<bean id="aliveCheckJobDetail"
		class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
		<property name="targetObject" ref="aliveCheckJob" />
		<property name="targetMethod" value="check" />
	</bean>

	<bean id="aliveCheckTrigger" class="org.springframework.scheduling.quartz.SimpleTriggerBean">
		<property name="jobDetail" ref="aliveCheckJobDetail" />
		<property name="startDelay" value="180000" />
		<property name="repeatInterval" value="600000" />
	</bean>
	<!-- 監控系統Alive Check排程 END -->


	<bean class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
		<property name="quartzProperties">
			<props>
				<prop key="org.quartz.threadPool.class">org.quartz.simpl.SimpleThreadPool</prop>
				<prop key="org.quartz.threadPool.threadCount">2</prop>
			</props>
		</property>
		<property name="waitForJobsToCompleteOnShutdown" value="false" />
		<property name="triggers">
			<list>
				<ref bean="aliveCheckTrigger" />
			</list>
		</property>
	</bean>

</beans>