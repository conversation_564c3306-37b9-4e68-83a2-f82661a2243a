# TCBJCICW 專案中 FORCEFLAG 和 TOJCIC 變數關係分析

## 概述

本文檔分析 TCBJCICW 專案中 `LOGFILE.FORCEFLAG` 和 `TOJCIC` 這兩個變數的定義、使用方式和它們之間的關係。這兩個變數在系統中扮演著重要的角色，決定系統是否向聯徵中心發送查詢請求或直接從資料庫取得資料。

## 變數定義

### LOGFILE.FORCEFLAG

`FORCEFLAG` 是 `LogFile` 類別中的一個屬性，定義在 `TCBJCICW\src\main\java\ejcic\model\LogFile.java` 中：

```java
// FORCEFLAG 查詢方式 CHAR(1)
@TField
private String forceflag;
```

`FORCEFLAG` 可能的值：
- `"A"`: 自動（系統自行判斷）
- `"Y"`: 強迫至聯徵（強制向聯徵中心發送查詢）
- `"N"`: 資料庫查詢（直接從資料庫取得資料）

### TOJCIC

`TOJCIC` 是 `LogFile` 類別中的另一個屬性，同樣定義在 `TCBJCICW\src\main\java\ejcic\model\LogFile.java` 中：

```java
// TOJCIC 是否有送聯徵中心查詢 CHAR(1)
@TField
private String tojcic;

public static final String TOJCIC_YES = "Y";
public static final String TOJCIC_NO = "N";
```

`TOJCIC` 可能的值：
- `"Y"`: 是（已向聯徵中心發送查詢）
- `"N"`: 否（未向聯徵中心發送查詢）

## 資料流程分析

根據程式碼分析，`FORCEFLAG` 和 `TOJCIC` 之間的資料流程如下：

1. 前端傳入 `force2Jcic` 參數，設定到 `LogFile` 物件的 `forceflag` 屬性中
2. 系統根據 `forceflag` 和系統參數決定是否向聯徵中心發送查詢
3. 查詢執行後，系統將實際的查詢來源（是否向聯徵中心查詢）記錄到 `AUDITLOG` 表的 `TOJCIC` 欄位中
4. 系統從 `AUDITLOG` 表中讀取 `TOJCIC` 值，設定到返回給前端的 `JCICST00VO` 物件中

這個流程可以用以下步驟表示：

```
前端 force2Jcic 參數 → LOGFILE.FORCEFLAG → 查詢執行 → AUDITLOG.TOJCIC → 回傳給前端
```

## 關鍵程式碼分析

### 1. 前端參數處理

在 `JCICCS01Handler.java` 中，前端傳入的 `force2Jcic` 參數被設置到 `LogFile` 物件的 `forceflag` 屬性中：

```java
// 取得其他參數
String force2Jcic = StringUtils.trimToEmpty(paramMap.get("force2Jcic"));
// ...
LogFile logfile = new LogFile();
// ...
logfile.setForceflag(force2Jcic);
```

### 2. 強制查詢邏輯

在 `JCICST00Service.java` 的 `getW2DStdMessage` 方法中（第 620-655 行），有一段處理是否強制向聯徵查詢的邏輯：

```java
// 判斷查詢項目是否強制向聯徵查詢
String forceFlag = logfile.getForceflag();
String[] forceToJcic = StringUtils.split(StringUtils.trimToEmpty(this.sysParamService.getProperty(SysParamConst.STDTXID_FORCE_TOJCIC)), ",");
if (ArrayUtils.contains(forceToJcic, logfile.getTxid())) {
    forceFlag = "Y";
    logger.warn("查詢項目[{}]在強制向聯徵查詢的清單內，將F_BFORCEFLAG : {} --> {}", new String[] { logfile.getTxid(), logfile.getForceflag(), forceFlag });
}
msg.setItem(W2DStdMessage.F_BFORCEFLAG, forceFlag);
```

這段程式碼檢查當前查詢項目是否在強制查詢清單中，如果是，則無論前端傳入的 `forceflag` 是什麼，都會將其設為 `"Y"`。

### 3. 從 AUDITLOG 讀取 TOJCIC

在 `JCICST00Service.java` 的 `stdCSQuery` 方法中（第 97-187 行），系統從 `AUDITLOG` 表中讀取 `TOJCIC` 值：

```java
List<AuditLog> retLogfile = auditLogDao.findToJcicByMsgIdQDateAndTxid(TODAY, MSGID, txid);
if (retLogfile != null && retLogfile.size() > 0) {
    vo.setToJcic(StringUtils.trimToEmpty(retLogfile.get(0).getTojcic()));
}
```

類似的程式碼也出現在 `stdCCQuery` 方法（第 197-286 行）和 `stdCTQuery` 方法（第 296-469 行）中。

### 4. AuditLogDao 的實現

在 `AuditLogDaoImpl.java` 中，`findToJcicByMsgIdQDateAndTxid` 方法（第 237-241 行）用於從 `AUDITLOG` 表中查詢 `TOJCIC` 值：

```java
@Override
public List<AuditLog> findToJcicByMsgIdQDateAndTxid(String qdate, String msgid, String txid) {
    final String SQL = "SELECT TOJCIC FROM AUDITLOG WHERE DATE=? AND TXID=? AND MSGID=?";
    return this.getJdbc().query(SQL, new Object[] { qdate, txid, msgid }, ParameterizedBeanPropertyRowMapper.newInstance(modelType));
}
```

## 結論

根據程式碼分析，您的說法是正確的：

1. `LOGFILE.FORCEFLAG` 是一個輸入參數，用於指示系統是否應該向聯徵中心發送查詢
2. 查詢執行完畢後，系統會將實際的查詢來源（是否向聯徵中心查詢）記錄到 `AUDITLOG` 表的 `TOJCIC` 欄位中
3. 系統會從 `AUDITLOG` 表中讀取 `TOJCIC` 值，設定到返回給前端的 `JCICST00VO` 物件中

這個流程可以用以下步驟表示：

```
前端 force2Jcic 參數 → LOGFILE.FORCEFLAG → 查詢執行 → AUDITLOG.TOJCIC → 回傳給前端
```

這種設計允許系統在保持靈活性的同時，確保關鍵查詢始終獲取最新的聯徵中心資料，並且記錄查詢的實際來源，提高系統的可靠性和資料的準確性。

## 使用情境

### 情境一：強制向聯徵中心查詢

當 `FORCEFLAG = "Y"` 時，系統會強制向聯徵中心發送查詢，不論資料庫中是否已有相關資料。在這種情況下，查詢完成後，`AUDITLOG.TOJCIC` 會被設為 `"Y"`，表示已向聯徵中心發送查詢。

### 情境二：直接從資料庫查詢

當 `FORCEFLAG = "N"` 時，系統會直接從資料庫取得資料，不向聯徵中心發送查詢。在這種情況下，查詢完成後，`AUDITLOG.TOJCIC` 會被設為 `"N"`，表示未向聯徵中心發送查詢。

### 情境三：自動判斷

當 `FORCEFLAG = "A"` 時，系統會根據一定的規則自動判斷是否需要向聯徵中心發送查詢。判斷的依據可能包括：
- 資料庫中是否已有相關資料
- 資料庫中的資料是否過期
- 查詢項目是否在強制查詢清單中

在這種情況下，`AUDITLOG.TOJCIC` 的值取決於系統的判斷結果，可能是 `"Y"` 或 `"N"`。

## 系統參數配置

系統參數 `SysParamConst.STDTXID_FORCE_TOJCIC` 的配置方式：

1. 在系統參數表中設定 `STDTXID_FORCE_TOJCIC` 參數
2. 參數值為以逗號分隔的查詢項目代碼列表，例如：`"HA01,HA02,HA03"`
3. 當查詢項目代碼在此列表中時，系統會強制向聯徵中心發送查詢，無論 `FORCEFLAG` 的值是什麼
