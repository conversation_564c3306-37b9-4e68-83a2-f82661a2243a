package mon.service.impl;

import java.io.IOException;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ibm.mq.MQException;
import com.ibm.mq.MQMessage;
import com.ibm.mq.MQQueueManager;
import com.ibm.mq.pcf.MQCFH;
import com.ibm.mq.pcf.MQCFIL;
import com.ibm.mq.pcf.MQCFIN;
import com.ibm.mq.pcf.MQCFST;
import com.ibm.mq.pcf.PCFMessageAgent;
import com.ibm.mq.pcf.PCFParameter;

/**
 * <pre>
 * JmsMQPCFAgent.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class JmsMQPCFAgent extends PCFMessageAgent {
	private static final Logger LOGGER = LoggerFactory.getLogger(JmsMQAdapter.class);

	private String userId;

	/**
	 * @param qmanager
	 * @throws MQException
	 */
	public JmsMQPCFAgent(MQQueueManager qmanager, String userId) throws MQException {
		super(qmanager);
		this.userId = userId;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.ibm.mq.pcf.PCFAgent#open(com.ibm.mq.MQQueueManager, java.lang.String, java.lang.String, boolean)
	 */
	@Override
	protected synchronized void open(MQQueueManager qmgr, String custTargetQueue, String targetQmanager,
			boolean external) throws MQException {

		String targetQueue = StringUtils.trimToEmpty(custTargetQueue);
		try {
			disconnect();
		} catch (MQException mqe) {
		}

		if (StringUtils.isEmpty(targetQueue)) {
			targetQueue = qmgr.getCommandInputQueueName();
		}

		String prefix = this.replyQueuePrefix;

		if ((prefix != null) && (prefix.length() > 0)) {
			prefix = prefix + Integer.toString(super.hashCode());
		}
		try {
			LOGGER.debug("[open]targetQueue={}, userId={}", targetQueue, this.userId);

			this.adminQueue = qmgr.accessQueue(targetQueue, 8240, targetQmanager, "", this.userId);
		} catch (MQException e) {
			if (e.getReason() == 2045) {
				this.adminQueue = qmgr.accessQueue(targetQueue, 8208, targetQmanager, "", this.userId);
			} else {
				throw e;
			}
		}

		LOGGER.debug("[open]modelQueueName={}, userId={}", modelQueueName, this.userId);
		this.replyQueue = qmgr.accessQueue(this.modelQueueName, 8196, "", prefix, this.userId);
		this.replyQueueName = this.replyQueue.name;

		this.replyQueue.closeOptions = 2;

		if (!(external)) {
			this.qmanager = qmgr;
		}

		getBasicQmgrInfo(qmgr, true);
	}

	private void getBasicQmgrInfo(MQQueueManager qmgr, boolean tryBacklevel) throws MQException {
		this.qmanager_level = qmgr.getCommandLevel();

		int type = 16;
		int version = 3;

		if (tryBacklevel) {
			type = 1;
			version = 1;
		}

		try {
			MQMessage message = setRequestMQMD(new MQMessage());
			MQCFH.write(message, 2, 1, type, version);
			MQCFIL.write(message, 1001, new int[] { 32, 2015, 2 });

			this.adminQueue.put(message, this.pmo);

			message.messageId = null;
			message.encoding = this.encoding;
			message.characterSet = this.defaultCharacterSet;
			this.replyQueue.get(message, this.gmo);

			MQCFH cfh = new MQCFH(message);

			if (cfh.reason == 0) {
				int parameterCount = cfh.parameterCount;
				while (parameterCount-- > 0) {
					PCFParameter p = PCFParameter.nextParameter(message);

					switch (p.getParameter()) {
					case 32:
						this.qmanager_platform = ((MQCFIN) p).value;
						break;
					case 2015:
						String temp = ((MQCFST) p).string;
						if (temp != null) {
							this.qmanager_name = ((MQCFST) p).string.trim();
						} else {
							this.qmanager_name = null;
						}
						break;
					case 2:
						this.qmanager_ccsid = ((MQCFIN) p).value;
					}

				}

			} else if ((((cfh.reason == 3001) || (cfh.reason == 3003))) && (tryBacklevel == true)) {
				getBasicQmgrInfo(qmgr, false);
			} else {
				throw new MQException(cfh.compCode, cfh.reason, this);
			}
		} catch (IOException e) {
			MQException returnException = new MQException(2, 2195, this);
			returnException.initCause(e);
			throw returnException;
		}
	}
}
