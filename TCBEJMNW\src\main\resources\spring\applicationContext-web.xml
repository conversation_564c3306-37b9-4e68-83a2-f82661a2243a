<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    	   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config />

	<bean id="placeholderConfigurer"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="ignoreUnresolvablePlaceholders" value="false" />
		<property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE" />
		<property name="ignoreResourceNotFound" value="false" />
		<property name="locations">
			<list>
				<value>classpath:ejmn.properties</value>
			</list>
		</property>
	</bean>

	<bean id="springContext" class="mon.service.impl.SpringContextHelper" />

	<util:properties id="monProperties" location="classpath:ejmn.properties" />
	<util:properties id="errorProperties" location="classpath:ejmn.error.desc.properties" />

	<import resource="classpath:spring/datasource-jndi.xml" />
	<import resource="classpath:spring/jdbc-ejmn.xml" />
	<import resource="classpath:spring/jdbc-ejcic.xml" />
	<import resource="classpath:spring/jdbc-etch.xml" />
	<import resource="classpath:spring/jdbc-stj.xml" />
    <import resource="classpath:spring/jdbc-eloan.xml" />
	<import resource="classpath:spring/services-base.xml" />
	<import resource="classpath:spring/services-web.xml" />
    <import resource="classpath:spring/schedule.xml" />
</beans>