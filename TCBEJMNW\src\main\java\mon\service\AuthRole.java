package mon.service;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <pre> AuthLevel.java </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul> <li>2003/06/30,PGU,NEW </ul>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuthRole {
	String[] role();
}
