package etch.dao.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import etch.dao.AtomProfileDao;
import etch.model.AtomProfile;

/**
 * <pre>
 * AtomProfileDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("atomProfileDao")
public class AtomProfileDaoImpl extends AbstractGenericDao<AtomProfile> implements AtomProfileDao {

	@Override
	public List<AtomProfile> findByTxid(String txid) {
		final String SQL = "SELECT  O.TXID,O.ATOMID,A.CNAME,A.PRICE  FROM TXID O  LEFT JOIN ATOMPROFILE A ON O.ATOMID=A.ATOMID WHERE O.TXID=? ORDER BY  O.TXID,O.ATOMID";
		Object[] args = new Object[] { txid };
		SqlRowSet srs = this.getJdbc().queryForRowSet(SQL, args);
		List<AtomProfile> itemList = new ArrayList<AtomProfile>();
		while (srs.next()) {
			AtomProfile model = new AtomProfile();
			model.setAtomid(srs.getString("ATOMID"));
			model.setCname(srs.getString("CNAME"));
			model.setPrice(srs.getBigDecimal("PRICE"));
			itemList.add(model);
		}

		return itemList;
	}

}