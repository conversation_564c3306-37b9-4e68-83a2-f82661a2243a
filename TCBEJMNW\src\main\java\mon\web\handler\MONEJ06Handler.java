package mon.web.handler;

import java.util.Map;

import javax.annotation.Resource;

import mon.exception.AppException;
import mon.model.EJCICLogFile;
import mon.service.AuthRole;
import mon.web.core.AbstractHandler;
import mon.web.core.WebContext;
import mon.web.core.WebView;
import mon.web.model.MONEJ06VO;
import mon.web.model.WebUserProfile;
import mon.web.service.impl.MONEJ00Service;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;

/**
 * <pre>
 * MONMQ02Handler.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Controller
@AuthRole(role = { WebUserProfile.ROLE_MONITOR })
public class MONEJ06Handler extends AbstractHandler {

	@Resource
	MONEJ00Service monej00Service;

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.web.core.Handler#action(mon.web.core.WebContext, java.util.Map)
	 */
	@Override
	public WebView action(WebContext context, Map<String, String> paramMap) throws AppException {
		String url = "/mon/MONEJ06R.jsp";

		String date = StringUtils.trimToEmpty(paramMap.get("qdate"));
		String txId = StringUtils.trimToEmpty(paramMap.get("txId")).toUpperCase();
		String msgId = StringUtils.trimToEmpty(paramMap.get("msgId"));
		String productId = StringUtils.trimToEmpty(paramMap.get("productId"));
		String cpxProdiId = StringUtils.trimToEmpty(paramMap.get("cpxProdiId"));
		String requestId = StringUtils.trimToEmpty(paramMap.get("requestId"));
		String division = StringUtils.trimToEmpty(paramMap.get("division"));
		String querykey1 = StringUtils.trimToEmpty(paramMap.get("querykey1")).toUpperCase();
		String querykey2 = StringUtils.trimToEmpty(paramMap.get("querykey2"));
		String querykey3 = StringUtils.trimToEmpty(paramMap.get("querykey3"));
		String rc = StringUtils.trimToEmpty(paramMap.get("rc"));
		if (!"ST".equalsIgnoreCase(productId)) {
			productId = cpxProdiId;
		}

		EJCICLogFile input = new EJCICLogFile();
		input.setDate(date);
		input.setTxid(txId);
		input.setMsgid(msgId);
		input.setProductid(productId);
		input.setRequestid(requestId);
		input.setDivision(division);
		input.setQuerykey1(querykey1);
		input.setQuerykey2(querykey2);
		input.setIsprint(querykey3);
		input.setRc(rc);

		MONEJ06VO vo = new MONEJ06VO();
		vo.setInput(input);

		vo = this.monej00Service.doEJ06(vo);

		return WebView.forward("/mon/MONEJ06R.jsp").requestAttr("monej06data", vo);
	}

}
