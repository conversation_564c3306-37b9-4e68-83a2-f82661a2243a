# TCBETCHW 專案詳細規格文件

## 專案概述

TCBETCHW (Taiwan Cooperative Bank - Electronic Ticket Clearing House Web) 是票據交換所交易系統的Web前端專案，主要負責處理與票據交換所相關的查詢和交易業務。

### 主要功能
- **票據查詢**: 支票、本票、匯票等票據查詢
- **票據狀態管理**: 票據狀態更新、退票處理、止付處理
- **區域碼處理**: 支援不同區域票據交換所的路由
- **授權管理**: 使用者權限和交易授權控制

## 技術架構

### 技術棧
- **JDK**: 1.6
- **Web框架**: Spring MVC 2.x
- **建置工具**: Maven 3.2.5
- **Web容器**: Jetty 8.1.8
- **資料庫**: IBM DB2
- **訊息佇列**: IBM MQ
- **前端**: JSP + JavaScript + CSS

### 專案結構

```
TCBETCHW/
├── src/main/java/etch/
│   ├── dao/                    # 資料存取層
│   ├── disp/                   # 分發器模組
│   ├── exception/              # 例外處理
│   ├── gw/                     # 閘道模組
│   ├── jdbc/                   # JDBC工具
│   ├── ma/                     # 管理功能
│   ├── model/                  # 資料模型
│   ├── msg/                    # 訊息處理
│   ├── service/                # 服務層
│   ├── utils/                  # 工具類別
│   └── web/                    # Web層
│       ├── constant/           # 常數定義
│       ├── core/               # 核心框架
│       ├── filter/             # 過濾器
│       ├── handler/            # 請求處理器
│       ├── model/              # Web模型
│       ├── render/             # 渲染器
│       ├── service/            # Web服務
│       └── utils/              # Web工具
├── src/main/resources/
│   ├── spring/                 # Spring配置
│   ├── etch.properties         # 主配置檔
│   ├── DEV.etch.properties     # 開發環境配置
│   ├── TESTING.etch.properties # 測試環境配置
│   └── etc/                    # 其他配置
└── WebContent/                 # Web資源
    ├── WEB-INF/
    ├── jsp/                    # JSP頁面
    ├── js/                     # JavaScript
    └── css/                    # 樣式表
```

## 核心架構設計

### Web層架構

```mermaid
classDiagram
    class WebController {
        +void doGet(HttpServletRequest, HttpServletResponse)
        +void doPost(HttpServletRequest, HttpServletResponse)
        +void processRequest(HttpServletRequest, HttpServletResponse)
        -Handler getHandler(String)
    }
    
    class AbstractHandler {
        <<abstract>>
        +WebView action(WebContext, Map)
        +String getErrorBackUrl()
        #Logger logger
    }
    
    class WebContext {
        +HttpServletRequest request
        +HttpServletResponse response
        +WebUserProfile userProfile
        +String userId
        +String branchId
        +Map parameters
    }
    
    class WebView {
        +String viewName
        +Map model
        +boolean isRedirect
        +boolean isJson
        +String jsonOutput
    }
    
    WebController --> AbstractHandler
    AbstractHandler --> WebContext
    AbstractHandler --> WebView
```

### 票據交易Handler

系統使用Handler模式處理不同的票據業務請求：

#### 主要Handler類別
- **ETCHCM01Handler**: 票據查詢主頁面
- **ETCHCM02Handler**: 票據查詢處理
- **ETCHCM03Handler**: 票據狀態管理
- **ETCHMA01Handler**: 授權管理
- **ETCHCS01Handler**: 系統參數設定

#### Handler實作範例
```java
@Controller
public class ETCHCM02Handler extends AbstractHandler {
    
    @Resource
    private TchTxidService tchTxidService;
    
    @Override
    public WebView action(WebContext context, Map<String, String> paramMap) 
            throws AppException {
        
        // 1. 參數驗證
        String billNo = paramMap.get("billNo");
        String areaCode = paramMap.get("areaCode");
        
        // 2. 授權檢查
        if (!tchTxidService.checkAuth("CM02", billNo, context.getUserId())) {
            throw new AppException("授權不足");
        }
        
        // 3. 區域碼處理
        String targetAreaCode = resolveAreaCode(areaCode, billNo);
        
        // 4. 業務處理
        Result result = tchTxidService.queryBill("CM02", billNo, targetAreaCode);
        
        // 5. 回傳結果
        return WebView.jsp("etch/cm02_result")
                     .addModel("result", result)
                     .addModel("areaCode", targetAreaCode);
    }
}
```

### 服務層架構

```mermaid
classDiagram
    class TchTxidService {
        <<interface>>
        +boolean checkTxid(String txid, String billNo)
        +boolean checkAuth(String txid, String billNo, String userId)
        +Result queryBill(String txid, String billNo, String areaCode)
        +Result updateBillStatus(String billNo, String status)
    }
    
    class AreaCodeService {
        <<interface>>
        +String resolveAreaCode(String inputAreaCode, String billNo)
        +String getDefaultAreaCode()
        +List~String~ getSupportedAreaCodes()
    }
    
    class BillStatusService {
        <<interface>>
        +BillStatus getBillStatus(String billNo)
        +void updateBillStatus(String billNo, String status, String operator)
        +List~BillStatus~ getBillStatusHistory(String billNo)
    }
    
    class MQService {
        <<interface>>
        +void sendToTch(String areaCode, Message msg)
        +Message receiveFromTch(String areaCode)
        +boolean isConnected(String areaCode)
    }
    
    TchTxidService --> MQService
    TchTxidService --> AreaCodeService
    TchTxidService --> BillStatusService
```

## 主要業務模組

### 1. 票據查詢模組

#### 查詢類型
- **CM01**: 支票查詢
- **CM02**: 本票查詢
- **CM03**: 匯票查詢
- **CM04**: 票據狀態查詢
- **CM05**: 退票記錄查詢
- **CM06**: 止付記錄查詢

#### 查詢流程
```mermaid
sequenceDiagram
    participant User as 使用者
    participant Handler as Handler
    participant AreaService as AreaCodeService
    participant TchService as TchTxidService
    participant MQ as MQService
    participant TCH as 票據交換所
    
    User->>Handler: 提交票據查詢
    Handler->>AreaService: 解析區域碼
    AreaService-->>Handler: 回傳目標區域碼
    Handler->>TchService: 檢查授權
    TchService-->>Handler: 授權通過
    Handler->>TchService: 處理查詢
    TchService->>MQ: 發送MQ訊息
    MQ->>TCH: 轉發至票據交換所
    TCH-->>MQ: 回傳查詢結果
    MQ-->>TchService: 接收回應
    TchService-->>Handler: 回傳處理結果
    Handler-->>User: 顯示查詢結果
```

### 2. 區域碼處理模組

#### 區域碼概念
票據交換所系統支援多個區域，每個區域有獨立的處理系統：
- **00**: 預設區域碼（台北）
- **01**: 台中區域
- **02**: 高雄區域
- **03**: 其他區域

#### 區域碼處理架構
```mermaid
classDiagram
    class AreaCodeResolver {
        +String resolve(String inputAreaCode, String billNo)
        +String getDefaultAreaCode()
        -String extractAreaFromBillNo(String billNo)
        -boolean isValidAreaCode(String areaCode)
    }
    
    class AreaCodeConfig {
        +String defaultAreaCode
        +Map~String,String~ areaCodeMapping
        +List~String~ supportedAreaCodes
    }
    
    class TchRoutingService {
        +String getTargetQueue(String areaCode)
        +String getResponseQueue(String areaCode)
        +boolean isAreaCodeActive(String areaCode)
    }
    
    AreaCodeResolver --> AreaCodeConfig
    AreaCodeResolver --> TchRoutingService
```

#### 區域碼處理邏輯
```java
@Service
public class AreaCodeServiceImpl implements AreaCodeService {
    
    @Value("${TCH_GW_DEF_AREACODE}")
    private String defaultAreaCode;
    
    @Override
    public String resolveAreaCode(String inputAreaCode, String billNo) {
        // 1. 如果有明確指定區域碼，直接使用
        if (StringUtils.isNotEmpty(inputAreaCode) && isValidAreaCode(inputAreaCode)) {
            return inputAreaCode;
        }
        
        // 2. 從票據號碼推斷區域碼
        String extractedAreaCode = extractAreaFromBillNo(billNo);
        if (StringUtils.isNotEmpty(extractedAreaCode)) {
            return extractedAreaCode;
        }
        
        // 3. 使用預設區域碼
        return defaultAreaCode;
    }
    
    private String extractAreaFromBillNo(String billNo) {
        // 根據票據號碼格式推斷區域碼
        if (StringUtils.length(billNo) >= 10) {
            String prefix = billNo.substring(0, 2);
            return areaCodeMapping.get(prefix);
        }
        return null;
    }
}
```

### 3. 票據狀態管理模組

#### 票據狀態類型
- **NORMAL**: 正常
- **RETURNED**: 退票
- **STOPPED**: 止付
- **CLEARED**: 已兌現
- **CANCELLED**: 已作廢

#### 狀態管理流程
```mermaid
stateDiagram-v2
    [*] --> NORMAL : 票據建立
    NORMAL --> CLEARED : 正常兌現
    NORMAL --> RETURNED : 退票
    NORMAL --> STOPPED : 止付
    NORMAL --> CANCELLED : 作廢
    RETURNED --> CLEARED : 補提兌現
    STOPPED --> CLEARED : 解除止付後兌現
    CLEARED --> [*]
    CANCELLED --> [*]
```

#### 狀態管理類別
```java
@TModel(tableName = "BILL_STATUS")
public class BillStatus extends AbstractModelMeta {
    
    @TKeyField
    @TField(columnName = "BILL_NO")
    private String billNo;
    
    @TField(columnName = "BILL_TYPE")
    private String billType;
    
    @TField(columnName = "STATUS")
    private String status;
    
    @TField(columnName = "STATUS_DATE")
    private Date statusDate;
    
    @TField(columnName = "OPERATOR")
    private String operator;
    
    @TField(columnName = "AREA_CODE")
    private String areaCode;
    
    // 業務邏輯方法
    public boolean isReturned() {
        return "RETURNED".equals(status);
    }
    
    public boolean isStopped() {
        return "STOPPED".equals(status);
    }
    
    public boolean canBeCleared() {
        return "NORMAL".equals(status) || "RETURNED".equals(status);
    }
}
```

## MQ訊息處理

### MQ配置架構
```properties
# 票據交換所Gateway配置
TCH_QUEUE_GW_TOTCH=T0060000.TMET.QR
TCH_QUEUE_GW_FROMTCH=MQT006D.OUTLOCAL.QL
TCH_GW_DEF_AREACODE=00
TCH_GW_BANKCODE=006
TCH_GW_BANKID=0060000

# Dispatcher配置
TCH_QUEUE_W2D=MQT006D.WAS2DIS.QL
TCH_QUEUE_D2G=MQT006D.TCHCONN.QL
TCH_QUEUE_G2D=MQT006D.TCHW000.QL
TCH_QUEUE_D2W=MQT006D.DIS2WAS.QL

# 執行緒配置
TCH_MAX_WEB_DISP=2
TCH_MAX_TCH_DISP=2
```

### 訊息格式
```xml
<TCHMessage>
    <Header>
        <MessageId>TCH_20231201_001</MessageId>
        <Timestamp>2023-12-01T10:30:00</Timestamp>
        <TxId>CM02</TxId>
        <UserId>USER001</UserId>
        <BankCode>006</BankCode>
        <AreaCode>00</AreaCode>
    </Header>
    <Body>
        <BillQuery>
            <BillNo>**********</BillNo>
            <BillType>CHECK</BillType>
            <QueryType>STATUS</QueryType>
        </BillQuery>
    </Body>
</TCHMessage>
```

### MQ路由機制
```mermaid
graph TD
    A[Web層] --> B[Dispatcher]
    B --> C{區域碼判斷}
    C -->|00| D[台北票交所]
    C -->|01| E[台中票交所]
    C -->|02| F[高雄票交所]
    C -->|03| G[其他票交所]
    
    D --> H[Gateway 00]
    E --> I[Gateway 01]
    F --> J[Gateway 02]
    G --> K[Gateway 03]
    
    H --> L[MQ Queue 00]
    I --> M[MQ Queue 01]
    J --> N[MQ Queue 02]
    K --> O[MQ Queue 03]
```

## 資料模型設計

### 核心資料表

#### 票據交易設定檔
```sql
CREATE TABLE TCH_TXID_PROFILE (
    TXID VARCHAR(10) NOT NULL PRIMARY KEY,
    TXID_NAME VARCHAR(100),
    TXID_DESC VARCHAR(200),
    TXID_TYPE VARCHAR(10),
    AREA_CODE VARCHAR(2),
    NEED_AUTH CHAR(1),
    NEED_CHARGE CHAR(1),
    STATUS CHAR(1)
);
```

#### 票據狀態記錄
```sql
CREATE TABLE BILL_STATUS (
    BILL_NO VARCHAR(20) NOT NULL,
    STATUS_DATE DATE NOT NULL,
    BILL_TYPE VARCHAR(10),
    STATUS VARCHAR(20),
    STATUS_DESC VARCHAR(100),
    OPERATOR VARCHAR(20),
    AREA_CODE VARCHAR(2),
    CREATE_TIME TIMESTAMP,
    PRIMARY KEY (BILL_NO, STATUS_DATE)
);
```

#### 票據交易日誌
```sql
CREATE TABLE ETCH_LOG_FILE (
    DATE_YYYYMMDD DATE NOT NULL,
    TXID VARCHAR(10) NOT NULL,
    MSGID VARCHAR(50) NOT NULL,
    BILL_NO VARCHAR(20),
    AREA_CODE VARCHAR(2),
    RC VARCHAR(10),
    RESPONSE_TIME INTEGER,
    CREATE_TIME TIMESTAMP,
    PRIMARY KEY (DATE_YYYYMMDD, TXID, MSGID)
);
```

## 環境配置管理

### 多環境配置
系統支援多環境配置，透過不同的properties檔案：

#### 開發環境 (DEV.etch.properties)
```properties
# 開發環境MQ設定
MQ_SERVER=127.0.0.1
MQ_PORT=1414
MQ_MGR=MQT006D

# 開發環境Notes設定
NOTES.SERVER=10.0.6.196
NOTES.SERVER2=10.0.59.19
```

#### 測試環境 (TESTING.etch.properties)
```properties
# 測試環境MQ設定
MQ_SERVER=10.0.7.20
MQ_PORT=1414
MQ_MGR=MQT006T

# 測試環境Notes設定
NOTES.SERVER=10.0.59.19
NOTES.SERVER2=10.0.59.20
```

#### 生產環境 (etch.properties)
```properties
# 生產環境MQ設定
MQ_SERVER=10.0.7.20
MQ_PORT=1414
MQ_MGR=MQT006D

# 生產環境Notes設定
NOTES.SERVER=10.0.5.47
NOTES.SERVER2=10.0.5.48
```

### Spring配置結構
```
spring/
├── applicationContext-web.xml      # 主配置檔
├── datasource-jndi.xml            # 資料源配置
├── jdbc.xml                       # JDBC配置
├── services-base.xml              # 基礎服務
└── services-web.xml               # Web服務
```

## 安全機制

### 認證與授權
1. **Notes認證**: 透過Lotus Notes系統驗證
2. **票據授權**: 基於票據類型和金額的授權控制
3. **區域授權**: 特定區域的存取權限控制
4. **時間控制**: 營業時間和授權期限控制

### 資料保護
1. **票據號碼遮罩**: 敏感票據資訊部分遮蔽
2. **傳輸加密**: MQ訊息加密傳輸
3. **稽核記錄**: 完整的票據查詢記錄

## 部署架構

### 系統部署拓撲
```mermaid
graph TB
    subgraph "Web層"
        WEB1[TCBETCHW Web 1]
        WEB2[TCBETCHW Web 2]
    end
    
    subgraph "中間層"
        DISP1[TCH Dispatcher 1]
        DISP2[TCH Dispatcher 2]
    end
    
    subgraph "閘道層"
        GW00[Gateway 區域00]
        GW01[Gateway 區域01]
        GW02[Gateway 區域02]
    end
    
    subgraph "票據交換所"
        TCH00[台北票交所]
        TCH01[台中票交所]
        TCH02[高雄票交所]
    end
    
    WEB1 --> DISP1
    WEB2 --> DISP2
    DISP1 --> GW00
    DISP1 --> GW01
    DISP2 --> GW01
    DISP2 --> GW02
    
    GW00 --> TCH00
    GW01 --> TCH01
    GW02 --> TCH02
```

### 容器配置
- **Web容器**: Jetty 8.1.8
- **JVM參數**: -Xms512m -Xmx1024m
- **連線池**: 最大50個連線
- **Session逾時**: 30分鐘
