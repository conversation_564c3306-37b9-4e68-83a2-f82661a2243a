package mon.web.handler;

import java.util.Map;

import javax.annotation.Resource;

import mon.exception.AppException;
import mon.model.STJLogFile;
import mon.service.AuthRole;
import mon.web.constant.WebConst;
import mon.web.core.AbstractHandler;
import mon.web.core.WebContext;
import mon.web.core.WebView;
import mon.web.model.MONTJ01VO;
import mon.web.model.WebUserProfile;
import mon.web.service.impl.MONTJ00Service;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;

/**
 * <pre>
 * MONMQ02Handler.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Controller
@AuthRole(role = { WebUserProfile.ROLE_MONITOR, WebUserProfile.ROLE_TOJCIC })
public class MONTJ01Handler extends AbstractHandler {

	@Resource
	MONTJ00Service montj00Service;

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.web.core.Handler#action(mon.web.core.WebContext, java.util.Map)
	 */
	@Override
	public WebView action(WebContext context, Map<String, String> paramMap) throws AppException {
		String method = StringUtils.trimToEmpty(paramMap.get(WebConst.REQ_PARAM_METHOD));
		this.logger.info("{}={}", WebConst.REQ_PARAM_METHOD, method);
		this.logger.debug(paramMap.toString());

		WebView result = WebView.NULL;
		if ("RESULT".equalsIgnoreCase(method)) {
			result = this.findResult(context, paramMap);
		} else {
			result = this.query(context, paramMap);
		}
		return result;
	}

	public WebView query(WebContext context, Map<String, String> paramMap) throws AppException {
		String url = "/stj/MONTJ01R.jsp";

		String yyy = "";
		String mm = "";
		String yyymm = StringUtils.trimToEmpty(paramMap.get("yyymm"));
		String sendType = StringUtils.trimToEmpty(paramMap.get("sendType"));
		String step = StringUtils.trimToEmpty(paramMap.get("step"));
		String rc = StringUtils.trimToEmpty(paramMap.get("rc"));
		String runSNo = StringUtils.trimToEmpty(paramMap.get("runSNo"));

		if (yyymm.length() == 6) {
			yyy = yyymm.substring(0, 3);
			mm = yyymm.substring(4, 6);
		}

		logger.info("yyymm:" + yyymm);
		logger.info("sendType:" + sendType);
		logger.info("step:" + step);
		logger.info("rc:" + rc);
		logger.info("runSNo:" + runSNo);

		STJLogFile input = new STJLogFile();
		input.setYyy(yyy);
		input.setMm(mm);
		input.setSendType(sendType);
		input.setStep(step);
		input.setRc(rc);
		input.setRunSNo(runSNo);

		MONTJ01VO vo = new MONTJ01VO();
		vo.setInput(input);
		vo = montj00Service.doTJ01Query(vo);

		return WebView.forward(url).requestAttr("montj01data", vo);
	}

	public WebView findResult(WebContext context, Map<String, String> paramMap) throws AppException {
		String url = "/stj/MONTJ01F.jsp";

		String runSNo = StringUtils.trimToEmpty(paramMap.get("runSNo"));

		MONTJ01VO vo = new MONTJ01VO();
		vo.setOutput(montj00Service.doTJ01Result(runSNo));

		return WebView.forward(url).requestAttr("montj01data", vo);
	}

}
