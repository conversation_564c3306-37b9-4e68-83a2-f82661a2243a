<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
    xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    	   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <bean id="sysUsageJob" class="mon.schedule.SysUsageJob" />
    <bean id="queueDepthJob" class="mon.schedule.QueueDepthJob" />
    <bean id="houseKeepingJob" class="mon.schedule.HouseKeepingJob" />
    <bean id="stjMailCheckerJob" class="mon.schedule.STJMailCheckerJob" />

    <bean id="sysUsageJobDetail" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="sysUsageJob" />
        <property name="targetMethod" value="check" />
    </bean>

    <bean id="queueDepthJobDetail" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="queueDepthJob" />
        <property name="targetMethod" value="check" />
    </bean>

    <bean id="houseKeepingJobDetail" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="houseKeepingJob" />
        <property name="targetMethod" value="housekeeping" />
    </bean>

    <bean id="stjMailCheckerJobDetail" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="stjMailCheckerJob" />
        <property name="targetMethod" value="check" />
    </bean>


    <bean id="sysUsageTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
        <property name="jobDetail" ref="sysUsageJobDetail" />
        <property name="cronExpression" value="0 */2 * * * ?" />
    </bean>

    <bean id="queueDepthTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
        <property name="jobDetail" ref="queueDepthJobDetail" />
        <property name="cronExpression" value="0 */2 * * * ?" />
    </bean>


    <bean id="houseKeepingTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
        <property name="jobDetail" ref="houseKeepingJobDetail" />
        <property name="cronExpression" value="0 0 0 * * ?" />
    </bean>

    <bean id="stjMailCheckerTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
        <property name="jobDetail" ref="stjMailCheckerJobDetail" />
        <property name="cronExpression" value="0 */30 9-17 9-10 * ?" />
    </bean>

    <bean id="schedulerFactoryBean" class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
        <property name="quartzProperties">
            <props>
                <prop key="org.quartz.threadPool.class">org.quartz.simpl.SimpleThreadPool</prop>
                <prop key="org.quartz.threadPool.threadCount">3</prop>
            </props>
        </property>
        <property name="waitForJobsToCompleteOnShutdown" value="true" />
        <property name="triggers">
            <list>
                <ref bean="sysUsageTrigger" />
                <ref bean="queueDepthTrigger" />
                <ref bean="houseKeepingTrigger" />
                <ref bean="stjMailCheckerTrigger" />
            </list>
        </property>
    </bean>

</beans>