package ejcic.dao.impl;

import java.util.List;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import ejcic.dao.OptListDao;
import ejcic.model.OptList;

/**
 * <pre>
 * OptlistDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("optListDao")
public class OptListDaoImpl extends AbstractGenericDao<OptList> implements OptListDao {

	public List<OptList> finaByTxid(String txid) {
		String sql = "SELECT * FROM OPTLIST WHERE TXID=?";
		return this.getJdbc().query(sql, new Object[] { txid },
				ParameterizedBeanPropertyRowMapper.newInstance(modelType));

	}

	public int deleteByTxid(String txid) {
		String sql = "DELETE FROM OPTLIST WHERE TXID=?";
		return this.getJdbc().update(sql, new Object[] { txid });
	}
}