package mon.web.handler;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import mon.utils.FortifyUtils;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Servlet implementation class MONCM02Servlet
 */
@WebServlet("/MONCM02Servlet")
public class MONCM02Servlet extends HttpServlet {

	private static final Logger logger = LoggerFactory.getLogger(MONCM02Servlet.class);

	private static final long serialVersionUID = 1L;

	/**
	 * @see HttpServlet#HttpServlet()
	 */
	public MONCM02Servlet() {
		super();
	}

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		this.doPost(request, response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		try {
			String postData = IOUtils.toString(request.getInputStream());
			postData = FortifyUtils.checkOpenRedirect(postData);
			response.sendRedirect(FortifyUtils.checkHeader(request.getServletContext().getContextPath(), request.getServletContext().getContextPath()
					+ "/WebController?_HANDLER_=MONCM02&AD=Y&" + postData));
		} catch (Exception e) {
			logger.error("doPost EXCEPTION!!", e);
		}
	}
}
