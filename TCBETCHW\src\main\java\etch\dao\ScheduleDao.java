package etch.dao;

import java.util.List;
import java.util.Map;

import etch.model.SchItem;
import etch.model.SchMain;

public interface ScheduleDao {

	public int inserSchduleMain(SchMain shcMain);

	public int inserSchduleItem(SchItem schItem);

	public List<Map<String, Object>> findSchMainByToday(String requestId);

	public List<Map<String, Object>> findSchMainByToday(String queryKey, String requestId);

	public List<Map<String, Object>> findSchMainByKey(String queryKey, String requestId);

	public List<Map<String, Object>> findSchMainByDate(String strDate, String endDate, String queryKey, String requestId);

	public int deleteSchduleItem(List<Long> ids, boolean isMain);

	public int deleteSchduleMain(List<Long> ids);

	public int upadateSchduleMainCount(long shcSno);

	public int upadateSchduleMainCountAndChkDel(long schSno);

	public List<Map<String, Object>> findSchItemByKey(long schSno);

	public List<SchMain> findNonExeSchMain(String status);

	public List<SchItem> findNonExeSchItem(long shcSno, String status);

	public int updateSchMain(SchMain sm);

	public int updateSchItem(SchItem item);

	public void insertSchedule(SchMain schMain, List<SchItem> schItemList);

	public List<String> getScheItemRCs(long schNo);

}
