package mon.web.model;

/**
 * <pre>
 * ErrRsVo.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class MONTJ02IDVO {
	private String id;
	private String tjNo;
	private String fileName;
	private String exeResult;

	/**
	 * Returns the id
	 * 
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * Sets the id
	 * 
	 * @param id
	 *            the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * Returns the exeResult
	 * 
	 * @return the exeResult
	 */
	public String getExeResult() {
		return exeResult;
	}

	/**
	 * Sets the exeResult
	 * 
	 * @param exeResult
	 *            the exeResult to set
	 */
	public void setExeResult(String exeResult) {
		this.exeResult = exeResult;
	}

	/**
	 * Returns the tjNo
	 * 
	 * @return the tjNo
	 */
	public String getTjNo() {
		return tjNo;
	}

	/**
	 * Sets the tjNo
	 * 
	 * @param tjNo
	 *            the tjNo to set
	 */
	public void setTjNo(String tjNo) {
		this.tjNo = tjNo;
	}

	/**
	 * Returns the fileName
	 * 
	 * @return the fileName
	 */
	public String getFileName() {
		return fileName;
	}

	/**
	 * Sets the fileName
	 * 
	 * @param fileName
	 *            the fileName to set
	 */
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

}
