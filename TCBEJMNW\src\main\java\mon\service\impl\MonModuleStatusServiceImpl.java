package mon.service.impl;

import java.util.List;

import javax.annotation.Resource;

import mon.model.MonModuleStatus;
import mon.msg.MonMessage;
import mon.msg.MonReqMessage;
import mon.service.MQService;
import mon.service.MonModuleStatusService;
import mon.service.SysParamService;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <pre>
 * MonModuleStatusServiceImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Service("monModuleStatusService")
public class MonModuleStatusServiceImpl implements MonModuleStatusService {
	@Resource
	@Qualifier("EJCICMQService")
	MQService ejcicMQService;

	@Resource
	@Qualifier("ETCHMQService")
	MQService etchMQService;

	@Resource
	SysParamService sysParamService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleStatusService#getEJCICWebStatus()
	 */
	@Override
	public List<MonModuleStatus> doEJCICWebStatus() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleStatusService#getEJCICDispStatus()
	 */
	@Override
	public List<MonModuleStatus> doEJCICDispStatus() {
		MonMessage monMsg = new MonReqMessage();

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleStatusService#getEJCICGWStatus()
	 */
	@Override
	public List<MonModuleStatus> doEJCICGWStatus() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleStatusService#getEloanDispStatus()
	 */
	@Override
	public List<MonModuleStatus> doEloanDispStatus() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleStatusService#getETCHWebStatus()
	 */
	@Override
	public List<MonModuleStatus> doETCHWebStatus() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleStatusService#getETCHDispStatus()
	 */
	@Override
	public List<MonModuleStatus> doETCHDispStatus() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleStatusService#getETCHGWStatus()
	 */
	@Override
	public List<MonModuleStatus> doETCHGWStatus() {

		return null;
	}
}
