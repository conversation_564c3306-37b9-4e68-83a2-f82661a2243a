package mon.web.model;

import java.util.List;

import mon.model.ELJobLog;

/**
 * <pre>
 * MONEJ01VO.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class MONEL01VO {
	boolean ok = false;
	// input
	ELJobLog input;

	// output
	List<ELJobLog> dataList;

	public boolean isOk() {
		return this.ok;
	}

	/**
	 * Sets the ok
	 * 
	 * @param ok
	 *            the ok to set
	 */
	public void setOk(boolean ok) {
		this.ok = ok;
	}

	/**
	 * Returns the input
	 * 
	 * @return the input
	 */
	public ELJobLog getInput() {
		return input;
	}

	/**
	 * Sets the input
	 * 
	 * @param input
	 *            the input to set
	 */
	public void setInput(ELJobLog input) {
		this.input = input;
	}

	/**
	 * Returns the dataList
	 * 
	 * @return the dataList
	 */
	public List<ELJobLog> getDataList() {
		return dataList;
	}

	/**
	 * Sets the dataList
	 * 
	 * @param dataList
	 *            the dataList to set
	 */
	public void setDataList(List<ELJobLog> dataList) {
		this.dataList = dataList;
	}

}
