<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">

<log4j:configuration>
	<appender name="SOCKETMONITOR" class="tw.com.iisi.mnm.commons.socketappender.MnmSocketAppender">
		<param name="RemoteHost" value="${REMOTEHOST}" />
		<param name="Port" value="4560" />
		<param name="LocationInfo" value="true" />
		<param name="Application" value="$(COMPUTERNAME)-ETCH_DISP_WEB" />
		<param name="Threshold" value="DEBUG" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="%d [%t]|%-5p|%X{SESSION_ID}|%X{CLIENT_IP}|%X{BRANCH_ID}|%X{USER_ID}|%-20.20c{1}|%m%n" />
		</layout>
	</appender>

	<appender name="CONSOLE" class="org.apache.log4j.ConsoleAppender">
		<param name="Target" value="System.out" />
		<param name="Threshold" value="DEBUG" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="%d [%t] | %X{reqURI} | %-20.20c{1} [%-5p] %m%n" />
		</layout>
	</appender>

	<appender name="ETCH" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="/aphome/etch/logs/DISP_WEB.log" />
		<param name="Append" value="true" />
		<param name="encoding" value="ms950" />
		<param name="datePattern" value="'_'yyyy-MM-dd'.log'" />
		<param name="Threshold" value="DEBUG" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="%d [%t] | %X{reqURI} | %-20.20c{1} [%-5p] %m%n" />
		</layout>
	</appender>

	<appender name="ASYNC" class="tw.com.iisi.mnm.commons.asyncappender.MnmAsyncAppender">
		<param name="BufferSize" value="10000" />
		<appender-ref ref="SOCKETMONITOR" />
	</appender>

	<category name="org">
		<priority value="ERROR" />
	</category>

	<category name="net">
		<priority value="ERROR" />
	</category>

	<category name="org.springframework.security">
		<priority value="WARN" />
	</category>

	<category name="org.apache.commons">
		<priority value="WARN" />
	</category>

	<category name="org.apache.velocity">
		<priority value="WARN" />
	</category>

	<category name="org.springframework">
		<priority value="WARN" />
	</category>

	<category name="org.springframework.beans.factory">
		<priority value="WARN" />
	</category>

	<category name="org.springframework.beans.factory.support">
		<priority value="WARN" />
	</category>

	<category name="org.springframework.transaction">
		<priority value="WARN" />
	</category>

	<category name="com.mchange.v2">
		<priority value="WARN" />
	</category>

	<root>
		<priority value="DEBUG" />
		<appender-ref ref="ETCH" />
		<appender-ref ref="ASYNC" />
	</root>

</log4j:configuration>
