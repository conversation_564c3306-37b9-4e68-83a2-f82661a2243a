package mon.model;

import java.sql.Timestamp;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * MonQDepthTable.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@TModel(tbName = "MON_QDEPTH")
public class MonQDepth extends AbstractModelMeta {
	protected final static Logger logger = LoggerFactory.getLogger(MonQDepth.class);

	@TKeyField
	private String sysId;

	@TKeyField
	private String schSno;

	@TField
	private int q01_depth;
	@TField
	private int q02_depth;
	@TField
	private int q03_depth;
	@TField
	private int q04_depth;
	@TField
	private int q05_depth;
	@TField
	private int q06_depth;
	@TField
	private int q07_depth;
	@TField
	private int q08_depth;
	@TField
	private int q09_depth;
	@TField
	private int q10_depth;
	@TField
	private int q11_depth;
	@TField
	private int q12_depth;
	@TField
	private int q13_depth;
	@TField
	private int q14_depth;
	@TField
	private int q15_depth;
	@TField
	private int q16_depth;
	@TField
	private int q17_depth;
	@TField
	private int q18_depth;
	@TField
	private int q19_depth;
	@TField
	private int q20_depth;
	@TField
	private int q21_depth;
	@TField
	private int q22_depth;
	@TField
	private int q23_depth;
	@TField
	private int q24_depth;
	@TField
	private int q25_depth;
	@TField
	private int q26_depth;
	@TField
	private int q27_depth;
	@TField
	private int q28_depth;
	@TField
	private int q29_depth;
	@TField
	private int q30_depth;
	@TField
	private int q31_depth;
	@TField
	private int q32_depth;
	@TField
	private int q33_depth;
	@TField
	private int q34_depth;
	@TField
	private int q35_depth;
	@TField
	private int q36_depth;
	@TField
	private int q37_depth;
	@TField
	private int q38_depth;
	@TField
	private int q39_depth;
	@TField
	private int q40_depth;

	@TField
	private Timestamp runTS;

	public void setDepth(String qid, int depth) {
		qid = qid.toLowerCase() + "_depth";
		try {
			BeanUtils.setProperty(this, qid, depth);
		} catch (Exception e) {
			logger.error("[setDepth]EXCEPTOIN!!", e);
		}
	}

	public int getDepth(String qid) {
		qid = qid.toLowerCase() + "_depth";
		try {
			return NumberUtils.toInt(BeanUtils.getSimpleProperty(this, qid), -2);
		} catch (Exception e) {
			logger.error("[setDepth]EXCEPTOIN!!", e);
			return -2;
		}
	}

	/**
	 * Returns the sysId
	 * 
	 * @return the sysId
	 */
	public String getSysId() {
		return this.sysId;
	}

	/**
	 * Sets the sysId
	 * 
	 * @param sysId
	 *            the sysId to set
	 */
	public void setSysId(String sysId) {
		this.sysId = sysId;
	}

	/**
	 * Returns the schSno
	 * 
	 * @return the schSno
	 */
	public String getSchSno() {
		return this.schSno;
	}

	/**
	 * Sets the schSno
	 * 
	 * @param schSno
	 *            the schSno to set
	 */
	public void setSchSno(String schSno) {
		this.schSno = schSno;
	}

	/**
	 * Returns the runTS
	 * 
	 * @return the runTS
	 */
	public Timestamp getRunTS() {
		return runTS;
	}

	/**
	 * Sets the runTS
	 * 
	 * @param runTS
	 *            the runTS to set
	 */
	public void setRunTS(Timestamp runTS) {
		this.runTS = runTS;
	}

	/**
	 * Returns the q01_depth
	 * 
	 * @return the q01_depth
	 */
	public int getQ01_depth() {
		return q01_depth;
	}

	/**
	 * Sets the q01_depth
	 * 
	 * @param q01_depth
	 *            the q01_depth to set
	 */
	public void setQ01_depth(int q01_depth) {
		this.q01_depth = q01_depth;
	}

	/**
	 * Returns the q02_depth
	 * 
	 * @return the q02_depth
	 */
	public int getQ02_depth() {
		return q02_depth;
	}

	/**
	 * Sets the q02_depth
	 * 
	 * @param q02_depth
	 *            the q02_depth to set
	 */
	public void setQ02_depth(int q02_depth) {
		this.q02_depth = q02_depth;
	}

	/**
	 * Returns the q03_depth
	 * 
	 * @return the q03_depth
	 */
	public int getQ03_depth() {
		return q03_depth;
	}

	/**
	 * Sets the q03_depth
	 * 
	 * @param q03_depth
	 *            the q03_depth to set
	 */
	public void setQ03_depth(int q03_depth) {
		this.q03_depth = q03_depth;
	}

	/**
	 * Returns the q04_depth
	 * 
	 * @return the q04_depth
	 */
	public int getQ04_depth() {
		return q04_depth;
	}

	/**
	 * Sets the q04_depth
	 * 
	 * @param q04_depth
	 *            the q04_depth to set
	 */
	public void setQ04_depth(int q04_depth) {
		this.q04_depth = q04_depth;
	}

	/**
	 * Returns the q05_depth
	 * 
	 * @return the q05_depth
	 */
	public int getQ05_depth() {
		return q05_depth;
	}

	/**
	 * Sets the q05_depth
	 * 
	 * @param q05_depth
	 *            the q05_depth to set
	 */
	public void setQ05_depth(int q05_depth) {
		this.q05_depth = q05_depth;
	}

	/**
	 * Returns the q06_depth
	 * 
	 * @return the q06_depth
	 */
	public int getQ06_depth() {
		return q06_depth;
	}

	/**
	 * Sets the q06_depth
	 * 
	 * @param q06_depth
	 *            the q06_depth to set
	 */
	public void setQ06_depth(int q06_depth) {
		this.q06_depth = q06_depth;
	}

	/**
	 * Returns the q07_depth
	 * 
	 * @return the q07_depth
	 */
	public int getQ07_depth() {
		return q07_depth;
	}

	/**
	 * Sets the q07_depth
	 * 
	 * @param q07_depth
	 *            the q07_depth to set
	 */
	public void setQ07_depth(int q07_depth) {
		this.q07_depth = q07_depth;
	}

	/**
	 * Returns the q08_depth
	 * 
	 * @return the q08_depth
	 */
	public int getQ08_depth() {
		return q08_depth;
	}

	/**
	 * Sets the q08_depth
	 * 
	 * @param q08_depth
	 *            the q08_depth to set
	 */
	public void setQ08_depth(int q08_depth) {
		this.q08_depth = q08_depth;
	}

	/**
	 * Returns the q09_depth
	 * 
	 * @return the q09_depth
	 */
	public int getQ09_depth() {
		return q09_depth;
	}

	/**
	 * Sets the q09_depth
	 * 
	 * @param q09_depth
	 *            the q09_depth to set
	 */
	public void setQ09_depth(int q09_depth) {
		this.q09_depth = q09_depth;
	}

	/**
	 * Returns the q10_depth
	 * 
	 * @return the q10_depth
	 */
	public int getQ10_depth() {
		return q10_depth;
	}

	/**
	 * Sets the q10_depth
	 * 
	 * @param q10_depth
	 *            the q10_depth to set
	 */
	public void setQ10_depth(int q10_depth) {
		this.q10_depth = q10_depth;
	}

	/**
	 * Returns the q11_depth
	 * 
	 * @return the q11_depth
	 */
	public int getQ11_depth() {
		return q11_depth;
	}

	/**
	 * Sets the q11_depth
	 * 
	 * @param q11_depth
	 *            the q11_depth to set
	 */
	public void setQ11_depth(int q11_depth) {
		this.q11_depth = q11_depth;
	}

	/**
	 * Returns the q12_depth
	 * 
	 * @return the q12_depth
	 */
	public int getQ12_depth() {
		return q12_depth;
	}

	/**
	 * Sets the q12_depth
	 * 
	 * @param q12_depth
	 *            the q12_depth to set
	 */
	public void setQ12_depth(int q12_depth) {
		this.q12_depth = q12_depth;
	}

	/**
	 * Returns the q13_depth
	 * 
	 * @return the q13_depth
	 */
	public int getQ13_depth() {
		return q13_depth;
	}

	/**
	 * Sets the q13_depth
	 * 
	 * @param q13_depth
	 *            the q13_depth to set
	 */
	public void setQ13_depth(int q13_depth) {
		this.q13_depth = q13_depth;
	}

	/**
	 * Returns the q14_depth
	 * 
	 * @return the q14_depth
	 */
	public int getQ14_depth() {
		return q14_depth;
	}

	/**
	 * Sets the q14_depth
	 * 
	 * @param q14_depth
	 *            the q14_depth to set
	 */
	public void setQ14_depth(int q14_depth) {
		this.q14_depth = q14_depth;
	}

	/**
	 * Returns the q15_depth
	 * 
	 * @return the q15_depth
	 */
	public int getQ15_depth() {
		return q15_depth;
	}

	/**
	 * Sets the q15_depth
	 * 
	 * @param q15_depth
	 *            the q15_depth to set
	 */
	public void setQ15_depth(int q15_depth) {
		this.q15_depth = q15_depth;
	}

	/**
	 * Returns the q16_depth
	 * 
	 * @return the q16_depth
	 */
	public int getQ16_depth() {
		return q16_depth;
	}

	/**
	 * Sets the q16_depth
	 * 
	 * @param q16_depth
	 *            the q16_depth to set
	 */
	public void setQ16_depth(int q16_depth) {
		this.q16_depth = q16_depth;
	}

	/**
	 * Returns the q17_depth
	 * 
	 * @return the q17_depth
	 */
	public int getQ17_depth() {
		return q17_depth;
	}

	/**
	 * Sets the q17_depth
	 * 
	 * @param q17_depth
	 *            the q17_depth to set
	 */
	public void setQ17_depth(int q17_depth) {
		this.q17_depth = q17_depth;
	}

	/**
	 * Returns the q18_depth
	 * 
	 * @return the q18_depth
	 */
	public int getQ18_depth() {
		return q18_depth;
	}

	/**
	 * Sets the q18_depth
	 * 
	 * @param q18_depth
	 *            the q18_depth to set
	 */
	public void setQ18_depth(int q18_depth) {
		this.q18_depth = q18_depth;
	}

	/**
	 * Returns the q19_depth
	 * 
	 * @return the q19_depth
	 */
	public int getQ19_depth() {
		return q19_depth;
	}

	/**
	 * Sets the q19_depth
	 * 
	 * @param q19_depth
	 *            the q19_depth to set
	 */
	public void setQ19_depth(int q19_depth) {
		this.q19_depth = q19_depth;
	}

	/**
	 * Returns the q20_depth
	 * 
	 * @return the q20_depth
	 */
	public int getQ20_depth() {
		return q20_depth;
	}

	/**
	 * Sets the q20_depth
	 * 
	 * @param q20_depth
	 *            the q20_depth to set
	 */
	public void setQ20_depth(int q20_depth) {
		this.q20_depth = q20_depth;
	}

	/**
	 * Returns the q21_depth
	 * 
	 * @return the q21_depth
	 */
	public int getQ21_depth() {
		return q21_depth;
	}

	/**
	 * Sets the q21_depth
	 * 
	 * @param q21_depth
	 *            the q21_depth to set
	 */
	public void setQ21_depth(int q21_depth) {
		this.q21_depth = q21_depth;
	}

	/**
	 * Returns the q22_depth
	 * 
	 * @return the q22_depth
	 */
	public int getQ22_depth() {
		return q22_depth;
	}

	/**
	 * Sets the q22_depth
	 * 
	 * @param q22_depth
	 *            the q22_depth to set
	 */
	public void setQ22_depth(int q22_depth) {
		this.q22_depth = q22_depth;
	}

	/**
	 * Returns the q23_depth
	 * 
	 * @return the q23_depth
	 */
	public int getQ23_depth() {
		return q23_depth;
	}

	/**
	 * Sets the q23_depth
	 * 
	 * @param q23_depth
	 *            the q23_depth to set
	 */
	public void setQ23_depth(int q23_depth) {
		this.q23_depth = q23_depth;
	}

	/**
	 * Returns the q24_depth
	 * 
	 * @return the q24_depth
	 */
	public int getQ24_depth() {
		return q24_depth;
	}

	/**
	 * Sets the q24_depth
	 * 
	 * @param q24_depth
	 *            the q24_depth to set
	 */
	public void setQ24_depth(int q24_depth) {
		this.q24_depth = q24_depth;
	}

	/**
	 * Returns the q25_depth
	 * 
	 * @return the q25_depth
	 */
	public int getQ25_depth() {
		return q25_depth;
	}

	/**
	 * Sets the q25_depth
	 * 
	 * @param q25_depth
	 *            the q25_depth to set
	 */
	public void setQ25_depth(int q25_depth) {
		this.q25_depth = q25_depth;
	}

	/**
	 * Returns the q26_depth
	 * 
	 * @return the q26_depth
	 */
	public int getQ26_depth() {
		return q26_depth;
	}

	/**
	 * Sets the q26_depth
	 * 
	 * @param q26_depth
	 *            the q26_depth to set
	 */
	public void setQ26_depth(int q26_depth) {
		this.q26_depth = q26_depth;
	}

	/**
	 * Returns the q27_depth
	 * 
	 * @return the q27_depth
	 */
	public int getQ27_depth() {
		return q27_depth;
	}

	/**
	 * Sets the q27_depth
	 * 
	 * @param q27_depth
	 *            the q27_depth to set
	 */
	public void setQ27_depth(int q27_depth) {
		this.q27_depth = q27_depth;
	}

	/**
	 * Returns the q28_depth
	 * 
	 * @return the q28_depth
	 */
	public int getQ28_depth() {
		return q28_depth;
	}

	/**
	 * Sets the q28_depth
	 * 
	 * @param q28_depth
	 *            the q28_depth to set
	 */
	public void setQ28_depth(int q28_depth) {
		this.q28_depth = q28_depth;
	}

	/**
	 * Returns the q29_depth
	 * 
	 * @return the q29_depth
	 */
	public int getQ29_depth() {
		return q29_depth;
	}

	/**
	 * Sets the q29_depth
	 * 
	 * @param q29_depth
	 *            the q29_depth to set
	 */
	public void setQ29_depth(int q29_depth) {
		this.q29_depth = q29_depth;
	}

	/**
	 * Returns the q30_depth
	 * 
	 * @return the q30_depth
	 */
	public int getQ30_depth() {
		return q30_depth;
	}

	/**
	 * Sets the q30_depth
	 * 
	 * @param q30_depth
	 *            the q30_depth to set
	 */
	public void setQ30_depth(int q30_depth) {
		this.q30_depth = q30_depth;
	}

	/**
	 * Returns the q31_depth
	 * 
	 * @return the q31_depth
	 */
	public int getQ31_depth() {
		return q31_depth;
	}

	/**
	 * Sets the q31_depth
	 * 
	 * @param q31_depth
	 *            the q31_depth to set
	 */
	public void setQ31_depth(int q31_depth) {
		this.q31_depth = q31_depth;
	}

	/**
	 * Returns the q32_depth
	 * 
	 * @return the q32_depth
	 */
	public int getQ32_depth() {
		return q32_depth;
	}

	/**
	 * Sets the q32_depth
	 * 
	 * @param q32_depth
	 *            the q32_depth to set
	 */
	public void setQ32_depth(int q32_depth) {
		this.q32_depth = q32_depth;
	}

	/**
	 * Returns the q33_depth
	 * 
	 * @return the q33_depth
	 */
	public int getQ33_depth() {
		return q33_depth;
	}

	/**
	 * Sets the q33_depth
	 * 
	 * @param q33_depth
	 *            the q33_depth to set
	 */
	public void setQ33_depth(int q33_depth) {
		this.q33_depth = q33_depth;
	}

	/**
	 * Returns the q34_depth
	 * 
	 * @return the q34_depth
	 */
	public int getQ34_depth() {
		return q34_depth;
	}

	/**
	 * Sets the q34_depth
	 * 
	 * @param q34_depth
	 *            the q34_depth to set
	 */
	public void setQ34_depth(int q34_depth) {
		this.q34_depth = q34_depth;
	}

	/**
	 * Returns the q35_depth
	 * 
	 * @return the q35_depth
	 */
	public int getQ35_depth() {
		return q35_depth;
	}

	/**
	 * Sets the q35_depth
	 * 
	 * @param q35_depth
	 *            the q35_depth to set
	 */
	public void setQ35_depth(int q35_depth) {
		this.q35_depth = q35_depth;
	}

	/**
	 * Returns the q36_depth
	 * 
	 * @return the q36_depth
	 */
	public int getQ36_depth() {
		return q36_depth;
	}

	/**
	 * Sets the q36_depth
	 * 
	 * @param q36_depth
	 *            the q36_depth to set
	 */
	public void setQ36_depth(int q36_depth) {
		this.q36_depth = q36_depth;
	}

	/**
	 * Returns the q37_depth
	 * 
	 * @return the q37_depth
	 */
	public int getQ37_depth() {
		return q37_depth;
	}

	/**
	 * Sets the q37_depth
	 * 
	 * @param q37_depth
	 *            the q37_depth to set
	 */
	public void setQ37_depth(int q37_depth) {
		this.q37_depth = q37_depth;
	}

	/**
	 * Returns the q38_depth
	 * 
	 * @return the q38_depth
	 */
	public int getQ38_depth() {
		return q38_depth;
	}

	/**
	 * Sets the q38_depth
	 * 
	 * @param q38_depth
	 *            the q38_depth to set
	 */
	public void setQ38_depth(int q38_depth) {
		this.q38_depth = q38_depth;
	}

	/**
	 * Returns the q39_depth
	 * 
	 * @return the q39_depth
	 */
	public int getQ39_depth() {
		return q39_depth;
	}

	/**
	 * Sets the q39_depth
	 * 
	 * @param q39_depth
	 *            the q39_depth to set
	 */
	public void setQ39_depth(int q39_depth) {
		this.q39_depth = q39_depth;
	}

	/**
	 * Returns the q40_depth
	 * 
	 * @return the q40_depth
	 */
	public int getQ40_depth() {
		return q40_depth;
	}

	/**
	 * Sets the q40_depth
	 * 
	 * @param q40_depth
	 *            the q40_depth to set
	 */
	public void setQ40_depth(int q40_depth) {
		this.q40_depth = q40_depth;
	}

}
