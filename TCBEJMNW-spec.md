# TCBEJMNW 專案詳細規格文件

## 專案概述

TCBEJMNW (Taiwan Cooperative Bank - Electronic Journal Monitoring Network Web) 是系統監控平台，主要負責監控和管理 TCBJCICW、TCBETCHW 和 ELOAN 等相關系統的運行狀態。

### 主要功能
- **系統資源監控**: CPU、記憶體、磁碟使用率監控
- **模組狀態監控**: 各系統模組運行狀態監控
- **佇列深度監控**: MQ佇列深度和狀態監控
- **日誌查詢系統**: 多系統日誌查詢和分析
- **排程任務管理**: 自動化監控任務排程

## 技術架構

### 技術棧
- **JDK**: 1.6
- **Web框架**: Spring MVC 2.x
- **建置工具**: Maven 3.2.5
- **Web容器**: Jetty 8.1.8
- **資料庫**: IBM DB2 (多資料庫連線)
- **訊息佇列**: IBM MQ
- **排程框架**: Quartz Scheduler
- **前端**: JSP + JavaScript + CSS

### 專案結構

```
TCBEJMNW/
├── src/main/java/mon/
│   ├── dao/                    # 資料存取層
│   ├── exception/              # 例外處理
│   ├── jdbc/                   # JDBC工具
│   ├── model/                  # 資料模型
│   ├── msg/                    # 訊息處理
│   ├── schedule/               # 排程任務
│   ├── service/                # 服務層
│   ├── utils/                  # 工具類別
│   └── web/                    # Web層
│       ├── constant/           # 常數定義
│       ├── core/               # 核心框架
│       ├── filter/             # 過濾器
│       ├── handler/            # 請求處理器
│       ├── model/              # Web模型
│       └── service/            # Web服務
├── src/main/resources/
│   ├── spring/                 # Spring配置
│   │   ├── jdbc-ejmn.xml       # EJMN資料庫配置
│   │   ├── jdbc-ejcic.xml      # EJCIC資料庫配置
│   │   ├── jdbc-etch.xml       # ETCH資料庫配置
│   │   ├── jdbc-stj.xml        # STJ資料庫配置
│   │   ├── jdbc-eloan.xml      # ELOAN資料庫配置
│   │   └── schedule.xml        # 排程配置
│   └── ejmn.properties         # 主配置檔
└── WebContent/                 # Web資源
    ├── WEB-INF/
    ├── jsp/                    # JSP頁面
    ├── js/                     # JavaScript
    └── css/                    # 樣式表
```

## 核心架構設計

### 監控系統架構

```mermaid
graph TB
    subgraph "監控目標系統"
        EJCICW[TCBJCICW Web]
        EJCICD[TCBJCICW Dispatcher]
        EJCICG[TCBJCICW Gateway]
        ETCHW[TCBETCHW Web]
        ETCHD[TCBETCHW Dispatcher]
        ETCHG[TCBETCHW Gateway]
        ELOAN[ELOAN System]
    end
    
    subgraph "監控平台 (TCBEJMNW)"
        WEB[Web監控介面]
        HANDLER[監控Handler]
        SERVICE[監控服務層]
        SCHEDULER[排程任務]
        DAO[多資料庫DAO]
    end
    
    subgraph "資料儲存"
        EJMNDB[(EJMN監控資料庫)]
        EJCICDB[(EJCIC業務資料庫)]
        ETCHDB[(ETCH業務資料庫)]
        STJDB[(STJ資料庫)]
        ELOANDB[(ELOAN資料庫)]
    end
    
    WEB --> HANDLER
    HANDLER --> SERVICE
    SERVICE --> DAO
    SCHEDULER --> SERVICE
    
    SERVICE -.-> EJCICW
    SERVICE -.-> EJCICD
    SERVICE -.-> EJCICG
    SERVICE -.-> ETCHW
    SERVICE -.-> ETCHD
    SERVICE -.-> ETCHG
    SERVICE -.-> ELOAN
    
    DAO --> EJMNDB
    DAO --> EJCICDB
    DAO --> ETCHDB
    DAO --> STJDB
    DAO --> ELOANDB
```

### 監控Handler架構

```mermaid
classDiagram
    class AbstractHandler {
        <<abstract>>
        +WebView action(WebContext, Map)
        +String getErrorBackUrl()
        #Logger logger
    }
    
    class MONEJ01Handler {
        +WebView action(WebContext, Map)
        -MonQDepthService qDepthService
    }
    
    class MONEJ06Handler {
        +WebView action(WebContext, Map)
        -MONEJ00Service monej00Service
    }
    
    class MONEJ07Handler {
        +WebView action(WebContext, Map)
        -MonSysUsageService sysUsageService
    }
    
    class MONTJ01Handler {
        +WebView action(WebContext, Map)
        +WebView query(WebContext, Map)
        +WebView findResult(WebContext, Map)
        -MONTJ00Service montj00Service
    }
    
    class MONEL01Handler {
        +WebView action(WebContext, Map)
        -MONEL00Service monel00Service
    }
    
    AbstractHandler <|-- MONEJ01Handler
    AbstractHandler <|-- MONEJ06Handler
    AbstractHandler <|-- MONEJ07Handler
    AbstractHandler <|-- MONTJ01Handler
    AbstractHandler <|-- MONEL01Handler
```

## 主要監控模組

### 1. 系統資源監控模組

#### 監控指標
- **CPU使用率**: AP伺服器和DB伺服器CPU使用率
- **記憶體使用**: Web、Dispatcher、Gateway記憶體使用情況
- **系統狀態**: vmstat系統統計資訊

#### 資料模型
```java
@TModel(tbName = "MON_SYSUSAGE")
public class MonSysUsage extends AbstractModelMeta {
    
    @TKeyField
    private String sysId;           // 系統ID
    
    @TKeyField
    private String schSno;          // 排程序號
    
    @TKeyField
    private String hostId;          // 主機ID
    
    @TField
    private int cpu_ap;             // AP伺服器CPU使用率
    
    @TField
    private int cpu_db;             // DB伺服器CPU使用率
    
    @TField
    private int mem_web;            // Web記憶體使用量
    
    @TField
    private int mem_web_max;        // Web記憶體最大值
    
    @TField
    private int mem_disp1;          // Dispatcher1記憶體使用量
    
    @TField
    private int mem_disp1_max;      // Dispatcher1記憶體最大值
    
    @TField
    private int mem_gw;             // Gateway記憶體使用量
    
    @TField
    private int mem_gw_max;         // Gateway記憶體最大值
    
    @TField
    private Timestamp runts;        // 執行時間
    
    @TField
    private String vmstat_ap;       // AP伺服器vmstat
    
    @TField
    private String vmstat_db;       // DB伺服器vmstat
}
```

#### 監控服務實作
```java
@Service("monSysUsageService")
public class MonSysUsageServiceImpl implements MonSysUsageService {
    
    @Override
    public MonSysUsage doEJCICWebSysUsage() {
        // 透過MQ或Agent取得EJCIC Web系統使用狀況
        MonSysUsage usage = new MonSysUsage();
        usage.setSysId("EJCICW");
        usage.setHostId(getHostId());
        
        // 取得CPU使用率
        usage.setCpu_ap(getCpuUsage("EJCIC_AP"));
        usage.setCpu_db(getCpuUsage("EJCIC_DB"));
        
        // 取得記憶體使用情況
        usage.setMem_web(getMemoryUsage("WEB"));
        usage.setMem_web_max(getMaxMemory("WEB"));
        
        // 取得vmstat資訊
        usage.setVmstat_ap(getVmstat("AP"));
        usage.setVmstat_db(getVmstat("DB"));
        
        return usage;
    }
}
```

### 2. 佇列深度監控模組

#### 佇列監控架構
```mermaid
classDiagram
    class MonQDepth {
        +String sysId
        +String schSno
        +int q01_depth
        +int q02_depth
        +int q03_depth
        +int q40_depth
        +Timestamp runTS
        +void setDepth(String qid, int depth)
    }
    
    class MONEJ01VO {
        +boolean ok
        +String qid
        +String qname
        +String qdesc
        +String depth
        +String color
        +String getColorByDepth()
    }
    
    class QueueMonitorService {
        +MonQDepth getQueueDepth(String sysId)
        +List~MONEJ01VO~ getQueueStatus(String sysId)
        +void updateQueueDepth(String sysId, String queueId, int depth)
    }
    
    MonQDepth --> MONEJ01VO
    QueueMonitorService --> MonQDepth
```

#### 佇列深度告警機制
```java
public class MONEJ01VO {
    
    public String getColorByDepth() {
        int depthValue = NumberUtils.toInt(depth, 0);
        
        if (depthValue == 0) {
            return "green";     // 正常
        } else if (depthValue < 100) {
            return "yellow";    // 警告
        } else if (depthValue < 500) {
            return "orange";    // 注意
        } else {
            return "red";       // 危險
        }
    }
}
```

### 3. 日誌查詢系統

#### 支援的日誌類型
- **EJCIC日誌**: 聯徵中心交易日誌
- **STJ日誌**: 報送處理日誌
- **ELOAN日誌**: 放款系統作業日誌

#### 日誌查詢Handler
```java
@Controller
@AuthRole(role = {WebUserProfile.ROLE_MONITOR})
public class MONEJ06Handler extends AbstractHandler {
    
    @Resource
    private MONEJ00Service monej00Service;
    
    @Override
    public WebView action(WebContext context, Map<String, String> paramMap) 
            throws AppException {
        
        // 建立查詢條件
        EJCICLogFile input = new EJCICLogFile();
        input.setDate(parseDate(paramMap.get("date")));
        input.setTxid(paramMap.get("txId"));
        input.setMsgid(paramMap.get("msgId"));
        input.setProductid(paramMap.get("productId"));
        input.setDivision(paramMap.get("division"));
        input.setQuerykey1(paramMap.get("querykey1"));
        input.setRc(paramMap.get("rc"));
        
        // 執行查詢
        MONEJ06VO vo = new MONEJ06VO();
        vo.setInput(input);
        vo = monej00Service.doEJ06(vo);
        
        return WebView.forward("/mon/MONEJ06R.jsp")
                     .requestAttr("monej06data", vo);
    }
}
```

#### 日誌資料模型
```java
@TModel(tbName = "EJCIC_LOG_FILE")
public class EJCICLogFile extends AbstractModelMeta {
    
    @TKeyField
    @TField(columnName = "DATE_YYYYMMDD")
    private Date date;
    
    @TKeyField
    @TField(columnName = "TXID")
    private String txid;
    
    @TKeyField
    @TField(columnName = "MSGID")
    private String msgid;
    
    @TField(columnName = "PRODUCTID")
    private String productid;
    
    @TField(columnName = "REQUESTID")
    private String requestid;
    
    @TField(columnName = "DIVISION")
    private String division;
    
    @TField(columnName = "QUERYKEY1")
    private String querykey1;
    
    @TField(columnName = "QUERYKEY2")
    private String querykey2;
    
    @TField(columnName = "RC")
    private String rc;
    
    @TField(columnName = "TOJCIC")
    private String tojcic;
}
```

### 4. 排程任務系統

#### Quartz排程配置
```xml
<bean id="schedulerFactoryBean" 
      class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
    <property name="quartzProperties">
        <props>
            <prop key="org.quartz.threadPool.class">
                org.quartz.simpl.SimpleThreadPool
            </prop>
            <prop key="org.quartz.threadPool.threadCount">3</prop>
        </props>
    </property>
    <property name="triggers">
        <list>
            <ref bean="sysUsageTrigger" />
            <ref bean="queueDepthTrigger" />
            <ref bean="houseKeepingTrigger" />
            <ref bean="stjMailCheckerTrigger" />
        </list>
    </property>
</bean>
```

#### 主要排程任務
1. **系統使用狀況監控**: 每5分鐘執行
2. **佇列深度監控**: 每1分鐘執行
3. **資料清理**: 每日凌晨執行
4. **郵件檢查**: 每30分鐘執行

#### 排程任務實作
```java
@Component
public class SysUsageMonitorJob implements Job {
    
    @Resource
    private MonSysUsageService monSysUsageService;
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            // 監控EJCIC系統
            MonSysUsage ejcicUsage = monSysUsageService.doEJCICWebSysUsage();
            saveMonitorData(ejcicUsage);
            
            // 監控ETCH系統
            MonSysUsage etchUsage = monSysUsageService.doETCHWebSysUsage();
            saveMonitorData(etchUsage);
            
            // 監控ELOAN系統
            MonSysUsage eloanUsage = monSysUsageService.doEloanDispSysUsage();
            saveMonitorData(eloanUsage);
            
        } catch (Exception e) {
            logger.error("系統使用狀況監控失敗", e);
            throw new JobExecutionException(e);
        }
    }
}
```

## 多資料庫架構

### 資料庫連線配置
系統支援多個資料庫連線，分別存取不同系統的資料：

```xml
<!-- EJMN監控資料庫 -->
<bean id="ejmnDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
    <property name="jndiName" value="java:comp/env/jdbc/EJMN"/>
</bean>

<!-- EJCIC業務資料庫 -->
<bean id="ejcicDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
    <property name="jndiName" value="java:comp/env/jdbc/EJCIC"/>
</bean>

<!-- ETCH業務資料庫 -->
<bean id="etchDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
    <property name="jndiName" value="java:comp/env/jdbc/ETCH"/>
</bean>

<!-- STJ資料庫 -->
<bean id="stjDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
    <property name="jndiName" value="java:comp/env/jdbc/STJ"/>
</bean>

<!-- ELOAN資料庫 -->
<bean id="eloanDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
    <property name="jndiName" value="java:comp/env/jdbc/ELOAN"/>
</bean>
```

### 多資料庫DAO實作
```java
@Repository
public class MultiDatabaseDAO {
    
    @Resource
    @Qualifier("ejmnJdbcTemplate")
    private JdbcTemplate ejmnJdbcTemplate;
    
    @Resource
    @Qualifier("ejcicJdbcTemplate")
    private JdbcTemplate ejcicJdbcTemplate;
    
    @Resource
    @Qualifier("etchJdbcTemplate")
    private JdbcTemplate etchJdbcTemplate;
    
    public List<EJCICLogFile> queryEJCICLogs(EJCICLogFile criteria) {
        return ejcicJdbcTemplate.query(buildEJCICLogSQL(criteria), 
                                     new EJCICLogFileRowMapper());
    }
    
    public List<STJLogFile> querySTJLogs(STJLogFile criteria) {
        return stjJdbcTemplate.query(buildSTJLogSQL(criteria), 
                                   new STJLogFileRowMapper());
    }
}
```

## 監控介面設計

### 監控儀表板
```mermaid
graph TD
    A[監控首頁] --> B[系統資源監控]
    A --> C[佇列深度監控]
    A --> D[模組狀態監控]
    A --> E[日誌查詢]
    
    B --> B1[CPU使用率圖表]
    B --> B2[記憶體使用率圖表]
    B --> B3[系統負載趨勢]
    
    C --> C1[佇列深度即時顯示]
    C --> C2[佇列狀態告警]
    C --> C3[歷史趨勢分析]
    
    D --> D1[Web模組狀態]
    D --> D2[Dispatcher狀態]
    D --> D3[Gateway狀態]
    
    E --> E1[EJCIC日誌查詢]
    E --> E2[ETCH日誌查詢]
    E --> E3[ELOAN日誌查詢]
```

### 告警機制
1. **即時告警**: 佇列深度超過閾值時即時顯示
2. **顏色編碼**: 綠色(正常)、黃色(警告)、橙色(注意)、紅色(危險)
3. **歷史記錄**: 保存告警歷史記錄供分析
4. **郵件通知**: 嚴重告警時發送郵件通知

## 安全與權限控制

### 角色定義
```java
public class WebUserProfile {
    public static final String ROLE_MONITOR = "MONITOR";
    public static final String ROLE_TOJCIC = "TOJCIC";
    public static final String ROLE_ADMIN = "ADMIN";
}
```

### 權限控制註解
```java
@AuthRole(role = {WebUserProfile.ROLE_MONITOR, WebUserProfile.ROLE_ADMIN})
public class MONEJ07Handler extends AbstractHandler {
    // 只有監控人員和管理員可以存取
}
```

## 部署與配置

### 系統配置
- **Web容器**: Jetty 8.1.8
- **JVM參數**: -Xms512m -Xmx1024m
- **排程執行緒**: 3個執行緒
- **資料庫連線**: 每個資料庫最大20個連線
- **監控頻率**: 系統資源5分鐘、佇列深度1分鐘
