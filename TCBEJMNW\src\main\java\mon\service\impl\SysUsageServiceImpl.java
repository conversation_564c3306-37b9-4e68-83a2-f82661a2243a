package mon.service.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;

import mon.service.SysUsageService;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <pre>
 * SysUsageServiceImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Service("sysUsageService")
public class SysUsageServiceImpl implements SysUsageService {
	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	private static final long MEGABYTE = 1024L * 1024L;

	@Override
	public long[] getMemUsage() {
		MemoryMXBean mem = ManagementFactory.getMemoryMXBean();
		MemoryUsage memUsage = mem.getHeapMemoryUsage();
		return new long[] { memUsage.getUsed() / MEGABYTE, memUsage.getCommitted() / MEGABYTE };
	}

	@Override
	public int getCPU(String vmstatStr) {
		// r b avm fre re pi po fr sr cy in sy cs us sy id wa
		// String vmstatStr = "1  3 113726  124   0  14   6 151  600   0 521 5533 816  23 13  7  57";

		String vmstat = this.getVmstatRow(vmstatStr);
		logger.info("[getCPU]vmstat=[{}]", vmstat);

		int cpuval = 0;
		String[] cols = StringUtils.split(vmstat, " ");
		// 13,14,16
		try {
			if (cols.length > 16) {
				cpuval += NumberUtils.toInt(cols[13]); // user
				cpuval += NumberUtils.toInt(cols[14]); // sys
				cpuval += NumberUtils.toInt(cols[16]); // wait
			} else {
				cpuval = -7;
			}
		} catch (Exception ex) {
			logger.error("[getCPU]vmstat=" + vmstat, ex);
			cpuval = -1;
		}
		return cpuval;
	}

	@Override
	public int getCPU() {
		return this.getCPU(this.getVmstat());
	}

	@Override
	public int getJavaThreadSize() {
		return ManagementFactory.getThreadMXBean().getThreadCount();
	}

	@Override
	public int getJavaThreadSize(String prefixTN) {
		ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
		long[] allThreadIds = threadBean.getAllThreadIds();
		int count = 0;
		for (long tid : allThreadIds) {
			ThreadInfo tinfo = threadBean.getThreadInfo(tid);
			if (tinfo != null) {
				logger.debug("[getJavaThreadSize]{}", tinfo);
				if (tinfo.getThreadName().startsWith(prefixTN)) {
					count++;
				}
			}
		}
		return count;
	}

	@Override
	public String getVmstat() {
		String s = new String();
		try {
			Process p = Runtime.getRuntime().exec("vmstat -t 1 1");
			InputStreamReader ir = new InputStreamReader(p.getInputStream());
			BufferedReader br = new BufferedReader(ir);
			StringBuffer str = new StringBuffer();
			String line;
			int i = 0;
			while ((line = br.readLine()) != null && i++ < 10) {
				str.append(line).append("\n");
			}
			s = str.toString();
		} catch (IOException e) {
			s = e.getLocalizedMessage();
		}

		if (s.getBytes().length > 1000) {
			s = new String(s.getBytes(), 0, 1000);
		}
		logger.debug("[getVmstat]vmstat=[{}]", s);
		return this.getVmstatRow(s);
	}

	private String getVmstatRow(String vmstatStr) {
		String vmstat = vmstatStr;
		String[] lines = StringUtils.split(vmstatStr, "\n");
		if (lines.length > 1) {
			boolean getNextLine = false;

			for (String line : lines) {
				if (getNextLine) {
					vmstat = line;
					break;
				} else if (line.indexOf("avm") != -1 && line.indexOf("fre") != -1) {
					getNextLine = true;
				}
			}
		}
		return vmstat;
	}

}
