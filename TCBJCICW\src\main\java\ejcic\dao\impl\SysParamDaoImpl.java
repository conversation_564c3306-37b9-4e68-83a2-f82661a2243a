package ejcic.dao.impl;

import org.springframework.stereotype.Repository;

import ejcic.dao.SysParamDao;
import ejcic.model.SysParam;

/**
 * <pre>
 * ParaDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("sysParamDao")
public class SysParamDaoImpl extends AbstractGenericDao<SysParam> implements SysParamDao {

	@Override
	public int updateParamValue(String param, String paramValue) {
		String sql = "UPDATE SYSPARAM SET PARAMVALUE=? WHERE PARAM=?";
		return this.getJdbc().update(sql, new String[] { paramValue, param });
	}

}