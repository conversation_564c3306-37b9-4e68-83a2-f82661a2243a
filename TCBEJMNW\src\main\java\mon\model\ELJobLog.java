package mon.model;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * <pre>
 * ELJobLog.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class ELJobLog {
	private String jmName;
	private int seq = -1;
	private String tbName;
	private String type;
	private Date date;
	private String rc;
	private Timestamp sTime;
	private Timestamp eTime;
	private BigDecimal readCnt;
	private BigDecimal skippCnt;
	private BigDecimal loadCnt;
	private BigDecimal rejCnt;
	private BigDecimal delCnt;
	private BigDecimal commCnt;
	private String reMark;

	private Date dateEnd;

	/**
	 * Returns the jmName
	 * 
	 * @return the jmName
	 */
	public String getJmName() {
		return jmName;
	}

	/**
	 * Sets the jmName
	 * 
	 * @param jmName
	 *            the jmName to set
	 */
	public void setJmName(String jmName) {
		this.jmName = jmName;
	}

	/**
	 * Returns the seq
	 * 
	 * @return the seq
	 */
	public int getSeq() {
		return seq;
	}

	/**
	 * Sets the seq
	 * 
	 * @param seq
	 *            the seq to set
	 */
	public void setSeq(int seq) {
		this.seq = seq;
	}

	/**
	 * Returns the tbName
	 * 
	 * @return the tbName
	 */
	public String getTbName() {
		return tbName;
	}

	/**
	 * Sets the tbName
	 * 
	 * @param tbName
	 *            the tbName to set
	 */
	public void setTbName(String tbName) {
		this.tbName = tbName;
	}

	/**
	 * Returns the type
	 * 
	 * @return the type
	 */
	public String getType() {
		return type;
	}

	/**
	 * Sets the type
	 * 
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}

	/**
	 * Returns the date
	 * 
	 * @return the date
	 */
	public Date getDate() {
		return date;
	}

	/**
	 * Sets the date
	 * 
	 * @param date
	 *            the date to set
	 */
	public void setDate(Date date) {
		this.date = date;
	}

	/**
	 * Returns the rc
	 * 
	 * @return the rc
	 */
	public String getRc() {
		return rc;
	}

	/**
	 * Sets the rc
	 * 
	 * @param rc
	 *            the rc to set
	 */
	public void setRc(String rc) {
		this.rc = rc;
	}

	/**
	 * Returns the sTime
	 * 
	 * @return the sTime
	 */
	public Timestamp getsTime() {
		return sTime;
	}

	/**
	 * Sets the sTime
	 * 
	 * @param sTime
	 *            the sTime to set
	 */
	public void setsTime(Timestamp sTime) {
		this.sTime = sTime;
	}

	/**
	 * Returns the eTime
	 * 
	 * @return the eTime
	 */
	public Timestamp geteTime() {
		return eTime;
	}

	/**
	 * Sets the eTime
	 * 
	 * @param eTime
	 *            the eTime to set
	 */
	public void seteTime(Timestamp eTime) {
		this.eTime = eTime;
	}

	/**
	 * Returns the readCnt
	 * 
	 * @return the readCnt
	 */
	public BigDecimal getReadCnt() {
		return readCnt;
	}

	/**
	 * Sets the readCnt
	 * 
	 * @param readCnt
	 *            the readCnt to set
	 */
	public void setReadCnt(BigDecimal readCnt) {
		this.readCnt = readCnt;
	}

	/**
	 * Returns the skippCnt
	 * 
	 * @return the skippCnt
	 */
	public BigDecimal getSkippCnt() {
		return skippCnt;
	}

	/**
	 * Sets the skippCnt
	 * 
	 * @param skippCnt
	 *            the skippCnt to set
	 */
	public void setSkippCnt(BigDecimal skippCnt) {
		this.skippCnt = skippCnt;
	}

	/**
	 * Returns the loadCnt
	 * 
	 * @return the loadCnt
	 */
	public BigDecimal getLoadCnt() {
		return loadCnt;
	}

	/**
	 * Sets the loadCnt
	 * 
	 * @param loadCnt
	 *            the loadCnt to set
	 */
	public void setLoadCnt(BigDecimal loadCnt) {
		this.loadCnt = loadCnt;
	}

	/**
	 * Returns the rejCnt
	 * 
	 * @return the rejCnt
	 */
	public BigDecimal getRejCnt() {
		return rejCnt;
	}

	/**
	 * Sets the rejCnt
	 * 
	 * @param rejCnt
	 *            the rejCnt to set
	 */
	public void setRejCnt(BigDecimal rejCnt) {
		this.rejCnt = rejCnt;
	}

	/**
	 * Returns the delCnt
	 * 
	 * @return the delCnt
	 */
	public BigDecimal getDelCnt() {
		return delCnt;
	}

	/**
	 * Sets the delCnt
	 * 
	 * @param delCnt
	 *            the delCnt to set
	 */
	public void setDelCnt(BigDecimal delCnt) {
		this.delCnt = delCnt;
	}

	/**
	 * Returns the commCnt
	 * 
	 * @return the commCnt
	 */
	public BigDecimal getCommCnt() {
		return commCnt;
	}

	/**
	 * Sets the commCnt
	 * 
	 * @param commCnt
	 *            the commCnt to set
	 */
	public void setCommCnt(BigDecimal commCnt) {
		this.commCnt = commCnt;
	}

	/**
	 * Returns the reMark
	 * 
	 * @return the reMark
	 */
	public String getReMark() {
		return reMark;
	}

	/**
	 * Sets the reMark
	 * 
	 * @param reMark
	 *            the reMark to set
	 */
	public void setReMark(String reMark) {
		this.reMark = reMark;
	}

	/**
	 * Returns the dateEnd
	 * 
	 * @return the dateEnd
	 */
	public Date getDateEnd() {
		return dateEnd;
	}

	/**
	 * Sets the dateEnd
	 * 
	 * @param dateEnd
	 *            the dateEnd to set
	 */
	public void setDateEnd(Date dateEnd) {
		this.dateEnd = dateEnd;
	}

}
