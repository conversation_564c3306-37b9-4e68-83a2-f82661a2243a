package mon.dao.impl;

import java.util.List;

import mon.dao.MonSysParamDao;
import mon.model.MonSysParam;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * <pre>
 * ParaDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("sysParamDao")
public class MonSysParamDaoImpl extends AbstractGenericDao<MonSysParam> implements MonSysParamDao {

	@Override
	public List<MonSysParam> findByParamList(List<String> keyList) {

		if (keyList == null) {
			return null;
		}
		String querySql = "(";
		for (int i = 0; i < keyList.size(); i++) {
			if (i == 0) {
				querySql = querySql + "?";
			} else {
				querySql = querySql + ", ?";
			}
		}
		querySql = querySql + ")";

		final String SQL = " select * from MON_SYSPARAM where PARAM in " + querySql + " order by PARAM asc";
		return this.getJdbc().query(SQL, keyList.toArray(),
				ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));

	}

	@Override
	public List<MonSysParam> findByParamKeyword(String keyword) {

		keyword = "%" + keyword + "%";
		String sql = " select * from MON_SYSPARAM where PARAM like ? order by PARAM asc";

		return this.getJdbc().query(sql, new String[] { keyword },
				ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));
	}

	@Override
	public List<MonSysParam> findByParams(String... params) {

		String sql = " select * from MON_SYSPARAM where RTRIM(PARAM) IN ( ";
		for (int i = 0; i < params.length; i++) {
			sql += (i == 0 ? "?" : ",?");
		}
		sql += ")order by PARAM asc";

		return this.getJdbc().query(sql, params, ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));

	}

	@Override
	public int updateParamValue(String param, String paramValue) {
		String sql = "UPDATE MON_SYSPARAM SET PARAMVALUE=? WHERE PARAM=?";
		return this.getJdbc().update(sql, new String[] { paramValue, param });
	}

}