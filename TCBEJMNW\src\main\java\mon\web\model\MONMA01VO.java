package mon.web.model;

import java.util.List;

import mon.model.MonSysParam;

/**
 * <pre>
 * MONEJ01VO.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class MONMA01VO {
	boolean ok = false;
	String errMsg;

	MonSysParam updModel;
	List<MonSysParam> dataList;

	/**
	 * Returns the ok
	 * 
	 * @return the ok
	 */
	public boolean isOk() {
		return this.ok;
	}

	/**
	 * Sets the ok
	 * 
	 * @param ok
	 *            the ok to set
	 */
	public void setOk(boolean ok) {
		this.ok = ok;
	}

	/**
	 * Returns the errMsg
	 * 
	 * @return the errMsg
	 */
	public String getErrMsg() {
		return errMsg;
	}

	/**
	 * Sets the errMsg
	 * 
	 * @param errMsg
	 *            the errMsg to set
	 */
	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	/**
	 * Returns the dataList
	 * 
	 * @return the dataList
	 */
	public List<MonSysParam> getDataList() {
		return dataList;
	}

	/**
	 * Sets the dataList
	 * 
	 * @param dataList
	 *            the dataList to set
	 */
	public void setDataList(List<MonSysParam> dataList) {
		this.dataList = dataList;
	}

	/**
	 * Returns the updModel
	 * 
	 * @return the updModel
	 */
	public MonSysParam getUpdModel() {
		return updModel;
	}

	/**
	 * Sets the updModel
	 * 
	 * @param updModel
	 *            the updModel to set
	 */
	public void setUpdModel(MonSysParam updModel) {
		this.updModel = updModel;
	}

}
