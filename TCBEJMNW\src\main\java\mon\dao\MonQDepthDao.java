package mon.dao;

import java.sql.Timestamp;
import java.util.List;

import mon.model.MonQDepth;

/**
 * <pre>
 * MonSysConfig.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public interface MonQDepthDao extends GenericDao<MonQDepth> {
	public List<MonQDepth> findQDepthByRunts(Timestamp startDate, Timestamp endDate, String sysId);
}
