-- This CLP file was created using DB2LOOK Version "10.1" 
-- Timestamp: Wed Feb 19 17:38:27 2025
-- Database Name: EJMN           
-- Database Manager Version: DB2/AIX64 Version 10.1.6      
-- Database Codepage: 950
-- Database Collating Sequence is: UNIQUE
-- Alternate collating sequence(alt_collate): null
-- varchar2 compatibility(varchar2_compat): OFF
-- COMMIT is omitted. Explicit commit is required after executing the script.


------------------------------------
-- DDL Statements for BUFFERPOOLS --
------------------------------------
 
CREATE BUFFERPOOL "EJMN_4KBP" SIZE AUTOMATIC PAGESIZE 4096;

CONNECT RESET;
CONNECT TO EJMN;

--------------------------
-- Mimic Storage groups --
--------------------------
 
<PERSON><PERSON><PERSON> STOGROUP "IBMSTOGROUP"
	 OVERHEAD 6.725000
	 DEVICE READ RATE 100.000000
	 DATA TAG NONE 
	 SET AS DEFAULT;

------------------------------------
-- DDL Statements for TABLESPACES --
------------------------------------


CREATE LARGE TABLESPACE "EJMNTABSP4K" IN DATABASE PARTITION GROUP IBMDEFAULTGROUP 
	 PAGESIZE 4096 MANAGED BY AUTOMATIC STORAGE 
	 USING STOGROUP "IBMSTOGROUP" 
	 AUTORESIZE YES 
	 INITIALSIZE 32 M 
	 MAXSIZE NONE 
	 EXTENTSIZE 32
	 PREFETCHSIZE AUTOMATIC
	 BUFFERPOOL "EJMN_4KBP"
	 DATA TAG INHERIT
	 OVERHEAD INHERIT
	 TRANSFERRATE INHERIT 
	 DROPPED TABLE RECOVERY ON;


CREATE LARGE TABLESPACE "SYSTOOLSPACE" IN DATABASE PARTITION GROUP IBMCATGROUP 
	 PAGESIZE 4096 MANAGED BY AUTOMATIC STORAGE 
	 USING STOGROUP "IBMSTOGROUP" 
	 AUTORESIZE YES 
	 INITIALSIZE 32 M 
	 MAXSIZE NONE 
	 EXTENTSIZE 4
	 PREFETCHSIZE AUTOMATIC
	 BUFFERPOOL "IBMDEFAULTBP"
	 DATA TAG INHERIT
	 OVERHEAD INHERIT
	 TRANSFERRATE INHERIT 
	 DROPPED TABLE RECOVERY ON;


CREATE USER TEMPORARY TABLESPACE "SYSTOOLSTMPSPACE" IN DATABASE PARTITION GROUP IBMCATGROUP 
	 PAGESIZE 4096 MANAGED BY AUTOMATIC STORAGE 
	 USING STOGROUP "IBMSTOGROUP" 
	 EXTENTSIZE 4
	 PREFETCHSIZE AUTOMATIC
	 BUFFERPOOL "IBMDEFAULTBP"
	 OVERHEAD INHERIT
	 TRANSFERRATE INHERIT 
	 FILE SYSTEM CACHING  
	 DROPPED TABLE RECOVERY OFF;


----------------------
-- Mimic tablespace --
----------------------

ALTER TABLESPACE "SYSCATSPACE"
      PREFETCHSIZE AUTOMATIC
      OVERHEAD INHERIT
      AUTORESIZE YES 
      TRANSFERRATE INHERIT;


ALTER TABLESPACE "SYSCATSPACE"
      USING STOGROUP "IBMSTOGROUP"; 


ALTER TABLESPACE "TEMPSPACE1"
      PREFETCHSIZE AUTOMATIC
      OVERHEAD INHERIT
      FILE SYSTEM CACHING 
      TRANSFERRATE INHERIT;


ALTER TABLESPACE "USERSPACE1"
      PREFETCHSIZE AUTOMATIC
      OVERHEAD INHERIT
      AUTORESIZE YES 
      TRANSFERRATE INHERIT
      DATA TAG INHERIT;


ALTER TABLESPACE "USERSPACE1"
      USING STOGROUP "IBMSTOGROUP"; 


------------------------------------------------
-- DDL Statements for Schemas
------------------------------------------------

-- Running the DDL below will explicitly create a schema in the
-- new database that corresponds to an implicitly created schema
-- in the original database.

CREATE SCHEMA "EJMNAP  ";

CREATE SCHEMA "JCDINST1";



------------------------------------------------
-- DDL Statements for Table "EJMNAP  "."MON_SYSUSAGE"
------------------------------------------------
 

CREATE TABLE "EJMNAP  "."MON_SYSUSAGE"  (
		  "SYSID" VARCHAR(5) NOT NULL , 
		  "SCHSNO" VARCHAR(16) NOT NULL , 
		  "HOSTID" VARCHAR(8) , 
		  "CPU_AP" DOUBLE , 
		  "CPU_DB" DOUBLE , 
		  "MEM_WEB" DOUBLE , 
		  "MEM_WEB_MAX" DOUBLE , 
		  "MEM_DISP1" DOUBLE , 
		  "MEM_DISP1_MAX" DOUBLE , 
		  "MEM_DISP2" DOUBLE , 
		  "MEM_DISP2_MAX" DOUBLE , 
		  "MEM_GW" DOUBLE , 
		  "MEM_GW_MAX" DOUBLE , 
		  "RUNTS" TIMESTAMP , 
		  "VMSTAT_AP" VARCHAR(1000) , 
		  "VMSTAT_DB" VARCHAR(1000) )   
		 IN "EJMNTABSP4K" ; 


-- DDL Statements for Primary Key on Table "EJMNAP  "."MON_SYSUSAGE"

ALTER TABLE "EJMNAP  "."MON_SYSUSAGE" 
	ADD CONSTRAINT "P_MON_QDEPTH" PRIMARY KEY
		("SYSID",
		 "SCHSNO");


------------------------------------------------
-- DDL Statements for Table "EJMNAP  "."MON_SYSPARAM"
------------------------------------------------
 

CREATE TABLE "EJMNAP  "."MON_SYSPARAM"  (
		  "PARAM" VARCHAR(50) NOT NULL , 
		  "PARAMVALUE" VARCHAR(1000) NOT NULL , 
		  "PARAMDESC" VARCHAR(300) , 
		  "MODIFYBY" VARCHAR(10) , 
		  "MODIFYTIME" TIMESTAMP WITH DEFAULT CURRENT TIMESTAMP )   
		 IN "EJMNTABSP4K" ; 


-- DDL Statements for Primary Key on Table "EJMNAP  "."MON_SYSPARAM"

ALTER TABLE "EJMNAP  "."MON_SYSPARAM" 
	ADD CONSTRAINT "P_MON_SYSPARAM" PRIMARY KEY
		("PARAM");


------------------------------------------------
-- DDL Statements for Table "EJMNAP  "."MON_QDEPTH"
------------------------------------------------
 

CREATE TABLE "EJMNAP  "."MON_QDEPTH"  (
		  "SYSID" VARCHAR(5) NOT NULL , 
		  "SCHSNO" VARCHAR(16) NOT NULL , 
		  "Q01_DEPTH" INTEGER , 
		  "Q02_DEPTH" INTEGER , 
		  "Q03_DEPTH" INTEGER , 
		  "Q04_DEPTH" INTEGER , 
		  "Q05_DEPTH" INTEGER , 
		  "Q06_DEPTH" INTEGER , 
		  "Q07_DEPTH" INTEGER , 
		  "Q08_DEPTH" INTEGER , 
		  "Q09_DEPTH" INTEGER , 
		  "Q10_DEPTH" INTEGER , 
		  "Q11_DEPTH" INTEGER , 
		  "Q12_DEPTH" INTEGER , 
		  "Q13_DEPTH" INTEGER , 
		  "Q14_DEPTH" INTEGER , 
		  "Q15_DEPTH" INTEGER , 
		  "Q16_DEPTH" INTEGER , 
		  "Q17_DEPTH" INTEGER , 
		  "Q18_DEPTH" INTEGER , 
		  "Q19_DEPTH" INTEGER , 
		  "Q20_DEPTH" INTEGER , 
		  "Q21_DEPTH" INTEGER , 
		  "Q22_DEPTH" INTEGER , 
		  "Q23_DEPTH" INTEGER , 
		  "Q24_DEPTH" INTEGER , 
		  "Q25_DEPTH" INTEGER , 
		  "Q26_DEPTH" INTEGER , 
		  "Q27_DEPTH" INTEGER , 
		  "Q28_DEPTH" INTEGER , 
		  "Q29_DEPTH" INTEGER , 
		  "Q30_DEPTH" INTEGER , 
		  "Q31_DEPTH" INTEGER , 
		  "Q32_DEPTH" INTEGER , 
		  "Q33_DEPTH" INTEGER , 
		  "Q34_DEPTH" INTEGER , 
		  "Q35_DEPTH" INTEGER , 
		  "Q36_DEPTH" INTEGER , 
		  "Q37_DEPTH" INTEGER , 
		  "Q38_DEPTH" INTEGER , 
		  "Q39_DEPTH" INTEGER , 
		  "Q40_DEPTH" INTEGER , 
		  "RUNTS" TIMESTAMP )   
		 IN "EJMNTABSP4K" ; 


-- DDL Statements for Primary Key on Table "EJMNAP  "."MON_QDEPTH"

ALTER TABLE "EJMNAP  "."MON_QDEPTH" 
	ADD CONSTRAINT "P_MON_QDEPTH" PRIMARY KEY
		("SYSID",
		 "SCHSNO");







---------------------------------
-- DDL Statements for User Defined Functions
---------------------------------


CREATE FUNCTION "EJMNAP  "."MONVMSTAT" 
		(
		)
 
		RETURNS  VARCHAR(1000)
		SPECIFIC MONVMSTAT 
		EXTERNAL NAME 'SysInfo!getVmstat()' 
		LANGUAGE JAVA     
		PARAMETER STYLE JAVA     
		VARIANT 
		FENCED THREADSAFE 
		NULL CALL 
		NO SQL 
		NO EXTERNAL ACTION 
		NO SCRATCHPAD 
		NO FINAL CALL 
		DISALLOW PARALLEL 
		NO DBINFO 
		NOT SECURED;




--------------------------------------------
-- Authorization Statements on Tables/Views 
--------------------------------------------

 
GRANT SELECT ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "EJMNAP  " ;

GRANT INSERT ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "EJMNAP  " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "EJMNAP  " ;

GRANT UPDATE ON TABLE "EJMNAP  "."MON_QDEPTH" TO USER "EJMNAP  " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "EJMNAP  " ;

GRANT INSERT ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "EJMNAP  " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "EJMNAP  " ;

GRANT UPDATE ON TABLE "EJMNAP  "."MON_SYSPARAM" TO USER "EJMNAP  " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "EMDAP31 " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "EMDAP32 " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "EMDAP33 " ;

GRANT DELETE ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "DCDBMOD " ;

GRANT INSERT ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "DCDBMOD " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "DCDBMOD " ;

GRANT UPDATE ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "DCDBMOD " ;

GRANT DELETE ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "EJMNAP  " ;

GRANT INSERT ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "EJMNAP  " ;

GRANT SELECT ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "EJMNAP  " ;

GRANT UPDATE ON TABLE "EJMNAP  "."MON_SYSUSAGE" TO USER "EJMNAP  " ;

----------------------------------------
-- Authorization Statements on Packages 
----------------------------------------

 
GRANT BIND ON PACKAGE "NULLID  "."AOTMJ03 " TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."AOTMJ03 " TO  PUBLIC   ;

GRANT BIND ON PACKAGE "NULLID  "."SQLL9J1M" TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLL9J1M" TO  PUBLIC   ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUFJ15" TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUFJ15" TO  PUBLIC   ;

GRANT BIND ON PACKAGE "NULLID  "."SQLUHJ05" TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SQLUHJ05" TO  PUBLIC   ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "EJMNAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH200" TO USER "EMDAP33 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "DCDBMOD " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "EJMNAP  " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "EMDAP31 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "EMDAP32 " ;

GRANT EXECUTE ON PACKAGE "NULLID  "."SYSSH300" TO USER "EMDAP33 " ;

----------------------------------------
-- Authorization Statements on Database 
----------------------------------------

 
GRANT CONNECT ON DATABASE  TO USER "DCDBMOD " ;

GRANT CONNECT ON DATABASE  TO USER "EJMNAP  " ;

GRANT CONNECT ON DATABASE  TO USER "EMDAP31 " ;

GRANT CONNECT ON DATABASE  TO USER "EMDAP32 " ;

GRANT CONNECT ON DATABASE  TO USER "EMDAP33 " ;

---------------------------------------
-- Authorization statement on table space 
---------------------------------------

 
GRANT USE OF TABLESPACE "SYSTOOLSTMPSPACE" TO  PUBLIC   ;

--------------------------------------------------------
-- Database and Database Manager configuration parameters
--------------------------------------------------------

UPDATE DBM CFG USING cpuspeed 3.148961e-07;
UPDATE DBM CFG USING intra_parallel NO;
UPDATE DBM CFG USING comm_bandwidth 100.000000;
UPDATE DBM CFG USING federated NO;
UPDATE DBM CFG USING fed_noauth NO;

UPDATE DB CFG FOR EJMN USING locklist 13984;
UPDATE DB CFG FOR EJMN USING dft_degree 1;
UPDATE DB CFG FOR EJMN USING maxlocks 98;
UPDATE DB CFG FOR EJMN USING avg_appls 1;
UPDATE DB CFG FOR EJMN USING stmtheap 8192;
UPDATE DB CFG FOR EJMN USING dft_queryopt 5;
UPDATE DB CFG FOR EJMN USING cur_commit ON;

---------------------------------
-- Environment Variables settings
---------------------------------

