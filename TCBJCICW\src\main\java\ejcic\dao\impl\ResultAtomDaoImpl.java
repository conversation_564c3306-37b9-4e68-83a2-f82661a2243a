package ejcic.dao.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import ejcic.dao.ResultAtomDao;
import ejcic.jdbc.EJCICJdbcTemplate;

/**
 * <pre>
 * ResultAtomDaoImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Repository("resultAtomDao")
public class ResultAtomDaoImpl implements ResultAtomDao {
	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	private EJCICJdbcTemplate jdbc;

	public EJCICJdbcTemplate getJdbc() {
		return this.jdbc;
	}

	@Override
	public String getACS003_REST_IDN(String id) {
		String sql = "select distinct RESP_IDN from ACS003  where ID=? ";
		List<Map<String, Object>> rs = this.jdbc.queryForList(sql, new Object[] { id });
		String respIdn = "";
		if (rs != null && rs.size() > 0) {
			for (Map<String, Object> row : rs) {
				respIdn = (String) row.get("RESP_IDN");
			}
		}
		return respIdn;
	}

	@Override
	public List<Map<String, Object>> findACS003ById(String id) {
		String sql = "select NAME,E_NAME,ADDRESS,RESP_DATE,RESP_IDN from ACS003  where ID=? ";
		return this.jdbc.queryForList(sql, new Object[] { id });
	}

	public List<Map<String, Object>> findAPS001ById(String id) {
		String sql = "select RGY_ADDRESS from APS001  where ID=? ";
		return this.jdbc.queryForList(sql, new Object[] { id });
	}

}
