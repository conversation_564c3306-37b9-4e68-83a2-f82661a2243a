package ejcic.dao.impl;

import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import ejcic.dao.CheckTxidDao;
import ejcic.model.CheckTxid;

/**
 * <pre>
 * ChecktxidDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("checkTxidDao")
public class CheckTxidDaoImpl extends AbstractGenericDao<CheckTxid> implements CheckTxidDao {

	/*
	 * (non-Javadoc)
	 * 
	 * @see ejcic.dao.CheckTxidDao#findDate(java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public String findDate(String txid, String chargedId, String dataDate) {
		String sql = "select Date from CHECKTXID where Txid = ?  and CODE = ?  and Datadate = ? fetch first 1 rows only with ur";
		Object[] args = new Object[] { txid, chargedId, dataDate };
		List<Map<String, Object>> result = this.getJdbc().queryForList(sql, args);
		if (result != null && result.size() > 0) {
			return (String) result.get(0).get("Date");
		}
		return null;
	}

	@Override
	public boolean isPrintW20(String chargedId, String dataDate) {
		String sql = "select Date from CHECKTXID where Txid ='HW20'  and CODE = ?  and Datadate = ? fetch first 1 rows only with ur";
		Object[] args = new Object[] { chargedId, dataDate };
		List<Map<String, Object>> result = this.getJdbc().queryForList(sql, args);
		return result != null && result.size() > 0;
	}

	@Override
	public boolean isQueryZ51(String chargedId, String timestamp) {
		String sql = "select date from checktxid where txid='HZ51' and code=? and timestamp>? fetch first 1 rows only with ur";
		Object[] args = new Object[] { chargedId, timestamp };
		List<Map<String, Object>> result = this.getJdbc().queryForList(sql, args);
		return result != null && result.size() > 0;
	}

	@Override
	public CheckTxid findZ51(String chargedId, String timestamp) {
		String sql = "select date,txid,division,code,datadate,timestamp from checktxid where txid='HZ51' and code=? and timestamp<? order by timestamp desc fetch first 1 rows only with ur";
		Object[] args = new Object[] { chargedId, timestamp };
		List<CheckTxid> result = this.getJdbc().query(sql, args, ParameterizedBeanPropertyRowMapper.newInstance(modelType));
		if (result != null && result.size() > 0) {
			return result.get(0);
		}
		return null;
	}

	@Override
	public String findZ51Date(String chargedId, String date) {
		String sql = "select date from checktxid where txid='HZ51' and code=? and date=? fetch first 1 rows only with ur";
		Object[] args = new Object[] { chargedId, date };
		List<Map<String, Object>> result = this.getJdbc().queryForList(sql, args);
		if (result != null && result.size() > 0) {
			return (String) result.get(0).get("date");
		}
		return null;
	}
}