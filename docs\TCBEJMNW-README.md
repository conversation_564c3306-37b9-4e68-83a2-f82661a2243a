# TCBEJMNW 專案技術文檔

## 專案概述

TCBEJMNW 是一個監控系統專案，主要用於監控和管理 TCBJCICW、TCBETCHW 和 ELOAN 等相關系統的運行狀態。該系統提供了各種監控功能，包括系統資源使用情況、模組狀態、執行緒狀態、佇列深度等監控指標，以及日誌查詢和系統參數管理等功能。

## 系統架構

TCBEJMNW 採用 Spring MVC 架構，基於 JDK 1.6 開發，使用 Maven 3.2.5 進行專案管理，Jetty 8.1.8 作為 Web 容器。

### 架構圖

```mermaid
graph TD
    Client[客戶端瀏覽器] --> WebLayer[Web 層]
    WebLayer --> ServiceLayer[服務層]
    ServiceLayer --> DAOLayer[數據訪問層]
    DAOLayer --> DB[(資料庫)]
    ServiceLayer --> MQ[消息佇列]
    MQ --> TCBJCICW[TCBJCICW 系統]
    MQ --> TCBETCHW[TCBETCHW 系統]
    MQ --> ELOAN[ELOAN 系統]
    
    subgraph WebLayer
        JSP[JSP 頁面]
        Handler[Handler 控制器]
        Filter[過濾器]
    end
    
    subgraph ServiceLayer
        MonService[監控服務]
        MQService[MQ 服務]
        SysParamService[系統參數服務]
    end
    
    subgraph DAOLayer
        GenericDao[通用 DAO]
        ModelMeta[模型元數據]
    end
```

## 核心功能模組

### 1. 系統資源監控模組

負責監控各系統的 CPU、記憶體使用情況等系統資源。

```mermaid
classDiagram
    class MonSysUsage {
        +String sysId
        +String schSno
        +String hostId
        +int cpu_ap
        +int cpu_db
        +int mem_web
        +int mem_web_max
        +int mem_disp1
        +int mem_disp1_max
        +int mem_disp2
        +int mem_disp2_max
        +int mem_gw
        +int mem_gw_max
        +Timestamp runts
        +String vmstat_ap
        +String vmstat_db
    }
    
    class MonSysUsageService {
        +MonSysUsage doEJCICWebSysUsage()
        +MonSysUsage doEJCICDispSysUsage()
        +MonSysUsage doEJCICGWSysUsage()
        +MonSysUsage doEloanDispSysUsage()
        +MonSysUsage doETCHWebSysUsage()
        +MonSysUsage doETCHDispSysUsage()
        +MonSysUsage doETCHGWSysUsage()
    }
    
    MonSysUsageService --> MonSysUsage
```

### 2. 模組狀態監控模組

負責監控各系統的模組運行狀態。

```mermaid
classDiagram
    class MonModuleStatus {
        +String sysId
        +String moduleId
        +String moduleName
        +String status
        +String statusDesc
    }
    
    class MonModuleStatusService {
        +List~MonModuleStatus~ doEJCICWebStatus()
        +List~MonModuleStatus~ doEJCICDispStatus()
        +List~MonModuleStatus~ doEJCICGWStatus()
        +List~MonModuleStatus~ doEloanDispStatus()
        +List~MonModuleStatus~ doETCHWebStatus()
        +List~MonModuleStatus~ doETCHDispStatus()
        +List~MonModuleStatus~ doETCHGWStatus()
    }
    
    MonModuleStatusService --> MonModuleStatus
```

### 3. 佇列深度監控模組

負責監控各系統的消息佇列深度。

```mermaid
classDiagram
    class MonQDepth {
        +String sysId
        +String schSno
        +int q01_depth
        +int q02_depth
        +int q03_depth
        ...
        +int q40_depth
        +Timestamp runTS
        +void setDepth(String qid, int depth)
    }
    
    class MONEJ01VO {
        +boolean ok
        +String qid
        +String qname
        +String qdesc
        +String depth
        +String color
    }
    
    MONEJ01VO --> MonQDepth
```

### 4. 日誌查詢模組

提供各系統的日誌查詢功能。

```mermaid
classDiagram
    class EJCICLogFile {
        +Date date
        +String txid
        +String msgid
        +String productid
        +String requestid
        +String division
        +String querykey1
        +String querykey2
        +String isprint
        +String rc
    }
    
    class STJLogFile {
        +String yyy
        +String mm
        +String sendType
        +String step
        +String rc
        +String runSNo
    }
    
    class ELJobLog {
        +String jmName
        +int seq
        +String tbName
        +String type
        +Date date
        +String rc
        +Timestamp sTime
        +Timestamp eTime
        +BigDecimal readCnt
        +BigDecimal skippCnt
        +BigDecimal loadCnt
        +BigDecimal rejCnt
        +BigDecimal delCnt
        +BigDecimal commCnt
        +String reMark
        +Date dateEnd
    }
    
    class MONEJ06Handler {
        +WebView action(WebContext context, Map~String, String~ paramMap)
    }
    
    class MONTJ01Handler {
        +WebView action(WebContext context, Map~String, String~ paramMap)
        +WebView query(WebContext context, Map~String, String~ paramMap)
        +WebView findResult(WebContext context, Map~String, String~ paramMap)
    }
    
    class MONEL01Handler {
        +WebView action(WebContext context, Map~String, String~ paramMap)
    }
    
    MONEJ06Handler --> EJCICLogFile
    MONTJ01Handler --> STJLogFile
    MONEL01Handler --> ELJobLog
```

## 主要業務流程

### 1. 系統資源監控流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant Handler as MONEJ07Handler
    participant Service as MonSysUsageService
    participant MQ as MQService
    participant DB as 資料庫
    
    User->>Handler: 請求系統資源監控頁面
    Handler->>Service: 呼叫 doEJCICWebSysUsage() 等方法
    Service->>MQ: 發送監控請求消息
    MQ-->>Service: 返回監控數據
    Service->>DB: 儲存監控數據
    Service-->>Handler: 返回處理結果
    Handler-->>User: 顯示監控結果頁面
```

### 2. 模組狀態監控流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant Handler as Handler
    participant Service as MonModuleStatusService
    participant MQ as MQService
    participant Target as 目標系統
    
    User->>Handler: 請求模組狀態頁面
    Handler->>Service: 呼叫 doEJCICWebStatus() 等方法
    Service->>MQ: 發送狀態查詢請求
    MQ->>Target: 轉發請求
    Target-->>MQ: 返回模組狀態
    MQ-->>Service: 返回狀態數據
    Service-->>Handler: 返回處理結果
    Handler-->>User: 顯示狀態頁面
```

### 3. 日誌查詢流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant Handler as MONEJ06Handler/MONTJ01Handler/MONEL01Handler
    participant Service as MONEJ00Service/MONTJ00Service/MONEL00Service
    participant DAO as DAO層
    participant DB as 資料庫
    
    User->>Handler: 提交查詢條件
    Handler->>Service: 呼叫查詢服務
    Service->>DAO: 執行查詢
    DAO->>DB: 查詢資料庫
    DB-->>DAO: 返回查詢結果
    DAO-->>Service: 返回數據
    Service-->>Handler: 返回處理結果
    Handler-->>User: 顯示查詢結果頁面
```

## 數據模型

TCBEJMNW 專案使用了多種數據模型來表示監控數據和系統狀態：

1. **MonSysUsage**: 系統資源使用情況模型
2. **MonModuleStatus**: 模組狀態模型
3. **MonQDepth**: 佇列深度模型
4. **EJCICLogFile**: EJCIC 系統日誌模型
5. **STJLogFile**: STJ 系統日誌模型
6. **ELJobLog**: ELOAN 系統作業日誌模型

這些模型都繼承自 `AbstractModelMeta` 基類，並使用 `@TModel`、`@TField` 和 `@TKeyField` 等註解來定義表名和欄位映射。

## 技術特點

1. **ORM 映射**: 使用自定義的註解和反射機制實現簡單的 ORM 映射
2. **MQ 通訊**: 使用消息佇列與其他系統進行通訊
3. **Spring MVC**: 使用 Spring MVC 框架處理 Web 請求
4. **Handler 模式**: 使用 Handler 模式處理不同的業務邏輯
5. **VO 模式**: 使用 VO (Value Object) 模式在各層之間傳遞數據

## 部署說明

TCBEJMNW 專案需要部署在支援 JDK 1.6 的環境中，並配置相應的資料庫連接和 MQ 連接。詳細的部署步驟請參考專案的部署文檔。

## 常見問題

1. **連接超時**: 檢查資料庫和 MQ 連接設定是否正確
2. **監控數據不更新**: 檢查目標系統是否正常運行，MQ 連接是否正常
3. **日誌查詢失敗**: 檢查資料庫連接和查詢條件是否正確

## 結論

TCBEJMNW 專案是一個功能完整的監控系統，提供了豐富的監控功能和日誌查詢功能，能夠有效地監控和管理 TCBJCICW、TCBETCHW 和 ELOAN 等相關系統的運行狀態。通過本文檔，您可以了解 TCBEJMNW 專案的架構、功能和業務流程，以便更好地使用和維護該系統。
