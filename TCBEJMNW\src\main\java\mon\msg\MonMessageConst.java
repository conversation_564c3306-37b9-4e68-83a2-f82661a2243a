package mon.msg;

/**
 * <pre>
 * MonMessageConst.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public interface MonMessageConst {
	public static final String MONITOR_TXID = "$MON";

	public static final String SYSID_EJCIC = "EJCIC";
	public static final String SYSID_ETCH = "ETCH";
	public static final String SYSID_ELOAN = "ELOAN";

	public static final String SYSTYPE_DISP = "DISP";
	public static final String SYSTYPE_GW = "GW";
	public static final String SYSTYPE_WEB = "WEB";

	public static final String QTYPE_SYSINFO = "1";
	public static final String QTYPE_THREAD = "2";
	public static final String QTYPE_STATUS = "3";

	public static final String STATUS_ACTIVE = "A";
	public static final String STATUS_STOPPED = "S";
	public static final String STATUS_PAUSE = "P";

	public static final String MSGID_EJCIC = "EJCIC";
	public static final String MSGID_ETCH = "ETCH";
	public static final String MSGID_DISP = "DISP";
	public static final String MSGID_GW = "GW";

	public static final String MSGID_EJCIC_WEB = "EJCIC_WEB";
	public static final String MSGID_EJCIC_WEB_DISP = "EJCIC_WEB_DISP";
	public static final String MSGID_EJCIC_ELOAN_DISP = "EJCIC_ELOAN_DISP";
	public static final String MSGID_EJCIC_GW = "EJCIC_GW";

	public static final String MSGID_ETCH_WEB = "ETCH_WEB";
	public static final String MSGID_ETCH_WEB_DISP = "ETCH_WEB_DISP";
	public static final String MSGID_ETCH_GW = "ETCH_GW";
}
