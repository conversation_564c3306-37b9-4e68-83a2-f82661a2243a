package ejcic.web.handler;

import java.util.Map;

import ejcic.exception.AppException;
import ejcic.web.core.AbstractHandler;
import ejcic.web.core.WebContext;
import ejcic.web.core.WebView;

/**
 * <pre>
 * DummyHandler.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class DummyHandler extends AbstractHandler {

	/*
	 * (non-Javadoc)
	 * 
	 * @see etch.web.core.Handler#action(etch.web.core.WebContext, java.util.Map)
	 */
	@Override
	public WebView action(WebContext context, Map<String, String> paramMap) throws AppException {
		return null;
	}
}
