# TCBETCHW 專案技術文檔

## 專案概述

TCBETCHW 是一個金融交易系統的 Web 前端專案，主要用於處理與票據交換所 (TCH) 相關的查詢和交易。該系統提供了票據查詢、票據狀態管理、授權管理、系統參數設定等功能，並透過 MQ 與票據交換所系統進行通訊。

## 系統架構

TCBETCHW 採用 Spring MVC 架構，基於 JDK 1.6 開發，使用 Maven 3.2.5 進行專案管理，Jetty 8.1.8 作為 Web 容器。

### 架構圖

```mermaid
graph TD
    Client[客戶端瀏覽器] --> WebLayer[Web 層]
    WebLayer --> ServiceLayer[服務層]
    ServiceLayer --> DAOLayer[數據訪問層]
    DAOLayer --> DB[(資料庫)]
    ServiceLayer --> MQ[消息佇列]
    MQ --> DISP[Dispatcher]
    DISP --> GW[Gateway]
    GW --> TCH[票據交換所]
    
    subgraph WebLayer
        JSP[JSP 頁面]
        Handler[Handler 控制器]
        Filter[過濾器]
    end
    
    subgraph ServiceLayer
        TxidService[交易服務]
        MQService[MQ 服務]
        AuthService[授權服務]
    end
    
    subgraph DAOLayer
        GenericDao[通用 DAO]
        ModelMeta[模型元數據]
    end
```

## 核心功能模組

### 1. 票據查詢模組

提供各種票據查詢功能，包括支票查詢、本行票據查詢、他行票據查詢等。

```mermaid
classDiagram
    class TchTxidProfile {
        +String txid
        +String txidName
        +String txidDesc
        +String txidType
        +String authType
        +String needAuth
        +String needCharge
        +String needLog
        +String needPrint
    }
    
    class TchTxidService {
        +boolean checkTxid(String txid, String billNo)
        +boolean checkAuth(String txid, String billNo, String userId)
        +Map<String, Object> queryBill(String txid, String billNo)
    }
    
    TchTxidService --> TchTxidProfile
```

### 2. 票據狀態管理模組

管理票據的各種狀態，包括已兌現、已退票、已止付等。

```mermaid
classDiagram
    class BillStatus {
        +String billNo
        +String billType
        +String status
        +Date statusDate
        +String statusDesc
        +String operator
    }
    
    class BillStatusService {
        +BillStatus getBillStatus(String billNo)
        +void updateBillStatus(String billNo, String status)
        +List<BillStatus> queryBillStatus(Map<String, String> params)
    }
    
    BillStatusService --> BillStatus
```

### 3. 授權管理模組

管理用戶對各種交易的授權。

```mermaid
classDiagram
    class UserAuth {
        +String userId
        +String billType
        +String txid
        +String authType
        +Date startDate
        +Date endDate
        +String status
    }
    
    class AuthService {
        +boolean hasAuth(String userId, String txid, String billType)
        +void grantAuth(String userId, String txid, String billType)
        +void revokeAuth(String userId, String txid, String billType)
    }
    
    AuthService --> UserAuth
```

### 4. 系統參數管理模組

管理系統的各種參數設定。

```mermaid
classDiagram
    class SysParam {
        +String paramId
        +String paramValue
        +String paramDesc
        +String paramType
    }
    
    class SysParamService {
        +String getParam(String paramId)
        +void setParam(String paramId, String paramValue)
        +List<SysParam> getAllParams()
    }
    
    SysParamService --> SysParam
```

## 主要業務流程

### 1. 票據查詢流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant Handler as Handler
    participant Service as TchTxidService
    participant MQ as MQService
    participant TCH as 票據交換所
    
    User->>Handler: 提交票據查詢請求
    Handler->>Service: 檢查授權
    Service->>Handler: 授權通過
    Handler->>MQ: 發送查詢請求
    MQ->>TCH: 轉發請求
    TCH-->>MQ: 返回查詢結果
    MQ-->>Handler: 返回結果
    Handler-->>User: 顯示查詢結果
```

### 2. 票據狀態更新流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant Handler as Handler
    participant Service as BillStatusService
    participant MQ as MQService
    participant TCH as 票據交換所
    
    User->>Handler: 提交票據狀態更新請求
    Handler->>Service: 檢查授權
    Service->>Handler: 授權通過
    Handler->>MQ: 發送狀態更新請求
    MQ->>TCH: 轉發請求
    TCH-->>MQ: 返回處理結果
    MQ-->>Handler: 返回結果
    Handler-->>User: 顯示處理結果
```

### 3. 授權管理流程

```mermaid
sequenceDiagram
    participant Admin as 管理員
    participant Handler as Handler
    participant AuthService as AuthService
    participant DAO as DAO
    participant DB as 資料庫
    
    Admin->>Handler: 提交授權設定
    Handler->>AuthService: 處理授權請求
    AuthService->>DAO: 儲存授權資料
    DAO->>DB: 寫入資料庫
    DB-->>DAO: 確認寫入
    DAO-->>AuthService: 返回處理結果
    AuthService-->>Handler: 返回處理結果
    Handler-->>Admin: 顯示處理結果
```

## 系統組件

### 1. Dispatcher (分發器)

負責接收 Web 層的請求，並將其分發到相應的處理模組。

```mermaid
classDiagram
    class AbstractDispatcher {
        +void init()
        +void start()
        +void stop()
        +void dispatch(Message msg)
    }
    
    class TchDispatcher {
        +void processMessage(Message msg)
        +void sendToTch(Message msg)
        +void receiveFromTch(Message msg)
    }
    
    class MonDispatcher {
        +void processMessage(Message msg)
        +void sendMonitorData(Message msg)
    }
    
    AbstractDispatcher <|-- TchDispatcher
    AbstractDispatcher <|-- MonDispatcher
```

### 2. Gateway (閘道)

負責與票據交換所進行通訊。

```mermaid
classDiagram
    class GWManager {
        +void init()
        +void start()
        +void stop()
        +void sendToTch(Message msg)
        +void receiveFromTch(Message msg)
    }
    
    class SendProcess {
        +void run()
        +void sendMessage(Message msg)
    }
    
    class RecvProcess {
        +void run()
        +Message receiveMessage()
    }
    
    GWManager --> SendProcess
    GWManager --> RecvProcess
```

### 3. MQ 服務

負責與 MQ 進行通訊。

```mermaid
classDiagram
    class MQService {
        +void sendMessage(String queueName, Message msg)
        +Message receiveMessage(String queueName)
    }
    
    class IbmMQAdapter {
        +void connect()
        +void disconnect()
        +void sendMessage(String queueName, Message msg)
        +Message receiveMessage(String queueName)
    }
    
    MQService --> IbmMQAdapter
```

## 技術特點

1. **ORM 映射**: 使用自定義的註解和反射機制實現簡單的 ORM 映射
2. **MQ 通訊**: 使用 IBM MQ 與後端系統和票據交換所進行通訊
3. **Spring MVC**: 使用 Spring MVC 框架處理 Web 請求
4. **Handler 模式**: 使用 Handler 模式處理不同的業務邏輯
5. **安全機制**: 實現了多層次的授權和認證機制

## 部署說明

TCBETCHW 專案需要部署在支援 JDK 1.6 的環境中，並配置相應的資料庫連接和 MQ 連接。詳細的部署步驟請參考專案的部署文檔。

## 常見問題

1. **連接超時**: 檢查資料庫和 MQ 連接設定是否正確
2. **授權失敗**: 檢查用戶權限和授權設定
3. **查詢失敗**: 檢查票據交換所連接是否正常

## 結論

TCBETCHW 專案是一個功能完整的金融交易系統前端，提供了豐富的票據查詢和管理功能，能夠有效地處理與票據交換所的交易。通過本文檔，您可以了解 TCBETCHW 專案的架構、功能和業務流程，以便更好地使用和維護該系統。
