<?xml version="1.0"?>
<!DOCTYPE Configure PUBLIC "-//Mort Bay Consulting//DTD Configure//EN" "http://jetty.mortbay.org/configure.dtd">
<Configure class="org.eclipse.jetty.server.Server">
    <New id="tcbeadmw" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>jdbc/EJMN</Arg>
        <Arg>
            <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
                <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
                <Set name="jdbcUrl">**********************************************************************************************;</Set>
                <Set name="user">fantasy</Set>
                <Set name="password">JjCcSsRU*T?^</Set>
            </New>
        </Arg>
    </New>
    <New id="tcbeloanw" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>jdbc/ELOAN</Arg>
        <Arg>
            <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
                <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
                <Set name="jdbcUrl">*****************************************************;</Set>
                <Set name="user">fantasy</Set>
                <Set name="password">JjCcSsRU*T?^</Set>
            </New>
        </Arg>
    </New>
    <New id="tcbstjw" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>jdbc/STJ</Arg>
        <Arg>
            <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
                <Set name="driverClass">com.microsoft.sqlserver.jdbc.SQLServerDriver</Set>
                <Set name="jdbcUrl">************************************************************************;</Set>
                <Set name="user">sa</Set>
                <Set name="password">12345678</Set>
            </New>
        </Arg>
    </New>
    <New id="tcbjcicw" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>jdbc/EJCIC</Arg>
        <Arg>
            <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
                <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
                <Set name="jdbcUrl">***********************************************************************************************;</Set>
                <Set name="user">fantasy</Set>
                <Set name="password">JjCcSsRU*T?^</Set>
            </New>
        </Arg>
    </New>
    <New id="tcbetchw" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>jdbc/ETCH</Arg>
        <Arg>
            <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
                <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
                <Set name="jdbcUrl">***********************************************************************************************;</Set>
                <Set name="user">fantasy</Set>
                <Set name="password">JjCcSsRU*T?^</Set>
            </New>
        </Arg>
    </New>
    <New id="cf1" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>jms/EJCIC_QCF</Arg>
        <Arg>
            <New class="com.ibm.mq.jms.MQQueueConnectionFactory">
                <Set name="HostName">127.0.0.1</Set>
                <Set name="Port">1414</Set>
                <Set name="Channel">MQJ006D.SVRCONN.CH0</Set>
                <Set name="TransportType">1</Set>
                <Set name="QueueManager">MQJ006D</Set>
            </New>
        </Arg>
    </New>
    <New id="cf2" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>jms/ETCH_QCF</Arg>
        <Arg>
            <New class="com.ibm.mq.jms.MQQueueConnectionFactory">
                <Set name="HostName">127.0.0.1</Set>
                <Set name="Port">1418</Set>
                <Set name="Channel">MQT006D.SVRCONN.CH0</Set>
                <Set name="TransportType">1</Set>
                <Set name="QueueManager">MQT006D</Set>
            </New>
        </Arg>
    </New>

</Configure>