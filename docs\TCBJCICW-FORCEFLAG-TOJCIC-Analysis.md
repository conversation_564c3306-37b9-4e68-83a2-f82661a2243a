# TCBJCICW 專案中 FORCEFLAG 和 TOJCIC 變數分析

## 概述

本文檔分析 TCBJCICW 專案中 `LOGFILE.FORCEFLAG` 和 `TOJCIC` 這兩個變數的定義、使用方式和它們之間的關係。這兩個變數在系統中扮演著重要的角色，決定系統是否向聯徵中心發送查詢請求或直接從資料庫取得資料。

## 變數定義

### LOGFILE.FORCEFLAG

`FORCEFLAG` 是 `LogFile` 類別中的一個屬性，定義在 `TCBJCICW\src\main\java\ejcic\model\LogFile.java` 中：

```java
// FORCEFLAG 查詢方式 CHAR(1)
@TField
private String forceflag;
```

`FORCEFLAG` 可能的值：
- `"A"`: 自動（系統自行判斷）
- `"Y"`: 強迫至聯徵（強制向聯徵中心發送查詢）
- `"N"`: 資料庫查詢（直接從資料庫取得資料）

### TOJCIC

`TOJCIC` 是 `LogFile` 類別中的另一個屬性，同樣定義在 `TCBJCICW\src\main\java\ejcic\model\LogFile.java` 中：

```java
// TOJCIC 是否有送聯徵中心查詢 CHAR(1)
@TField
private String tojcic;

public static final String TOJCIC_YES = "Y";
public static final String TOJCIC_NO = "N";
```

`TOJCIC` 可能的值：
- `"Y"`: 是（已向聯徵中心發送查詢）
- `"N"`: 否（未向聯徵中心發送查詢）

## 變數關係

`FORCEFLAG` 和 `TOJCIC` 這兩個變數之間存在明確的關係：

1. `FORCEFLAG` 是**輸入參數**，用於指示系統是否應該向聯徵中心發送查詢。它在查詢開始前設定，決定查詢的行為。

2. `TOJCIC` 是**輸出參數**，用於記錄系統是否實際向聯徵中心發送了查詢。它在查詢完成後設定，反映查詢的實際結果。

簡而言之，`FORCEFLAG` 決定系統**應該**做什麼，而 `TOJCIC` 記錄系統**實際**做了什麼。

## 使用情境

### 情境一：強制向聯徵中心查詢

當 `FORCEFLAG = "Y"` 時，系統會強制向聯徵中心發送查詢，不論資料庫中是否已有相關資料。在這種情況下，查詢完成後，`TOJCIC` 通常會被設為 `"Y"`，表示已向聯徵中心發送查詢。

### 情境二：直接從資料庫查詢

當 `FORCEFLAG = "N"` 時，系統會直接從資料庫取得資料，不向聯徵中心發送查詢。在這種情況下，查詢完成後，`TOJCIC` 通常會被設為 `"N"`，表示未向聯徵中心發送查詢。

### 情境三：自動判斷

當 `FORCEFLAG = "A"` 時，系統會根據一定的規則自動判斷是否需要向聯徵中心發送查詢。判斷的依據可能包括：
- 資料庫中是否已有相關資料
- 資料庫中的資料是否過期
- 查詢項目是否在強制查詢清單中

在這種情況下，`TOJCIC` 的值取決於系統的判斷結果，可能是 `"Y"` 或 `"N"`。

## 程式碼分析

### 1. 前端參數處理

在 `JCICCS01Handler.java` 中，前端傳入的 `force2Jcic` 參數被設置到 `LogFile` 物件的 `forceflag` 屬性中：

```java
// 取得其他參數
String force2Jcic = StringUtils.trimToEmpty(paramMap.get("force2Jcic"));
// ...
LogFile logfile = new LogFile();
// ...
logfile.setForceflag(force2Jcic);
```

### 2. 強制查詢邏輯

在 `JCICST00Service.java` 的 `getW2DStdMessage` 方法中，有一段處理是否強制向聯徵查詢的邏輯：

```java
// 判斷查詢項目是否強制向聯徵查詢
String forceFlag = logfile.getForceflag();
String[] forceToJcic = StringUtils.split(StringUtils.trimToEmpty(this.sysParamService.getProperty(SysParamConst.STDTXID_FORCE_TOJCIC)), ",");
if (ArrayUtils.contains(forceToJcic, logfile.getTxid())) {
    forceFlag = "Y";
    logger.warn("查詢項目[{}]在強制向聯徵查詢的清單內，將F_BFORCEFLAG : {} --> {}", new String[] { logfile.getTxid(), logfile.getForceflag(), forceFlag });
}
msg.setItem(W2DStdMessage.F_BFORCEFLAG, forceFlag);
```

這段程式碼檢查當前查詢項目是否在強制查詢清單中，如果是，則無論前端傳入的 `forceflag` 是什麼，都會將其設為 `"Y"`。

### 3. TOJCIC 的設定

在 `JCICST00Service.java` 的 `stdCSQuery` 和 `stdCCQuery` 方法中，會從 `AuditLog` 中取得 `TOJCIC` 的值：

```java
List<AuditLog> retLogfile = auditLogDao.findToJcicByMsgIdQDateAndTxid(TODAY, MSGID, txid);
if (retLogfile != null && retLogfile.size() > 0) {
    vo.setToJcic(StringUtils.trimToEmpty(retLogfile.get(0).getTojcic()));
}
```

這表明 `TOJCIC` 的值是在查詢過程中由系統設定的，並存儲在 `AuditLog` 表中。

### 4. TOJCIC 的使用

`LogFile` 類別提供了一個靜態方法 `getToJcicString`，用於將 `TOJCIC` 的值轉換為可讀的文字：

```java
public static String getToJcicString(String tojcic) {
    String tojcicStr = "";
    if (TOJCIC_YES.equals(tojcic)) {
        tojcicStr = "聯徵中心查詢";
    } else if (TOJCIC_NO.equals(tojcic)) {
        tojcicStr = "本地資料庫查詢";
    } else {
        tojcicStr = "無";
    }
    return tojcicStr;
}
```

這個方法可能用於在用戶界面上顯示查詢的來源。

## TOJCIC 角色

在 TCBEJMNW 專案中，`TOJCIC` 還被定義為一個用戶角色：

```java
public static final String ROLE_TOJCIC = "TOJCIC";
```

這個角色可能用於控制用戶對某些功能的訪問權限，特別是與報送處理相關的功能。例如，在 `MONTJ01Handler` 類中：

```java
@Controller
@AuthRole(role = { WebUserProfile.ROLE_MONITOR, WebUserProfile.ROLE_TOJCIC })
public class MONTJ01Handler extends AbstractHandler {
    // ...
}
```

這表明只有具有 `ROLE_MONITOR` 或 `ROLE_TOJCIC` 角色的用戶才能訪問 `MONTJ01Handler` 處理的功能。

## 結論

`LOGFILE.FORCEFLAG` 和 `TOJCIC` 這兩個變數在 TCBJCICW 專案中扮演著重要的角色，它們共同決定和記錄系統是否向聯徵中心發送查詢請求。

- `FORCEFLAG` 是一個輸入參數，用於指示系統是否應該向聯徵中心發送查詢。它可以由前端設定，也可以由系統根據配置自動調整。
- `TOJCIC` 是一個輸出參數，用於記錄系統是否實際向聯徵中心發送了查詢。它由系統在查詢過程中設定，並存儲在資料庫中。

此外，`TOJCIC` 還被用作一個用戶角色，用於控制用戶對某些功能的訪問權限。

通過這種設計，系統可以靈活地控制查詢的行為，同時保留查詢的歷史記錄，提高系統的可靠性和資料的準確性。
