package mon.service.impl;

import java.util.List;

import javax.annotation.Resource;

import mon.model.MonModuleThread;
import mon.service.MQService;
import mon.service.MonModuleThreadService;
import mon.service.SysParamService;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <pre>
 * MonModuleThreadServiceImpl.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Service("monModuleThreadService")
public class MonModuleThreadServiceImpl implements MonModuleThreadService {
	@Resource
	@Qualifier("EJCICMQService")
	MQService ejcicMQService;

	@Resource
	@Qualifier("ETCHMQService")
	MQService etchMQService;

	@Resource
	SysParamService sysParamService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleThreadService#getEJCICWebThread()
	 */
	@Override
	public List<MonModuleThread> doEJCICWebThread() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleThreadService#getEJCICDispThread()
	 */
	@Override
	public List<MonModuleThread> doEJCICDispThread() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleThreadService#getEJCICGWThread()
	 */
	@Override
	public List<MonModuleThread> doEJCICGWThread() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleThreadService#getEloanDispThread()
	 */
	@Override
	public List<MonModuleThread> doEloanDispThread() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleThreadService#getETCHWebThread()
	 */
	@Override
	public List<MonModuleThread> doETCHWebThread() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleThreadService#getETCHDispThread()
	 */
	@Override
	public List<MonModuleThread> doETCHDispThread() {

		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see mon.service.impl.MonModuleThreadService#getETCHGWThread()
	 */
	@Override
	public List<MonModuleThread> doETCHGWThread() {

		return null;
	}

}
