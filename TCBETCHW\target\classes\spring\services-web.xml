<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context" xmlns:util="http://www.springframework.org/schema/util"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
           http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd ">

    <context:component-scan base-package="etch.web.service.impl" />
    <context:component-scan base-package="etch.web.render.impl" />
    <context:component-scan base-package="etch.web.handler" />

    <bean id="appWebQCF" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>${JMS_JNDI_QCF}</value>
        </property>
    </bean>

</beans>