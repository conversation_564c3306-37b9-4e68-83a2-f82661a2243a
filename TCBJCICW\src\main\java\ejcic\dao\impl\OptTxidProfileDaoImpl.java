package ejcic.dao.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import ejcic.dao.OptTxidProfileDao;
import ejcic.model.OptTxidProfile;

/**
 * <pre>
 * OpttxidprofileDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("optTxidProfileDao")
public class OptTxidProfileDaoImpl extends AbstractGenericDao<OptTxidProfile> implements OptTxidProfileDao {

	@Override
	public List<Map<String, Object>> findAll4Opt() {
		final String SQL = "select Txid,Cname,Weight,Price from OptTxidProfile WHERE TXID LIKE 'Q%'";
		return this.getJdbc().queryForList(SQL, new Object[] {});
	}

}