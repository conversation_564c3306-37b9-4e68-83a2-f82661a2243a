package etch.gw;

/**
 * <pre>
 * CacheData.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class CacheItem {

	private String rc;
	private byte[] bmsg;
	private String dataType;

	public CacheItem(String rc, String dataType, byte[] bmsg) {
		this.rc = rc;
		this.dataType = dataType;
		this.bmsg = bmsg;
	}

	/**
	 * Returns the rc
	 * 
	 * @return the rc
	 */
	public String getRc() {
		return this.rc;
	}

	/**
	 * Sets the rc
	 * 
	 * @param rc
	 *            the rc to set
	 */
	public void setRc(String rc) {
		this.rc = rc;
	}

	/**
	 * Returns the bmsg
	 * 
	 * @return the bmsg
	 */
	public byte[] getBmsg() {
		return this.bmsg;
	}

	/**
	 * Sets the bmsg
	 * 
	 * @param bmsg
	 *            the bmsg to set
	 */
	public void setBmsg(byte[] bmsg) {
		this.bmsg = bmsg;
	}

	/**
	 * Returns the dataType
	 * 
	 * @return the dataType
	 */
	public String getDataType() {
		return this.dataType;
	}

	/**
	 * Sets the dataType
	 * 
	 * @param dataType
	 *            the dataType to set
	 */
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

}
