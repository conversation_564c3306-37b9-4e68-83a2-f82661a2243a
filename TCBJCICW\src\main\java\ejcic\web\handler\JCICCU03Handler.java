package ejcic.web.handler;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;

import ejcic.exception.AppException;
import ejcic.service.GWStatusService;
import ejcic.web.core.AbstractHandler;
import ejcic.web.core.WebContext;
import ejcic.web.core.WebView;
import ejcic.web.model.WebUserProfile;

/**
 * <pre>
 * JCICCU03Handler.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
@Controller
public class JCICCU03Handler extends AbstractHandler {

	@Resource
	GWStatusService gwStatusService;

	@Override
	public WebView action(WebContext context, Map<String, String> paramMap) throws AppException {
		WebUserProfile userInfo = context.getUserProfile();
		String json = userInfo == null ? "STATUS=SESSION_TIMEOUT" : gwStatusService.getStatusJSON();
		return WebView.json().setJsonOutput(json);
	}
}
