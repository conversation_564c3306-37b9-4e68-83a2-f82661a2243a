<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd">

	<bean id="WsConnector-MQ" factory-bean="Package" factory-method="createModule">
		<!-- Constructor arguments -->
		<!-- className -->
		<constructor-arg value="ejcic.module.EjcicMQ" />
		<!-- moduleName -->
		<constructor-arg value="ejcicmq" />
		<!-- Default Properties -->
		<!-- runsOnHosts values: @ALL @SERVER HostName HostNameRegex -->
		<property name="runsOnHosts">
			<list>
				<value><![CDATA[@SERVER]]></value>
			</list>
		</property>
		<property name="runsOnServiceIds">
			<list>
				<value><![CDATA[9960]]></value>
				<value><![CDATA[9964]]></value>
			</list>
		</property>
		<property name="activatable" value="true" />
		<property name="autoStart" value="true" />
		<property name="activityInterval" value="60000" />
		<property name="authEnabled" value="true" />
		<!-- User Properties -->
		<property name="realm" value="tcb.com" />
		<property name="prefixQueueName" value="EXT." />
	</bean>

</beans>