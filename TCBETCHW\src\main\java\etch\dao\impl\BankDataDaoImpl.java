package etch.dao.impl;

import java.util.List;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import etch.dao.BankDataDao;
import etch.model.BankData;

/**
 * <pre>
 * BankdataDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("bankDataDao")
public class BankDataDaoImpl extends AbstractGenericDao<BankData> implements BankDataDao {

	@Override
	public List<BankData> findByDeptId(String deptId) {
		final String SQL = "SELECT * FROM BANKDATA WHERE BID=?  ORDER BY BID";
		return this.getJdbc().query(SQL, new Object[] { deptId, deptId },
				ParameterizedBeanPropertyRowMapper.newInstance(this.modelType));
	}

}