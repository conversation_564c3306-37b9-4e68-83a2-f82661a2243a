
# ##########################################
# \u8cc7\u6599\u5eab\u9023\u7dda\u8a2d\u5b9a
# ##########################################
MON_DB_DATASOURCE=java:comp/env/jdbc/EJMN
EJCIC_DB_DATASOURCE=java:comp/env/jdbc/EJCIC
ETCH_DB_DATASOURCE=java:comp/env/jdbc/ETCH
STJ_DB_DATASOURCE=java:comp/env/jdbc/STJ
ELOAN_DB_DATASOURCE=java:comp/env/jdbc/ELOAN
# ##########################################
# ETCH Common
# ##########################################

#\u8cc7\u6599\u5eab\u4f7f\u7528\u8005\u5bc6\u78bc\u662f\u5426\u52a0\u5bc6
SYS_PWD_ENCRYPT=Y

#\u52a0\u5bc6\u6a94\u8def\u5f91
SYS_CRYPTO_KEYFILE=


#\u932f\u8aa4\u4ee3\u78bc\u8aaa\u660e\u6a94\u8def\u5f91
MON_SYS_ERROR_FILE=/aphome/mon/conf/ejmn.error.desc.properties

# ##########################################
# EJCIC GW MQ\u8a2d\u5b9a
# ##########################################
#MQ SERVER\u7684IP\u4f4d\u5740
EJCIC_MQ_SERVER=127.0.0.1

#MQ SERVER\u7684\u9023\u63a5\u57e0
EJCIC_MQ_PORT=1414

#Queue Manager\u540d\u7a31
EJCIC_MQ_MGR=MQJ006T


#MQ\u4f3a\u670d\u5668\u9023\u7dda\u901a\u9053\u540d\u7a31
EJCIC_MQ_CHANNEL=MQJ006T.SVRCONN.EJMN

#\u8a0a\u606f\u5728\u4f47\u5217\u7684\u6709\u6548\u6642\u9593(\u79d2)
EJCIC_MQ_MSG_EXPIRY=86400

#\u6536\u53d6MQ\u903e\u6642\u6642\u9593(\u79d2)
EJCIC_MQ_TIMEOUT=200

EJCIC_MQ_WAIT_FOR_RECV=30

EJCIC_MQ_CCSID=950

EJCIC_MQ_TEST=MQJ006T.MONITOR.QL

EJCIC_MQ_MONITOR=MQJ006T.MONITOR.QL

EJCIC_MQ_MONREQ=MQJ006T.MONREQ.QL

EJCIC_MQ_MONRESP=MQJ006T.MONRESP.QL

#MQ\u4f7f\u7528\u8005ID
EJCIC_MQ_USER_ID=ejcicmq

#MQ\u4f7f\u7528\u8005pwd
EJCIC_MQ_USER_PWD=

# ##########################################
# TCH GW MQ\u8a2d\u5b9a
# ##########################################
#MQ SERVER\u7684IP\u4f4d\u5740
ETCH_MQ_SERVER=127.0.0.1

#MQ SERVER\u7684\u9023\u63a5\u57e0
ETCH_MQ_PORT=1418

#Queue Manager\u540d\u7a31
ETCH_MQ_MGR=MQT006T


#MQ\u4f3a\u670d\u5668\u9023\u7dda\u901a\u9053\u540d\u7a31
ETCH_MQ_CHANNEL=MQT006T.SVRCONN.EJMN

#\u8a0a\u606f\u5728\u4f47\u5217\u7684\u6709\u6548\u6642\u9593(\u79d2)
ETCH_MQ_MSG_EXPIRY=120

#\u6536\u53d6MQ\u903e\u6642\u6642\u9593(\u79d2)
ETCH_MQ_TIMEOUT=200

ETCH_MQ_WAIT_FOR_RECV=30

ETCH_MQ_CCSID=950

ETCH_MQ_TEST=MQT006T.MONITOR.QL

ETCH_MQ_MONITOR=MQT006T.MONITOR.QL

ETCH_MQ_MONREQ=MQT006T.MONREQ.QL

ETCH_MQ_MONRESP=MQT006T.MONRESP.QL

#MQ\u4f7f\u7528\u8005ID
ETCH_MQ_USER_ID=etchmq

#MQ\u4f7f\u7528\u8005pwd
ETCH_MQ_USER_PWD=

###########################################
#FOR WEB JMS
###########################################
#JMS QCF
JMS_JNDI_QCF_EJCIC=jms/EJCIC_QCF
#JMS QCF
JMS_JNDI_QCF_ETCH=jms/ETCH_QCF
#JMS QCF BEAN ID
JMS_JNDI_QCF_BEAN_ID=appWebQCF

###########################################
#FOR DEBUG
###########################################
#\u662f\u5426\u958b\u555f\u5075\u932f\u5de5\u5177
DEBUG_TOOLS_ENABLED=Y

#\u662f\u5426\u5141\u8a31\u865b\u64ec\u4f7f\u7528\u8005\u767b\u5165(\u6b63\u5f0f\u74b0\u5883\u8acb\u8a2d\u70baN)
DEBUG_BYPASS_SSO=Y