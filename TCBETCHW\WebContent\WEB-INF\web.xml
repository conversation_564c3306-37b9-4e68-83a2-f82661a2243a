<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:javaee="http://java.sun.com/xml/ns/javaee"
	xmlns:jsp="http://java.sun.com/xml/ns/javaee/jsp" xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	id="WebApp">
	<display-name>TCBETCHW</display-name>
	<jsp-config>
		<jsp-property-group>
			<url-pattern>*.jsp</url-pattern>
			<el-ignored>false</el-ignored>
			<page-encoding>cp950</page-encoding>
			<scripting-invalid>false</scripting-invalid>
			<include-prelude>/ETCHIN01.jspf</include-prelude>
		</jsp-property-group>
	</jsp-config>
	<context-param>
		<param-name>webAppRootKey</param-name>
		<param-value>etch.root</param-value>
	</context-param>
	<context-param>
		<param-name>log4jConfigLocation</param-name>
		<param-value>classpath:spring/log4j-web.xml</param-value>
	</context-param>
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath:spring/applicationContext-web.xml</param-value>
	</context-param>
	<filter>
		<description>增加 HTTP HEADER內 no-cache設定</description>
		<display-name>NoCacheFilter</display-name>
		<filter-name>NoCacheFilter</filter-name>
		<filter-class>etch.web.filter.NoCacheFilter</filter-class>
	</filter>
	<filter>
		<filter-name>CharacterEncodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>CP950</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter>
		<description>授權檢查</description>
		<display-name>AuthorizationFilter</display-name>
		<filter-name>AuthorizationFilter</filter-name>
		<filter-class>etch.web.filter.AuthorizationFilter</filter-class>
		<init-param>
			<description>SESSION逾時檢查(秒)</description>
			<param-name>SESSION_TIMEOUT_SEC</param-name>
			<param-value>1800</param-value>
		</init-param>
		<init-param>
			<description>登入頁面，不檢查授權</description>
			<param-name>LOGIN_PAGE</param-name>
			<param-value>/ETCHCM02.jsp</param-value>
		</init-param>
		<init-param>
			<description>授權檢查失敗頁面</description>
			<param-name>LOGIN_ERR_PAGE</param-name>
			<param-value>/ETCHER02.jsp</param-value>
		</init-param>
		<init-param>
			<description>不需檢查授權頁面</description>
			<param-name>BYPASS_PAGE</param-name>
			<param-value>/index.jsp,/check.jsp,/ETCHER02.jsp,/ETCHER03.jsp</param-value>
		</init-param>
		<init-param>
			<description>不需檢查授權的Handler</description>
			<param-name>BYPASS_HANDLER</param-name>
			<param-value>ETCHCM02Handler,ETCHCM03Handler,ETCHMO01Handler,ETCHCM05Handler</param-value>
		</init-param>
	</filter>
	<filter>
		<filter-name>CompressingFilter</filter-name>
		<filter-class>com.planetj.servlet.filter.compression.CompressingFilter</filter-class>
		<init-param>
			<param-name>debug</param-name>
			<param-value>false</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>CharacterEncodingFilter</filter-name>
		<url-pattern>*.jsp</url-pattern>
		<url-pattern>/WebController</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>AuthorizationFilter</filter-name>
		<url-pattern>*.jsp</url-pattern>
		<url-pattern>/WebController</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>NoCacheFilter</filter-name>
		<url-pattern>*.jsp</url-pattern>
		<url-pattern>/WebController</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>CompressingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<listener>
		<listener-class>
			etch.web.core.EtchLog4jConfigListener
		</listener-class>
	</listener>
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<listener>
		<listener-class>org.springframework.web.util.WebAppRootListener</listener-class>
	</listener>
	<servlet>
		<display-name>WebController</display-name>
		<servlet-name>WebController</servlet-name>
		<servlet-class>etch.web.core.DispatchServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>WebController</servlet-name>
		<url-pattern>/WebController</url-pattern>
	</servlet-mapping>

	<welcome-file-list>
		<welcome-file>index.jsp</welcome-file>
	</welcome-file-list>
	<error-page>
		<error-code>500</error-code>
		<location>/ETCHER01.jsp</location>
	</error-page>
	<resource-ref>
		<description>ETCH DataSource Reference</description>
		<res-ref-name>jdbc/ETCH</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
</web-app>