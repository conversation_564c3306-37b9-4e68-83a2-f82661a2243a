package mon.web.model;

import java.util.List;

import mon.model.MonSysUsage;

/**
 * <pre>
 * MONEJ01VO.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,NEW
 *          </ul>
 */
public class MONEJ07VO {
	private boolean ok = false;
	private String msg = null;

	// input

	private String hidY1;
	private String hidM1;
	private String hidD1;
	private String hidH1;
	private String hidMN1;
	private String hidY2;
	private String hidM2;
	private String hidD2;
	private String hidH2;
	private String hidMN2;
	private String sysType;
	private String startDate;
	private String endDate;
	private int apServerItemCount = 5;

	// output
	private List<MonSysUsage> dataList;

	public String getQueryRange() {
		return this.getStartDate() + " ~ " + this.endDate;
	}

	/**
	 * Returns the sysType
	 * 
	 * @return the sysType
	 */
	public String getSysType() {
		return this.sysType;
	}

	/**
	 * Sets the sysType
	 * 
	 * @param sysType
	 *            the sysType to set
	 */
	public void setSysType(String sysType) {
		this.sysType = sysType;
	}

	/**
	 * Returns the startDate
	 * 
	 * @return the startDate
	 */
	public String getStartDate() {
		return this.startDate;
	}

	/**
	 * Sets the startDate
	 * 
	 * @param startDate
	 *            the startDate to set
	 */
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	/**
	 * Returns the endDate
	 * 
	 * @return the endDate
	 */
	public String getEndDate() {
		return this.endDate;
	}

	/**
	 * Sets the endDate
	 * 
	 * @param endDate
	 *            the endDate to set
	 */
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	/**
	 * Returns the ok
	 * 
	 * @return the ok
	 */
	public boolean isOk() {
		return this.ok;
	}

	/**
	 * Sets the ok
	 * 
	 * @param ok
	 *            the ok to set
	 */
	public void setOk(boolean ok) {
		this.ok = ok;
	}

	/**
	 * Returns the msg
	 * 
	 * @return the msg
	 */
	public String getMsg() {
		return this.msg;
	}

	/**
	 * Sets the msg
	 * 
	 * @param msg
	 *            the msg to set
	 */
	public void setMsg(String msg) {
		this.msg = msg;
	}

	/**
	 * Returns the dataList
	 * 
	 * @return the dataList
	 */
	public List<MonSysUsage> getDataList() {
		return this.dataList;
	}

	/**
	 * Sets the dataList
	 * 
	 * @param dataList
	 *            the dataList to set
	 */
	public void setDataList(List<MonSysUsage> dataList) {
		this.dataList = dataList;
	}

	/**
	 * Returns the hidY1
	 * 
	 * @return the hidY1
	 */
	public String getHidY1() {
		return this.hidY1;
	}

	/**
	 * Sets the hidY1
	 * 
	 * @param hidY1
	 *            the hidY1 to set
	 */
	public void setHidY1(String hidY1) {
		this.hidY1 = hidY1;
	}

	/**
	 * Returns the hidM1
	 * 
	 * @return the hidM1
	 */
	public String getHidM1() {
		return this.hidM1;
	}

	/**
	 * Sets the hidM1
	 * 
	 * @param hidM1
	 *            the hidM1 to set
	 */
	public void setHidM1(String hidM1) {
		this.hidM1 = hidM1;
	}

	/**
	 * Returns the hidD1
	 * 
	 * @return the hidD1
	 */
	public String getHidD1() {
		return this.hidD1;
	}

	/**
	 * Sets the hidD1
	 * 
	 * @param hidD1
	 *            the hidD1 to set
	 */
	public void setHidD1(String hidD1) {
		this.hidD1 = hidD1;
	}

	/**
	 * Returns the hidH1
	 * 
	 * @return the hidH1
	 */
	public String getHidH1() {
		return this.hidH1;
	}

	/**
	 * Sets the hidH1
	 * 
	 * @param hidH1
	 *            the hidH1 to set
	 */
	public void setHidH1(String hidH1) {
		this.hidH1 = hidH1;
	}

	/**
	 * Returns the hidMN1
	 * 
	 * @return the hidMN1
	 */
	public String getHidMN1() {
		return this.hidMN1;
	}

	/**
	 * Sets the hidMN1
	 * 
	 * @param hidMN1
	 *            the hidMN1 to set
	 */
	public void setHidMN1(String hidMN1) {
		this.hidMN1 = hidMN1;
	}

	/**
	 * Returns the hidY2
	 * 
	 * @return the hidY2
	 */
	public String getHidY2() {
		return this.hidY2;
	}

	/**
	 * Sets the hidY2
	 * 
	 * @param hidY2
	 *            the hidY2 to set
	 */
	public void setHidY2(String hidY2) {
		this.hidY2 = hidY2;
	}

	/**
	 * Returns the hidM2
	 * 
	 * @return the hidM2
	 */
	public String getHidM2() {
		return this.hidM2;
	}

	/**
	 * Sets the hidM2
	 * 
	 * @param hidM2
	 *            the hidM2 to set
	 */
	public void setHidM2(String hidM2) {
		this.hidM2 = hidM2;
	}

	/**
	 * Returns the hidD2
	 * 
	 * @return the hidD2
	 */
	public String getHidD2() {
		return this.hidD2;
	}

	/**
	 * Sets the hidD2
	 * 
	 * @param hidD2
	 *            the hidD2 to set
	 */
	public void setHidD2(String hidD2) {
		this.hidD2 = hidD2;
	}

	/**
	 * Returns the hidH2
	 * 
	 * @return the hidH2
	 */
	public String getHidH2() {
		return this.hidH2;
	}

	/**
	 * Sets the hidH2
	 * 
	 * @param hidH2
	 *            the hidH2 to set
	 */
	public void setHidH2(String hidH2) {
		this.hidH2 = hidH2;
	}

	/**
	 * Returns the hidMN2
	 * 
	 * @return the hidMN2
	 */
	public String getHidMN2() {
		return this.hidMN2;
	}

	/**
	 * Sets the hidMN2
	 * 
	 * @param hidMN2
	 *            the hidMN2 to set
	 */
	public void setHidMN2(String hidMN2) {
		this.hidMN2 = hidMN2;
	}

	public int getAPSrvItemCount() {
		if ("ETCH".equalsIgnoreCase(this.sysType)) {
			return 4;
		} else {
			return 5;
		}
	}

	public boolean isAPSrvItem4Eloan() {
		return !("ETCH".equalsIgnoreCase(this.sysType));
	}

}
