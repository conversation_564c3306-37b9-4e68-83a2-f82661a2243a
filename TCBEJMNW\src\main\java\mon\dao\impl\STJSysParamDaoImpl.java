package mon.dao.impl;

import java.util.List;

import javax.annotation.Resource;

import mon.dao.STJSysParamDao;
import mon.model.STJSysParam;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * <pre>
 * STJSysParamDaoImpl.java
 * </pre>
 * 
 * @since 2013/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/06/30,PGU,new
 *          </ul>
 */
@Repository("stjSysParamDao")
public class STJSysParamDaoImpl implements STJSysParamDao {

	@Resource
	@Qualifier("stjJcbcTemplate")
	private JdbcTemplate jdbc;

	@Override
	public STJSysParam findByKey(STJSysParam param) {

		String sql = " select * from SYSPARAM where PARAM = ? order by PARAM asc";

		List<STJSysParam> result = jdbc.query(sql, new String[] { param.getParam() },
				ParameterizedBeanPropertyRowMapper.newInstance(STJSysParam.class));

		return (result.size() > 0) ? result.get(0) : null;
	}

	@Override
	public int updateParamValue(String param, String paramValue) {
		String sql = "UPDATE SYSPARAM SET PARAMVALUE=? WHERE PARAM=?";
		return jdbc.update(sql, new Object[] { paramValue, param });
	}

}