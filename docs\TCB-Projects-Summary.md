# TCB 專案系統總結

## 專案概述

本文檔提供了 TCB 三個主要專案 (TCBJCICW、TCBETCHW 和 TCBEJMNW) 的比較和總結。這三個專案共同構成了一個完整的金融交易和監控系統，分別負責不同的業務功能。

## 專案比較

### 功能比較

```mermaid
graph TD
    TCB[TCB 系統]
    TCB --> TCBJCICW[TCBJCICW<br>聯合徵信中心交易系統]
    TCB --> TCBETCHW[TCBETCHW<br>票據交換所交易系統]
    TCB --> TCBEJMNW[TCBEJMNW<br>系統監控平台]
    
    TCBJCICW --> JCIC1[標準查詢]
    TCBJCICW --> JCIC2[組合查詢]
    TCBJCICW --> JCIC3[授權管理]
    TCBJCICW --> JCIC4[系統參數管理]
    
    TCBETCHW --> ETCH1[票據查詢]
    TCBETCHW --> ETCH2[票據狀態管理]
    TCBETCHW --> ETCH3[授權管理]
    TCBETCHW --> ETCH4[系統參數管理]
    
    TCBEJMNW --> EJMNW1[系統資源監控]
    TCBEJMNW --> EJMNW2[模組狀態監控]
    TCBEJMNW --> EJMNW3[佇列深度監控]
    TCBEJMNW --> EJMNW4[日誌查詢]
```

### 架構比較

三個專案都採用類似的架構，基於 Spring MVC 框架，使用 JDK 1.6 開發，Maven 3.2.5 進行專案管理，Jetty 8.1.8 作為 Web 容器。

```mermaid
graph TD
    subgraph "共同架構"
        Client[客戶端瀏覽器] --> WebLayer[Web 層]
        WebLayer --> ServiceLayer[服務層]
        ServiceLayer --> DAOLayer[數據訪問層]
        DAOLayer --> DB[(資料庫)]
        ServiceLayer --> MQ[消息佇列]
    end
    
    subgraph "TCBJCICW 特有"
        MQ --> JCICDISP[JCIC Dispatcher]
        JCICDISP --> JCICGW[JCIC Gateway]
        JCICGW --> JCIC[聯合徵信中心]
    end
    
    subgraph "TCBETCHW 特有"
        MQ --> TCHDISP[TCH Dispatcher]
        TCHDISP --> TCHGW[TCH Gateway]
        TCHGW --> TCH[票據交換所]
    end
    
    subgraph "TCBEJMNW 特有"
        MQ --> MON[監控系統]
        MON --> JCICW[TCBJCICW 系統]
        MON --> ETCHW[TCBETCHW 系統]
        MON --> ELOAN[ELOAN 系統]
    end
```

### 技術比較

| 專案 | 主要功能 | 外部系統 | 特有技術 |
|------|---------|---------|---------|
| TCBJCICW | 聯合徵信中心交易 | 聯合徵信中心 | 組合查詢處理、CPX 規則引擎 |
| TCBETCHW | 票據交換所交易 | 票據交換所 | 票據狀態管理、區域碼處理 |
| TCBEJMNW | 系統監控 | 多個內部系統 | 資源監控、日誌分析 |

## 系統整合

三個專案通過以下方式進行整合：

```mermaid
graph TD
    Client[客戶端瀏覽器] --> TCBJCICW
    Client --> TCBETCHW
    Client --> TCBEJMNW
    
    TCBEJMNW --> TCBJCICW_MON[監控 TCBJCICW]
    TCBEJMNW --> TCBETCHW_MON[監控 TCBETCHW]
    
    MQ[消息佇列] --> TCBJCICW
    MQ --> TCBETCHW
    MQ --> TCBEJMNW
    
    DB[(共享資料庫)] --> TCBJCICW
    DB --> TCBETCHW
    DB --> TCBEJMNW
```

### 共享資源

1. **共享資料庫**: 三個專案可能共享某些資料表，如用戶資訊、授權資訊等
2. **消息佇列**: 使用 IBM MQ 作為消息中間件進行通訊
3. **共用程式庫**: 在 common-resources 目錄中存放共用的 JAR 文件和配置文件

## TCBJCICW 專案特點

TCBJCICW 專案主要處理與聯合徵信中心的交易，具有以下特點：

1. **標準查詢**: 提供標準的聯徵中心查詢功能
2. **組合查詢**: 可以組合多個標準查詢，提供更豐富的查詢結果
3. **授權管理**: 嚴格的授權控制，確保只有授權用戶才能執行特定交易
4. **多層架構**: 採用 Web、Dispatcher、Gateway 三層架構，確保系統的穩定性和可擴展性

## TCBETCHW 專案特點

TCBETCHW 專案主要處理與票據交換所的交易，具有以下特點：

1. **票據查詢**: 提供各種票據查詢功能
2. **票據狀態管理**: 管理票據的各種狀態
3. **區域碼處理**: 支援不同區域的票據交換所
4. **多層架構**: 與 TCBJCICW 類似，採用三層架構

## TCBEJMNW 專案特點

TCBEJMNW 專案主要提供系統監控功能，具有以下特點：

1. **系統資源監控**: 監控 CPU、記憶體等系統資源使用情況
2. **模組狀態監控**: 監控各系統模組的運行狀態
3. **佇列深度監控**: 監控消息佇列的深度
4. **日誌查詢**: 提供各系統的日誌查詢功能
5. **集中監控**: 集中監控多個系統，提供統一的監控介面

## 系統流程

### 整體業務流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant JCICW as TCBJCICW
    participant ETCHW as TCBETCHW
    participant EJMNW as TCBEJMNW
    participant External as 外部系統
    
    User->>JCICW: 聯徵查詢請求
    JCICW->>External: 轉發查詢請求
    External-->>JCICW: 返回查詢結果
    JCICW-->>User: 顯示查詢結果
    
    User->>ETCHW: 票據查詢請求
    ETCHW->>External: 轉發查詢請求
    External-->>ETCHW: 返回查詢結果
    ETCHW-->>User: 顯示查詢結果
    
    User->>EJMNW: 監控請求
    EJMNW->>JCICW: 獲取系統狀態
    EJMNW->>ETCHW: 獲取系統狀態
    JCICW-->>EJMNW: 返回系統狀態
    ETCHW-->>EJMNW: 返回系統狀態
    EJMNW-->>User: 顯示監控結果
```

## 部署架構

```mermaid
graph TD
    subgraph "前端伺服器"
        JCICW_WEB[TCBJCICW Web]
        ETCHW_WEB[TCBETCHW Web]
        EJMNW_WEB[TCBEJMNW Web]
    end
    
    subgraph "中間層伺服器"
        JCICW_DISP[TCBJCICW Dispatcher]
        ETCHW_DISP[TCBETCHW Dispatcher]
        MQ[IBM MQ]
    end
    
    subgraph "後端伺服器"
        JCICW_GW[TCBJCICW Gateway]
        ETCHW_GW[TCBETCHW Gateway]
        DB[資料庫]
    end
    
    subgraph "外部系統"
        JCIC[聯合徵信中心]
        TCH[票據交換所]
    end
    
    JCICW_WEB --> JCICW_DISP
    ETCHW_WEB --> ETCHW_DISP
    EJMNW_WEB --> MQ
    
    JCICW_DISP --> MQ
    ETCHW_DISP --> MQ
    
    MQ --> JCICW_GW
    MQ --> ETCHW_GW
    
    JCICW_GW --> JCIC
    ETCHW_GW --> TCH
    
    JCICW_WEB --> DB
    ETCHW_WEB --> DB
    EJMNW_WEB --> DB
```

## 結論

TCB 的三個專案 (TCBJCICW、TCBETCHW 和 TCBEJMNW) 共同構成了一個完整的金融交易和監控系統。它們採用類似的架構和技術，但各自負責不同的業務功能。TCBJCICW 負責與聯合徵信中心的交易，TCBETCHW 負責與票據交換所的交易，而 TCBEJMNW 則負責監控這些系統的運行狀態。

這三個專案通過共享資源和消息佇列進行整合，形成了一個功能完整、架構清晰的系統。通過本文檔，您可以了解這三個專案的架構、功能和業務流程，以便更好地使用和維護這些系統。
