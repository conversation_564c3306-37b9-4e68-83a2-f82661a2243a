# TCBJCICW 系統資料處理流程分析

## 系統概述

TCBJCICW 是一個與聯合徵信中心進行通訊的系統，主要負責處理信用查詢請求。系統分為三個主要部分：Web 前端、Dispatch 服務和 Gateway 服務。

## 系統流程圖

```mermaid
flowchart TD
    A[Web 前端] -->|1.發起查詢| B[組建電文]
    B -->|2.發送| C[Web Queue]
    C -->|3.讀取| D[Dispatch 服務]
    D -->|4.處理| E{是否發查聯徵?}
    E -->|是| F[發送至 Gateway]
    E -->|否| G[直接回傳資料庫資料]
    F -->|5.讀取| H[Gateway 服務]
    H -->|6.發送| I[聯徵中心]
    I -->|7.回傳| J[Gateway Queue]
    J -->|8.讀取| K[Gateway 服務]
    K -->|9.處理| L[Dispatch Queue]
    L -->|10.讀取| M[Dispatch 服務]
    M -->|11.處理| N[Web Queue]
    N -->|12.讀取| O[Web 前端]
    G -->|直接回傳| N
```

## 詳細流程說明

### 1. Web 前端發起查詢

用戶在 Web 前端發起查詢請求，系統通過 `JCICST00Service.stdCSQuery` 方法處理標準查詢請求。

### 2. 組建電文並發送到 MQ

Web 前端通過 `JCICST00Service` 類中的 `getW2DStdMessage` 方法組建電文，並通過 `mqService.sendWeb2Disp` 方法將電文發送到 MQ 中的 Web Queue。

```java
// 判斷查詢項目是否強制向聯徵查詢
String forceFlag = logfile.getForceflag();
String[] forceToJcic = StringUtils.split(StringUtils.trimToEmpty(this.sysParamService.getProperty(SysParamConst.STDTXID_FORCE_TOJCIC)), ",");
if (ArrayUtils.contains(forceToJcic, logfile.getTxid())) {
    forceFlag = "Y";
    logger.warn("查詢項目[{}]在強制向聯徵查詢的清單內，將F_BFORCEFLAG : {} --> {}", new String[] { logfile.getTxid(), logfile.getForceflag(), forceFlag });
}
msg.setItem(W2DStdMessage.F_BFORCEFLAG, forceFlag);
```

### 3. Dispatch 服務讀取電文

Dispatch 服務通過 `mqService.receiveDisp2Web` 方法從 Web Queue 中讀取電文。

### 4. Dispatch 服務處理電文

Dispatch 服務（`WebDispatcher`）根據電文類型將其分配給不同的處理流程：

```java
if (this.isCpxMsg(htxid)) {
    // 組合查詢
    cpxflow.process(bmsg);
} else {
    // 標準查詢
    stdflow.setMQMessage((Message) messageObject);
    stdflow.process(bmsg);
}
```

### 5. 判斷是否發查聯徵

在 `WebStdFlow.process` 方法中，系統根據以下條件判斷是否需要發查聯徵中心：

```java
// 判斷是否送聯徵
final String forceFlag = w2dMsg.getItem(W2DStdMessage.F_BFORCEFLAG);
// 資料庫查詢
boolean isQueryInDB = Message.FORCEFLAG_NO.equalsIgnoreCase(forceFlag);
boolean isForce = Message.FORCEFLAG_YES.equals(forceFlag);
boolean isNotQueryed = true;
if (!isForce) {
    // 檢查H類查詢記錄
    logfileQDate = this.logFileDao.findStdQDateByQueryed(logfile.getTxid(), logfile.getQuerykey1(), logfile.getQuerykey2(),
            logfile.getIsprint());
    logger.info("{} LOGFILE查詢結果: {} ", logTitle, logfileQDate);
    // 是否未查詢過
    logfileQDate = StringUtils.trimToEmpty(logfileQDate);
    isNotQueryed = StringUtils.isEmpty(logfileQDate);
}
// 97.02.15新增強迫送出功能
if (isForce || isNotQueryed) {
    toJcic = "Y";
} else {
    // 檢查查詢記錄是否在有效期內
    // ...
    toJcic = (!inDB) ? "Y" : "N";
}
```

### 6. 發送電文到 Gateway 或直接回傳資料庫資料

如果需要發查聯徵中心（`toJcic = "Y"`），則通過 `sendToGW` 方法將電文發送到 Gateway：

```java
if (toJcic.equals("Y")) {
    // 送H類查詢至聯徵
    this.sendToGW(logfile);
} else {
    // 直接回傳資料庫資料
    this.sendToWeb(logfile, ReturnCodeService.RC_SUCCESS, "", logfileQDate, true);
}
```

在 `sendToGW` 方法中，使用 `mqService.sendDisp2Gw` 將電文發送到 Gateway Queue：

```java
if (this.mqService.sendDisp2Gw(this.msgid.getBytes(), this.msg.getBytes())) {
    logger.info(LOG_TITLE + "[" + this.msgid.getBytes() + "]訊息送至JCIC成功！");
}
```

### 7. Gateway 服務處理電文並發送到聯徵中心

Gateway 服務從 Gateway Queue 中讀取電文，處理後發送到聯徵中心。

### 8. 聯徵中心回傳結果

聯徵中心處理查詢請求後，將結果回傳給 Gateway 服務。

### 9. Gateway 服務處理回傳結果

Gateway 服務處理聯徵中心回傳的結果，並將其發送到 Dispatch Queue。

### 10. Dispatch 服務處理回傳結果

Dispatch 服務從 Dispatch Queue 中讀取回傳結果，處理後發送到 Web Queue。

### 11. Web 前端讀取回傳結果

Web 前端從 Web Queue 中讀取回傳結果，並顯示給用戶。

## 關鍵參數說明

### 1. FORCEFLAG 參數

`FORCEFLAG` 參數決定是否強制發查聯徵中心或直接從資料庫取得資料：

- `"A"`：自動判斷（根據資料庫中的資料是否在有效期內決定）
- `"Y"`：強制發查聯徵中心
- `"N"`：強制從資料庫取得資料

### 2. TOJCIC 參數

`TOJCIC` 參數表示是否實際發送查詢到聯徵中心：

- `"Y"`：已發送查詢到聯徵中心
- `"N"`：未發送查詢到聯徵中心（直接從資料庫取得資料）

### 3. LogFile.rc 參數

`LogFile.rc` 參數表示查詢結果的狀態碼：

- `"0000"`：查詢成功
- `"0001"`：處理中
- `"8888"`：特殊分行代碼，不發送查詢
- 其他值：查詢失敗，對應不同的錯誤原因

## 特殊分行處理邏輯

當 `LogFile.division` 或 `LogFile.chargeid` 為特定分行代碼（例如 9999）時，系統應該：

1. 不發送查詢到聯徵中心
2. 將 `LogFile.rc` 設為 8888
3. 將 `AUDITLOG.TOJCIC` 設為 "N"

這個邏輯需要在 `JCICST00Service.java` 的 `getW2DStdMessage` 方法中實現，在判斷是否強制向聯徵查詢的邏輯之後添加對 `division` 和 `chargeid` 的檢查。

## 系統組件關係圖

```mermaid
classDiagram
    class WebFrontend {
        +stdCSQuery()
        +stdCCQuery()
        +stdCTQuery()
    }
    
    class JCICST00Service {
        +stdCSQuery()
        +getW2DStdMessage()
        +mqService.sendWeb2Disp()
        +mqService.receiveDisp2Web()
    }
    
    class WebDispatcher {
        +dispatch()
        +process()
    }
    
    class WebStdFlow {
        +process()
        +sendToGW()
        +sendToWeb()
    }
    
    class WebCpxFlow {
        +process()
        +processQxxx()
    }
    
    class MQService {
        +sendWeb2Disp()
        +receiveDisp2Web()
        +sendDisp2Gw()
        +sendDisp2Web()
    }
    
    WebFrontend --> JCICST00Service
    JCICST00Service --> MQService
    WebDispatcher --> WebStdFlow
    WebDispatcher --> WebCpxFlow
    WebStdFlow --> MQService
    WebCpxFlow --> MQService
```

## 結論

TCBJCICW 系統通過 MQ 實現了 Web 前端、Dispatch 服務和 Gateway 服務之間的通訊，形成了一個完整的查詢處理流程。系統根據 `FORCEFLAG` 參數和資料庫中的查詢記錄決定是否發查聯徵中心，並通過 `TOJCIC` 參數記錄查詢是否實際發送到聯徵中心。

對於特殊分行代碼的處理，需要在 `JCICST00Service.java` 的 `getW2DStdMessage` 方法中添加相應的邏輯，確保系統不發送查詢到聯徵中心，並將 `LogFile.rc` 設為 8888。
