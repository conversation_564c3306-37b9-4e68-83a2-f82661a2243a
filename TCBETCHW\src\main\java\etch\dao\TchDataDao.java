package etch.dao;

import etch.model.TchData;

/**
 * <pre>
 * TchDataDao.java
 * </pre>
 * 
 * @since 2003/06/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2003/06/30,PGU,new
 *          </ul>
 */
public interface TchDataDao extends GenericDao<TchData> {

	public int deleteAndInsert(TchData model);

	public String findQDateByQueried(TchData model);

	public TchData findDataByDatatype(TchData model);

}
